GEM
  remote: https://gems.ruby-china.com/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    acts_as_list (1.1.0)
      activerecord (>= 4.2)
    addressable (2.8.4)
      public_suffix (>= 2.0.2, < 6.0)
    airbrussh (1.4.1)
      sshkit (>= 1.6.1, != 1.7.0)
    aliyun-cloud_sms (0.2.3)
      rest-client (>= 2.0)
      uuid (>= 2.0)
    aliyun-sdk (0.8.0)
      nokogiri (~> 1.6)
      rest-client (~> 2.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    api-pagination (5.0.0)
    ast (2.4.2)
    audited (5.3.3)
      activerecord (>= 5.0, < 7.1)
      request_store (~> 1.2)
    bcrypt (3.1.18)
    bindex (0.8.1)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    capistrano (3.17.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (1.3.0)
      capistrano (~> 3.1)
      sshkit (~> 1.2)
    capistrano-rails (1.3.1)
      capistrano (~> 3.1)
      capistrano-bundler (~> 1.1)
    capistrano-sidekiq (2.3.1)
      capistrano (>= 3.9.0)
      capistrano-bundler
      sidekiq (>= 6.0)
    capistrano3-puma (3.1.1)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (~> 3.4)
    capybara (3.39.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    carrierwave (1.3.3)
      activemodel (>= 4.0.0)
      activesupport (>= 4.0.0)
      mime-types (>= 1.16)
      ssrf_filter (~> 1.0, < 1.1.0)
    carrierwave-aliyun (1.2.3)
      aliyun-sdk (>= 0.7.0)
      carrierwave (>= 1)
    caxlsx (3.4.1)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.3)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    chronic (0.10.2)
    cocoon (1.2.15)
    concurrent-ruby (1.2.2)
    config (4.1.0)
      deep_merge (~> 1.2, >= 1.2.1)
      dry-validation (~> 1.0, >= 1.0.0)
    connection_pool (2.4.1)
    crass (1.0.6)
    date (3.3.3)
    debug (1.7.2)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    deep_merge (1.2.2)
    devise (4.9.2)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dry-configurable (1.0.1)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.0)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    dry-inflector (1.0.0)
    dry-initializer (3.1.1)
    dry-logic (1.5.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-schema (1.13.1)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-logic (>= 1.4, < 2)
      dry-types (>= 1.7, < 2)
      zeitwerk (~> 2.6)
    dry-types (1.7.1)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    dry-validation (1.10.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-schema (>= 1.12, < 2)
      zeitwerk (~> 2.6)
    enum_help (0.0.19)
      activesupport (>= 3.0.0)
    erubi (1.12.0)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faraday (2.7.10)
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-net_http (3.0.2)
    ffi (1.15.5)
    geocoder (1.8.2)
    globalid (1.1.0)
      activesupport (>= 5.0)
    grape (1.7.0)
      activesupport
      builder
      dry-types (>= 1.1)
      mustermann-grape (~> 1.0.0)
      rack (>= 1.3.0)
      rack-accept
    grape-entity (1.0.0)
      activesupport (>= 3.0.0)
      multi_json (>= 1.3.2)
    grape-swagger (1.6.0)
      grape (~> 1.3)
    grape-swagger-entity (0.5.1)
      grape-entity (>= 0.6.0)
      grape-swagger (>= 1.2.0)
    grape-swagger-rails (0.4.0)
      railties (>= 6.0.6.1)
    grape_logging (1.8.4)
      grape
      rack
    grape_on_rails_routes (0.3.2)
      rails (>= 3.1.1)
    hashie (5.0.0)
    hashie-forbidden_attributes (0.1.1)
      hashie (>= 3.0)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    importmap-rails (1.1.5)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.6.0)
    irb (1.6.4)
      reline (>= 0.3.0)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jpush (4.0.11)
    jquery-rails (4.5.1)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-ui-rails (6.0.1)
      railties (>= 3.2.16)
    json (2.6.3)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (3.17.0.3)
    laydate-rails (0.2.1)
    lint_roller (1.0.0)
    loofah (2.20.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    macaddr (1.7.2)
      systemu (~> 2.6.5)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    matrix (0.4.2)
    method_source (1.0.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.0218.1)
    mini_magick (4.12.0)
    mini_mime (1.1.2)
    minitest (5.18.0)
    mqtt (0.6.0)
    msgpack (1.7.0)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    mustermann (3.0.0)
      ruby2_keywords (~> 0.0.1)
    mustermann-grape (1.0.2)
      mustermann (>= 1.0.0)
    mysql2 (0.5.6)
    net-imap (0.3.4)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-smtp (0.3.3)
      net-protocol
    net-ssh (7.1.0)
    netrc (0.11.0)
    nio4r (2.5.9)
    nokogiri (1.14.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.14.2-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.14.2-x86_64-linux)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    pagy (6.0.3)
    parallel (1.22.1)
    parser (3.2.2.0)
      ast (~> 2.4.1)
    pingpp (2.2.4)
      rest-client (>= 1.4, < 4.0)
    public_suffix (5.0.1)
    puma (3.11.2)
    pundit (2.3.0)
      activesupport (>= 3.0.0)
    racc (1.6.2)
    rack (2.2.6.4)
    rack-accept (0.4.5)
      rack (>= 0.4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-admin-scaffold (0.1.0)
      rails (>= 4.0)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.0.6)
    ransack (4.0.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    redis (5.1.0)
      redis-client (>= 0.17.0)
    redis-client (0.18.0)
      connection_pool
    regexp_parser (2.7.0)
    reline (0.3.3)
      io-console (~> 0.5)
    request_store (1.5.1)
      rack (>= 1.4)
    responders (3.1.0)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.2.6)
    roo (2.10.0)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rspec-core (3.12.2)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (6.0.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.11)
      rspec-expectations (~> 3.11)
      rspec-mocks (~> 3.11)
      rspec-support (~> 3.11)
    rspec-support (3.12.0)
    rubocop (1.50.2)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.28.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.28.0)
      parser (>= *******)
    rubocop-performance (1.16.0)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    ruby-ole (********)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    selenium-webdriver (4.8.6)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sidekiq (7.2.0)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.14.0)
    sidekiq-failures (1.0.4)
      sidekiq (>= 4.0.0)
    simple_command (1.0.1)
    sorbet (0.5.11139)
      sorbet-static (= 0.5.11139)
    sorbet-static (0.5.11139-universal-darwin)
    sorbet-static (0.5.11139-x86_64-linux)
    spreadsheet (1.3.0)
      ruby-ole
    sprockets (4.2.0)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sshkit (1.21.4)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    ssrf_filter (1.0.8)
    standard (1.28.0)
      language_server-protocol (~> ********)
      lint_roller (~> 1.0)
      rubocop (~> 1.50.2)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.0.1)
    standard-custom (1.0.0)
      lint_roller (~> 1.0)
    standard-performance (1.0.1)
      lint_roller (~> 1.0)
      rubocop-performance (~> 1.16.0)
    stimulus-rails (1.2.1)
      railties (>= 6.0.0)
    systemu (2.6.5)
    test-prof (1.2.1)
    thor (1.2.1)
    tilt (2.1.0)
    timeout (0.3.2)
    turbo-rails (1.4.0)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (2.4.2)
    uuid (2.3.9)
      macaddr (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.2.0)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0)
    websocket (1.2.9)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.7)

PLATFORMS
  arm64-darwin-22
  arm64-darwin-23
  arm64-darwin-24
  x86_64-darwin-21
  x86_64-linux

DEPENDENCIES
  acts_as_list
  aliyun-cloud_sms
  annotate
  api-pagination
  audited
  bootsnap
  capistrano-rails
  capistrano-sidekiq (~> 2.3, >= 2.3.1)
  capistrano3-puma
  capybara
  carrierwave (~> 1.0)
  carrierwave-aliyun
  caxlsx_rails
  cocoon
  config
  debug
  devise
  enum_help
  factory_bot_rails
  faraday (~> 2.7, >= 2.7.10)
  geocoder
  grape
  grape-entity
  grape-swagger
  grape-swagger-entity
  grape-swagger-rails
  grape_logging
  grape_on_rails_routes
  hashie-forbidden_attributes
  httparty
  importmap-rails
  jbuilder
  jpush (~> 4.0, >= 4.0.11)
  jquery-rails
  jquery-ui-rails
  kaminari
  laydate-rails
  mini_magick
  mqtt
  mysql2 (~> 0.5)
  pagy (~> 6.0, >= 6.0.3)
  pingpp
  puma (~> 3.7)
  pundit
  rack-cors
  rails (~> 7.0.4, >= *******)
  rails-admin-scaffold
  ransack
  redis
  rest-client (~> 2.0, >= 2.0.2)
  rexml (~> 3.2, >= 3.2.6)
  roo (~> 2.7, >= 2.7.1)
  rspec-rails
  sassc-rails
  selenium-webdriver
  sidekiq
  sidekiq-failures
  simple_command
  sorbet (~> 0.5.11139)
  spreadsheet (~> 1.1, >= 1.1.4)
  sprockets-rails
  standard (~> 1.28)
  stimulus-rails
  test-prof
  turbo-rails
  tzinfo-data
  web-console
  webdrivers
  whenever

BUNDLED WITH
   2.4.10
