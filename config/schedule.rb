# Use this file to easily define all of your cron jobs.
#
# It's helpful, but not entirely necessary to understand cron before proceeding.
# http://en.wikipedia.org/wiki/Cron

# Example:
#
# set :output, "/path/to/my/cron_log.log"
#
#
set :output, "log/cron.log"

every 1.day, at: "04:00 am" do
  rake "db:backup"
end

every 1.day, at: "03:00 am" do
  rake "opinion_auto_finish"
end

every 25.day do
  rake "update_mini_program_url_link"
end

every 10.minutes do
  rake "wangdian_sync_logistics"
end

every 1.day, at: '1:00 am' do
  rake "stats:max_online_per_day"
end

#
# every 4.days do
#   runner "AnotherModel.prune_old_records"
# end

# Learn more: http://github.com/javan/whenever
