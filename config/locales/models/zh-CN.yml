zh-CN:
  activerecord:
    models:
      product: 商品
    attributes:
      product:
        aftermarket: 售后分享图
        before_image: 售前分享图
        detail: 商品详情
        name: 商品标题
        list_img_url: 商品白底图
        deposit: 定金
        sku:
          sku_markedprice: 原价
          sku_price: 现价
      sku:
        sku_markedprice: 原价
        sku_price: 现价

  enums:
    opinion:
      status:
        pending: 待处理
        processing: 处理中
        completed: 已完成
        canceled: 已取消
      option_type:
        software: APP建议
        logistics: 订单相关
        product: 产品相关
        returns: 售后支持
        other: 其他
        instruction: 说明
        cancel_instruction: 取消说明
    notification:
      notify_type:
        reply_comment: 回复了你的评论
        comment_star: 点赞了你的评论
        reply_opinion: 回复了你的反馈
    payment:
      status:
        unpaid: 未支付
        paid: 已支付
        refund: 已退款
      pay_method:
        alipay: 支付宝
        wxpay: 微信
    push_message:
      status:
        pending: 待发送
        pushing: 发送中
        pushed: 已完成
        revoked: 已撤销
      template_id:
        product_start_sale_notify: 商品开售通知
        product_pre_sale_notify: 商品预售通知
        custom_notify: 自定义内容
      timing:
        immediately: 立即推送
        definite_time: 定时推送