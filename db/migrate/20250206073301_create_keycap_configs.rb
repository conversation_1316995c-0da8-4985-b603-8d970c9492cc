class CreateKeycapConfigs < ActiveRecord::Migration[7.0]
  def change
    create_table :keycap_configs do |t|
      t.integer :keycap_id
      t.integer :career_preset_id
      t.boolean :enable_rt_mode
      t.float :trigger_point
      t.float :press_trigger_point
      t.float :release_trigger_point

      t.timestamps
    end
    add_index :keycap_configs, :keycap_id
    add_index :keycap_configs, :career_preset_id
  end
end
