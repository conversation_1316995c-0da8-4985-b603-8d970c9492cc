class CreateComments < ActiveRecord::Migration[7.0]
  def change
    create_table :comments do |t|
      t.integer :product_id
      t.integer :sku_id
      t.integer :user_id
      t.string :user_nickname
      t.boolean :is_buy,default: true
      t.string :comment
      t.integer :userlike_count,default: 0
      t.integer :parent_id
      t.integer :comments_count, default: 0
      t.integer :que

      t.timestamps
    end
  end
end
