class CreateUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :users do |t|
      t.string :user_nikename
      t.string :avatar
      t.string :phone
      t.string :openid
      t.string :wx_name
      t.boolean :status,default: false
      t.integer :option_count,default: 0
      t.integer :user_login_time,default: 0
      t.string :user_system,null: false,default: ""
      t.string :user_version,null: false, default: ""
      t.integer :comments_count,default: 0
      t.integer :orders_count,default: 0
      t.integer :subscriptions_count,default: 0
      t.integer :assembles_count,default: 0
      t.float :amounts_count,default: 0
      t.datetime :login_time

      t.timestamps
    end
  end
end
