class CreateProducts < ActiveRecord::Migration[7.0]
  def change
    create_table :products do |t|
      t.string :name,null: false,default: ""
      t.string :title
      t.string :button_txt
      t.string :before_image
      t.string :share_img_url
      t.string :list_img_url
      t.text :detail
      t.integer :sale_count,default: 0
      t.boolean :is_sale,default: false
      t.boolean :on_shelf,default: false
      t.boolean :on_sale,default: false
      t.boolean :is_comment,default: false
      t.boolean :is_open_comment,default: false
      t.integer :tag_id
      t.integer :que,default: 0

      t.timestamps
    end


  end
end
