class CreatePushMessages < ActiveRecord::Migration[7.0]
  def change
    create_table :push_messages do |t|
      t.string :title
      t.boolean :include_sms_message
      t.boolean :include_jpush
      t.integer :template_id
      t.integer :product_id
      t.text :template_content
      t.string :msg_id
      t.datetime :pushed_at
      t.json :crowd
      t.integer :expected_push_count
      t.integer :actual_push_count
      t.integer :timing
      t.integer :status

      t.timestamps
    end
    add_index :push_messages, :status
  end
end
