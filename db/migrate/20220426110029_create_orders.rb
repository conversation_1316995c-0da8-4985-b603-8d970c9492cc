class CreateOrders < ActiveRecord::Migration[7.0]
  def change
    create_table :orders do |t|
      t.string :number
      t.integer :count
      t.float :amount
      t.integer :user_id
      t.string :user_nickname
      t.string :phone
      t.datetime :pay_time
      t.datetime :delivery_time
      t.datetime :send_time
      t.datetime :refund_time
      t.datetime :charge_time
      t.string :charge_number
      t.string :pay_type
      t.integer :receive_id
      t.string :receive_name
      t.string :receive_phone
      t.string :receive_address
      t.string :logistics_com
      t.string :logistics_number
      t.string :logistics_desp
      t.string :user_node
      t.string :admin_node
      t.integer :status

      t.timestamps
    end
  end
end
