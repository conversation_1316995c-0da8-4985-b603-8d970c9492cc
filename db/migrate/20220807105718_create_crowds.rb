class CreateCrowds < ActiveRecord::Migration[7.0]
  def change
    create_table :crowds do |t|
      t.integer :product_id
      t.integer :sku_id
      t.string :name
      t.string :image
      t.boolean :is_time_come,default: false
      t.datetime :start_time
      t.datetime :end_time
      t.integer :end_status,default: 0
      t.integer :status,default: 0
      t.integer :que,default: 0
      t.integer :count,default: 0
      t.integer :subscriptions_count,default: 0

      t.timestamps
    end
  end
end
