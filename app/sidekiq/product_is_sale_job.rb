# frozen_string_literal: true

# 商品定时出售
class ProductIsSaleJob
  include Sidekiq::Job
  sidekiq_options :failures => true

  def perform(product_id, sale_time)
    product = Product.find(product_id)
    if product.is_sale_at_button? && product.is_sale_at.to_s == sale_time
      product.update!(is_sale: true, is_sale_at: nil, is_sale_at_button: false)
      p "#{product.name}-#{product.id} 已上架"
      begin
        Settings.feishu.receive_ids.split(",").each do |receive_id|
          Feishu.send_product_is_sale_notice(receive_id, product, sale_time)
        end
      rescue => e
        p "Feishu.send_product_is_sale_notice(#{product.name}-#{product.id}) 发送失败: #{e}"
      end
    else
      p "#{product.name}-#{product.id} 上架时间#{product.is_sale_at}与#{sale_time}不一致"
    end
  end
end
