# frozen_string_literal: true

# 商品定时出售
class PushMessageJob
  include Sidekiq::Job
  sidekiq_options failures: true

  def perform(push_message_id, pushed_at)
    push_message = PushMessage.find(push_message_id)
    return if push_message.status != 'pending'

    if push_message.immediately?
      push_message_content(push_message_id)
    else
      push_message_content(push_message_id) if push_message.pushed_at.to_s == pushed_at.to_s
    end
  end

  def push_message_content(push_message_id)
    push_message = PushMessage.find(push_message_id)
    if push_message.custom_notify?
      push_message.update(status: :pushing)
      push_message.registration_ids.each_slice(1000) do |group_registration_ids|
        Jiguang.send_message(group_registration_ids, push_message.template_content, push_message.title)
      end
      push_message.update(status: :pushed)
    else
      push_message.update(status: :pushing)
      if push_message.include_jpush?
        p '发送极光'
        push_message.registration_ids.each_slice(1000) do |group_registration_ids|
          Jiguang.send_message(group_registration_ids, push_message.template_content, push_message.title)
        end
      end

      if push_message.include_sms_message?
        p '============'
        p '发送短信'
        # data = JSON.parse(response.body)
        # Rails.logger.error '-----------loginSendSms------------'
        # Rails.logger.error data
        # if data['Code'] != 'OK'
        #   WarningNoticeJob.perform_later(params[:mobile], data["Message"])
        #   return { msg: '出错了', code: 500, data: {} }
        # end
        product = push_message.product
        p product
        if product.present?
          short_url = ShortUrl.find_or_create_by(redirect_to: "https://m.iqunix.com/web/products/#{product.share_id}")
          if push_message.product_start_sale_notify?
            if push_message.crowd.include?(PushMessage::ALL_PEOPLE)
              push_message.phones[:all_user_phones].each do |phone|
                response = Aliyun::CloudSms.send_msg(phone, 'SMS_464055164', { product: product.name, code: short_url.uniq_key })
              end
            else
              push_message.phones[:sub_user_phones].each do |phone|
                response = Aliyun::CloudSms.send_msg(phone, 'SMS_464125186', { product: product.name, code: short_url.uniq_key })
              end
              push_message.phones[:collect_user_phones].each do |phone|
                response = Aliyun::CloudSms.send_msg(phone, 'SMS_464055164', { product: product.name, code: short_url.uniq_key })
              end
            end
          elsif push_message.product_pre_sale_notify?
            if push_message.crowd.include?(PushMessage::ALL_PEOPLE)
              push_message.phones[:all_user_phones].each do |phone|
                response = Aliyun::CloudSms.send_msg(phone, 'SMS_464075187', { product: product.name, code: short_url.uniq_key })
              end
            else
              push_message.phones[:sub_user_phones].each do |phone|
                response = Aliyun::CloudSms.send_msg(phone, 'SMS_463910446', { product: product.name, code: short_url.uniq_key })
              end
              push_message.phones[:collect_user_phones].each do |phone|
                response = Aliyun::CloudSms.send_msg(phone, 'SMS_464075187', { product: product.name, code: short_url.uniq_key })
              end
            end
          end
        end
      end
      push_message.update(status: :pushed)
    end
  end
end
