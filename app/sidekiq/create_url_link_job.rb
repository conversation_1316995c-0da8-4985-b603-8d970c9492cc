class CreateUrlLinkJob
  include Sidekiq::Job
  sidekiq_options :failures => true

  def perform(product_id)
    product = Product.find_by(id: product_id)
    # 获取UrlLink
    url = "https://api.weixin.qq.com/wxa/generatescheme?access_token=#{mini_program_token}"
    response = Faraday.post(url, { jump_wxa: { path: "pages/product/show", query: "product_id=#{product_id}" } }.to_json)
    url_link = JSON.parse(response.body)['openlink']
    product.update_columns(url_link: url_link)
  end

  def mini_program_token
    Rails.cache.fetch("mini_program_token", expires_in: 1.hour) do
      response = Faraday.get("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=#{Settings.mini_program.appid}&secret=#{Settings.mini_program.app_secret}")
      result = JSON.parse(response.body)
      Rails.logger.info '----------wechat_mini_program----access_token--------'
      Rails.logger.info result['access_token']
      result['access_token']
    end
  end
end
