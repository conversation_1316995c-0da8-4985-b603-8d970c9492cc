# frozen_string_literal: true

# 预售相关通知
class PresaleOrderNotificationJob
  include Sidekiq::Job
  sidekiq_options :failures => true

  def perform(order_id, status)
    order = Order.find_by_id(order_id)
    order_detail = order.order_details.first
    product_name = order_detail.product_name
    notice_type = ""
    case status
    when 'paid_deposit'
      p notice_type = "用户付定金后发送付定金短信"
      response = Aliyun::CloudSms.send_msg(order.user.phone, 'SMS_463597870', { product: product_name })
    when 'change_pay_all'
      p notice_type = "商品转为付尾款状态后向用户发送付尾款短信"
      short_url = ShortUrl.find_or_create_by(redirect_to: "https://m.iqunix.com/web/products/#{order_detail.product.share_id}")
      response = Aliyun::CloudSms.send_msg(order.user.phone, 'SMS_463657903', { product: product_name, code: short_url.uniq_key })
    when 'no_pay_all_over_72_hours'
      # 72小时内未付尾款
      p notice_type = "72小时内未付尾款后向用户发送付尾款短信"
      if order.status == 2000
        short_url = ShortUrl.find_or_create_by(redirect_to: "https://m.iqunix.com/web/products/#{order_detail.product.share_id}")
        response = Aliyun::CloudSms.send_msg(order.user.phone, 'SMS_463587969', { product: product_name, code: short_url.uniq_key })
      end
    when 'initiative_deposit_refunded'
      # 用户付定订单退款成功后
      p notice_type = "主动退款用户付定订单后发送退款短信"
      response = Aliyun::CloudSms.send_msg(order.user.phone, 'SMS_463805622', { order_id: order.number, order_amount_total: order_detail.skuprice.to_i })
    when 'deposit_refunded'
      # 用户付定订单退款成功后
      p notice_type = "用户付定订单退款成功后发送退款短信"
      response = Aliyun::CloudSms.send_msg(order.user.phone, 'SMS_463790112', { order_id: order.number, order_amount_total: order_detail.skuprice.to_i })
    end

    data = JSON.parse(response.body)
    if data['Code'] != 'OK'
      WarningNoticeJob.perform_later(order.user.phone, data["Message"])
      p "#{order.number}-#{order.user.phone}-#{Time.current}-#{product_name}-#{notice_type}发送失败"
    else
      p "#{order.number}-#{order.user.phone}-#{Time.current}-#{product_name}-#{notice_type}发送成功"
    end
  end
end
