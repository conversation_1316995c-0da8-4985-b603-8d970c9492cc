class CreateQrcodeJob
  include Sidekiq::Job
  sidekiq_options :failures => true

  def perform(product_id)
    url = "https://api.weixin.qq.com/wxa/getwxacode?access_token=#{mini_program_token}"
    response = Faraday.post(url, { path: "pages/product/show?product_id=#{product_id}" }.to_json)
    product = Product.find(product_id)
    file = Tempfile.new(['temp', "#{SecureRandom.hex(16).to_s}.png"])
    file.binmode
    file.write response.body
    Rails.logger.info file.path
    uploader = FileUploader.new
    uploader.store!(file)
    product.update_columns(qrcode: uploader.url)
  end

  def mini_program_token
    Rails.cache.fetch("mini_program_token", expires_in: 1.hour) do
      response = Faraday.get("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=#{Settings.mini_program.appid}&secret=#{Settings.mini_program.app_secret}")
      result = JSON.parse(response.body)
      Rails.logger.info '----------wechat_mini_program----access_token--------'
      Rails.logger.info result['access_token']
      result['access_token']
    end
  end
end
