class AppVersions < Base
  namespace :react do
    resources :app do
      desc "版本"
      params do
        optional :ver_type, type: Integer, values: [0, 1, 2], desc: '版本类型{"0"=>"暂无" , "1"=>"安卓" , "2"=>"IOS"}'
      end
      get "version" do
        version = AppVersion.where(ver_type: params[:ver_type]).first
        return {msg: "App 最新版本", code: 200, data: {version: version.try(:version), title: version.try(:title), info: version.try(:node), filel: version.try(:file_load_url)}}
      end
    end
  end
end
