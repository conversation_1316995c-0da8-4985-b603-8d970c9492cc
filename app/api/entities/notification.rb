# frozen_string_literal: true

module Entities
  class Notification < Grape::Entity
    expose :id, documentation: {type: Integer, desc: "用户id"}
    expose :is_read, documentation: {type: Grape::API::<PERSON><PERSON>an, desc: "是否已读"}
    expose :notify_type_i18n, as: :notify_type, documentation: {type: String, desc: "通知类型", example: "回复了你的评论"}
    expose :target_type, documentation: {type: String, desc: "跳转目标类型", example: "Comment"}
    expose :target_id, documentation: {type: Integer, desc: "跳转目标ID", example: "21"}
    expose :content, documentation: {type: String, desc: "内容"}
    expose :product_id, documentation: {type: Integer, desc: "商品ID(当类型为讨论时使用)"}
    expose :created_at, documentation: {type: String, desc: "通知时间", example: "2023-06-01 12:00:00"}
    expose :send_user, using: Entities::User

    def created_at
      object.created_at.strftime("%F %T")
    end

    def product_id
      object.target.product_id if object.target_type == "Comment"
    end
  end
end
