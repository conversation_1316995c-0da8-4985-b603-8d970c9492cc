# frozen_string_literal: true

module Entities
  module Opinions
    class ChildOpinion < Grape::Entity
      expose :created_at, documentation: {required: true, type: "String", desc: "创建日期", example: "2023-06-01 12:00:00"}
      expose :user, using: Entities::User
      expose :detail, documentation: {required: true, type: "String", desc: "反馈内容"}
      expose :asset_imgs, using: Entities::AssetImg, documentation: {required: true, is_array: true, desc: "图片列表"}

      def created_at
        object.created_at.strftime("%F %T")
      end
    end
  end
end
