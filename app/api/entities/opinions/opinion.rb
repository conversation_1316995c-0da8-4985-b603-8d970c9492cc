# frozen_string_literal: true

module Entities
  module Opinions
    class Opinion < Grape::Entity
      expose :id, documentation: { required: true, type: 'Integer', desc: '反馈 ID' }
      expose :detail, documentation: { required: true, type: 'String', desc: '反馈内容' }
      expose :status_i18n,
             as: :status,
             documentation: { required: true, type: 'String', desc: '反馈状态', example: '处理中' }
      expose :code, documentation: { required: true, type: 'Integer', desc: '工单号', example: 'IQ16854381053324' }
      expose :created_at,
             documentation: { required: true, type: 'String', desc: '创建日期', example: '2023-06-01 12:00:00' }
      expose :option_type_i18n,
             as: :option_type,
             documentation: { required: true, type: 'String', desc: '反馈类型', example: '软件使用' }

      def created_at
        object.created_at.strftime('%F %T')
      end
    end
  end
end
