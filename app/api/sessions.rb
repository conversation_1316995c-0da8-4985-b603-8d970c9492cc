class Sessions < Base
  namespace :react do
    resources :sessions do
      desc '发送短信'
      params do
        requires :mobile, type: String, desc: '用户电话号码'
        # requires :code, type: String, desc: "验证码信息"
        requires :resource_type, type: String, values: ['NewUser'], desc: '验证码信息'
      end
      post :loginSendSms do
        verify_code = VerificationCode.where(phone: params[:mobile], resource_type: params[:resource_type]).first
        code = rand.to_s[2..7]
        if verify_code
          verify_code.code = code
          if verify_code.updated_at + 60 > Time.now
            return { msg: '验证码发送间隔太短，请等待 60 秒后重试', code: 500, data: {} }
          end
        else
          verify_code = VerificationCode.new(phone: params[:mobile], resource_type: params[:resource_type], code: code)
        end

        if verify_code.save
          response = Aliyun::CloudSms.send_msg(params[:mobile], Settings.sms.template_code, { code: code })
          data = JSON.parse(response.body)
          Rails.logger.error '-----------loginSendSms------------'
          Rails.logger.error data
          if data['Code'] != 'OK'
            # WarningNoticeJob.perform_later(params[:mobile], data["Message"])
            return { msg: '出错了', code: 500, data: {} }
          end

          return { msg: '发送成功', code: 200, data: {} }
        else
          return { msg: '出错了', code: 500, data: {} }
        end
      end

      desc '签名账号'
      params do
      end
      post :sign_account do
      end

      desc '绑定手机'
      params do
        requires :mobile, type: String, desc: '用户电话号码'
        requires :code, type: String, desc: '验证码信息'
        requires :token, type: String, desc: '用户的token'
      end
      post :mobile_bind do
        if VerificationCode.where(phone: params[:mobile], resource_type: 'NewUser').first
          # response = RestClient.get "https://sms.iqunix.com/sms/verify_sms_code.php", { params: { mobile: params[:mobile],verify_code: params[:code] , key: "f679"} }                     data = JSON.parse(response.body)

          if User.where(phone: params[:mobile]).first
            return { msg: '该手机号已经被注册', code: 501, data: {} }
          end
          if user = User.user_status.where(authentication_token: params[:token]).first
            if user.update(phone: params[:mobile])
              return { msg: '手机绑定成功！', code: 200, data: {} }
            end
          end
        else
          return { msg: '手机绑定失败！', code: 500, data: {} }
        end
      end

      desc '绑定微信' do
        detail '绑定微信'
        success model: Entities::WechatUser, status: 200, message: '返回格式'
      end
      params do
        requires :code, type: String, desc: '用户微信授权获取的 code'
      end
      post :wechat_bind do
        code = params[:code]
        request = RestClient.get("https://api.weixin.qq.com/sns/oauth2/access_token?appid=#{Settings.wechat.app_id}&secret=#{Settings.wechat.app_secret}&code=#{code}&grant_type=authorization_code")
        token = JSON.parse(request.body)
        openid = token['openid']
        access_token = token['access_token']
        info = RestClient.get("https://api.weixin.qq.com/sns/userinfo?access_token=#{access_token}&openid=#{openid}")
        openid = JSON.parse(info.body)['openid']
        unionid = JSON.parse(info.body)['unionid']
        error!('用户授权失败', 400) unless openid

        wechat_user = User.where(openid: openid).user_status.first
        error!('当前微信已绑定其他用户', 400) if wechat_user.present?

        phone_user = User.user_status.find_by(authentication_token: headers["Usertoken"])
        if phone_user.update(openid: openid, unionid: unionid, wx_name: JSON.parse(info.body)['nickname'])
          present phone_user, with: Entities::WechatUser
        else
          error!('微信绑定失败', 400)
        end
      end

      desc '微信用户和手机号用户进行合并'
      params do
        requires :mobile, type: String, desc: '用户电话号码'
        requires :code, type: String, desc: '验证码信息'
        requires :token, type: String, desc: '微信用户的token'
      end
      post :wechat_merge do
        code = params[:code]
        if v_c = VerificationCode.where(phone: params[:mobile], resource_type: 'NewUser').first
          redis_login_code = v_c.code
          if code != redis_login_code || redis_login_code.nil?
            return { msg: '验证码错误', code: 401, data: {} }
          end

          if wx_user = User.user_status.where(authentication_token: params[:token]).first
            if u = User.where(phone: params[:mobile]).first
              unless u.openid.blank?
                return { msg: '手机号已经绑定其他微信用户', code: 501, data: {} }
              end
            end
            if wx_user.phone == params[:mobile]
              return { msg: '同一个用户', code: 501, data: {} }
            end
            tmp_wx_open_id = wx_user.openid
            if wx_user.update_columns(openid: nil) && u&.update_columns(openid: tmp_wx_open_id)
              wx_user.destroy
              return { msg: '绑定成功', code: 200, data: { user: u.attributes.merge('user_nikename' => u.try(:user_name)), token: u.authentication_token } }
            end
          end
        end
        return { msg: '绑定不成功', code: 501, data: {} }
      end

      desc '解除绑定'
      params do
        requires :token, type: String, desc: '用户的token'
      end
      post :unbind_wx do
        wx_user = User.user_status.where(authentication_token: params[:token]).first
        wx_user.openid = nil
        wx_user.unionid = nil
        if wx_user.save
          return { msg: '解绑成功', code: 200, data: {} }
        else
          return { msg: '解绑不成功', code: 400, data: {} }
        end
      end

      desc '短信登录'
      params do
        requires :mobile, type: String, desc: '电话号码'
        requires :code, type: String, desc: '验证码'
      end
      post '' do
        mobile = params[:mobile]
        code = params[:code]
        if mobile == '12312345678' && code == '6699'
          @user = User.where(phone: '12312345678').first
          # login_as @user
          return { msg: '登录成功！', code: 200, data: { user: @user.attributes.merge('user_nikename' => @user.try(:user_name)), token: @user.authentication_token } }
        end
        if v_c = VerificationCode.where(phone: params[:mobile], resource_type: 'NewUser').first
          if Time.now - 600 > v_c.updated_at
            return { msg: '验证码过期！', code: 500, data: {} }
          end
          if v_c.code != code || v_c.code.nil?
            return { msg: '验证码错误', code: 401, data: {} }
          end
          if @user = User.where(phone: mobile).user_status.first
            # login_as @user
            return { msg: '登录成功！', code: 200, data: { user: @user.attributes.merge('user_nikename' => @user.try(:user_name)), token: @user.authentication_token } }
          else
            # 如果是新用户的话进行创建新用户
            @user = User.create(phone: mobile)
            return { msg: '登录成功！', code: 200, data: { user: @user.attributes.merge('user_nikename' => @user.try(:user_name)), token: @user.authentication_token } }
          end
        else
          return { msg: '该手机未发送验证码！', code: 500, data: {} }
        end
      end

      desc '微信小程序登录'
      params do
        requires :code, type: String, desc: '登录时获取的 code，可通过wx.login获取'
      end
      post :wechat_mini_program do
        response = Faraday.get("https://api.weixin.qq.com/sns/jscode2session?appid=#{Settings.mini_program.appid}&secret=#{Settings.mini_program.app_secret}&js_code=#{params[:code]}&grant_type=authorization_code")
        result = JSON.parse(response.body)
        Rails.logger.info '----------wechat_mini_program------------'
        Rails.logger.info result
        if result['unionid'].blank?
          return { msg: '小程序登录失败，未获取到unionid', code: 400, data: {} }
        end

        user = User.find_or_create_by(unionid: result['unionid'], mini_program_openid: result['openid'])
        if user.phone.blank?
          return { msg: '小程序登录成功', code: 200, data: {bind_phone: false, token: user.authentication_token} }
        else
          return { msg: '小程序登录成功', code: 200, data: {bind_phone: true, token: user.authentication_token, avatar: user.avatar, username: user.username} }
        end
      end

      desc '微信小程序绑定手机'
      params do
        requires :code, type: String, desc: '获取手机号code'
        requires :token, type: String, desc: '用户token'
      end
      post :wechat_mini_program_bind_phone do
        url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=#{mini_program_token}"
        body = {
          code: params[:code],
        }.to_json
        response = Faraday.post(url, body, 'Content-Type' => 'application/json')
        result = JSON.parse(response.body)
        phone_number = result['phone_info']['purePhoneNumber']
        Rails.logger.info '----------wechat_mini_program-----get_phone-------'
        Rails.logger.info result
        return { msg: '授权绑定失败', code: 400, data: {} } if phone_number.blank?

        user = User.find_by(authentication_token: params[:token])
        auth_user = User.find_by(phone: phone_number)
        # 存在手机号用户，则进行合并处理
        if auth_user.present?
          auth_user.update(unionid: user.unionid, mini_program_openid: user.mini_program_openid)
          auth_user.update_columns(created_at: Time.now)
          user.destroy
          return { msg: '授权绑定成功', code: 200, data: {token: auth_user.authentication_token, avatar: auth_user.avatar, username: auth_user.username} }
        else
          if user&.update(phone: phone_number)
            user.update_columns(created_at: Time.now)
            return { msg: '授权绑定成功', code: 200, data: {token: user.authentication_token, avatar: user.avatar, username: user.user_name} }
          else
            return { msg: '授权绑定失败', code: 400, data: {} }
          end
        end
      end
    end

    resources :sns do
      require 'rest-client'
      desc '微信登录'
      params do
        requires :code, type: String, desc: '用户Token'
      end
      post :wechat_code do
        code = params[:code]
        request = RestClient.get("https://api.weixin.qq.com/sns/oauth2/access_token?appid=#{Settings.wechat.app_id}&secret=#{Settings.wechat.app_secret}&code=#{code}&grant_type=authorization_code")
        token = JSON.parse(request.body)
        openid = token['openid']
        access_token = token['access_token']
        info = RestClient.get("https://api.weixin.qq.com/sns/userinfo?access_token=#{access_token}&openid=#{openid}")
        unionid = JSON.parse(info.body)["unionid"]
        Rails.logger.error "--------unionid----------"
        Rails.logger.error "--------#{unionid}----------"
        openid = JSON.parse(info.body)['openid']
        if !openid.blank? && (@user = User.where(openid: openid).user_status.first)
          hash_ = {}
          if @user.wx_name.blank?
            hash_ = hash_.merge(wx_name: JSON.parse(info.body)['nickname'])
          end
          if @user.avatar.blank?
            hash_ = hash_.merge(avatar: JSON.parse(info.body)['headimgurl'])
          end
          if @user.unionid.blank?
            hash_ = hash_.merge(unionid: unionid)
          end
          @user.update(hash_)
          return { msg: '登录成功！', code: 200, data: { user: @user.attributes.merge('user_nikename' => @user.try(:user_name)), token: @user.authentication_token } }
        elsif !openid.blank?
          # 创建一个用户
          @user = User.create(openid: openid, unionid: unionid, avatar: JSON.parse(info.body)['headimgurl'], wx_name: JSON.parse(info.body)['nickname'])
          return { msg: '登录成功！', code: 200, data: { user: @user.attributes.merge('user_nikename' => @user.try(:user_name)), token: @user.authentication_token } }
        end
        return { msg: '登录不成功！', code: 201, data: { user: {}, token: {} } }
      end
    end
  end
end
