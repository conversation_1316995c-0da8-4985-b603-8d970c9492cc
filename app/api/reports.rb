class Reports < Base
  resources :reports do
    before do
      user_authenticate!
    end
    desc "举报评论"
    params do
      requires :resoure_id, type: Integer, desc: "举报的类型id"
      requires :resoure_type, type: String, values: ["Comment"], desc: "举报的类型"
      requires :report_code, type: Integer, values: [1, 2, 3, 4, 5, 6, 7, 8, 9]
      optional :report_desc, type: String, desc: "描述"
    end
    post "" do
      if params[:resoure_type] == "Comment"
        if comment = Comment.find(params[:resoure_id])
          report = Report.new(report_code: params[:report_code], report_desc: params[:report_desc])
          report.user = @user
          report.resoure = comment
          if report.save
            return {msg: "举报成功", code: 200, data: report}
          else
            return {msg: "举报失败", code: 400, data: {}}
          end
        else
          return {msg: "无评论！", code: 400, data: {}}
        end
      end
    end
  end
end
