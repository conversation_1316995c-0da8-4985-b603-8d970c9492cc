class Users < Base
  namespace :react do
    resources :users do
      desc "生成极光里面的机器设备码"
      params do
        requires :token, type: String, desc: "用户的token"
        requires :register_id, type: String, desc: "设备id"
      end
      post "register_id" do
        register_id = params[:register_id]
        if user = User.user_status.where(authentication_token: params[:token]).first
          if user.update(register_id: register_id)
            return {msg: "保存成功！", code: 200, data: {}}
          else
            return {msg: "保存失败！", code: 400, data: {}}
          end

        else
          return {msg: "用户错误！", code: 400, data: {}}
        end
      end

      desc "注销用户"
      params do
        requires :token, type: String, desc: "用户的token"
      end
      post "destory_user" do
        if user = User.user_status.where(authentication_token: params[:token]).first
          user.phone = nil
          user.openid = nil
          user.username = user.user_nikename + ".#{Time.now.to_i}.delete"
          user.status = true
          if user.save
            return {msg: "注销", code: 200, data: {}}
          end
        end
      end

      desc "注册用户"
      params do
        requires :mobile, type: String, desc: "电话"
        requires :code, type: String, desc: "验证号码"
        requires :password, type: String, desc: "密码"
      end
      post "register" do
        # 验证码验证

        if user = User.where(phone: params[:mobile]).first
          return {msg: "该手机号已经注册", code: 413, data: {}}
        end
        user = User.new(user_nikename: params[:mobile], password: Digest::MD5.hexdigest(Digest::MD5.hexdigest(password)), phone: params[:mobile])
        if user.save
          return {msg: "成功注册！", code: 200, data: {user: user.attributes.merge("user_nikename" => user.try(:user_name)), token: user.authentication_token}}
        end
      end

      desc "修改密码"
      params do
        requires :mobile, type: String, desc: "电话"
        requires :code, type: String, desc: "验证号码"
        requires :password, type: String, desc: "密码"
      end
      post "reset" do
        # 验证码
        user = User.where(phone: params[:mobile]).where(status: false).first

        # user = User.new(user_nikename: params[:mobile],password:  Digest::MD5.hexdigest(Digest::MD5.hexdigest(password)),phone: params[:mobile])
        if user.update(password: Digest::MD5.hexdigest(Digest::MD5.hexdigest(params[:password])))
          return {msg: "密码修改成功！", code: 200, data: {user: user.attributes.merge("user_nikename" => u.try(:user_name))}}
        else
          return {msg: "手机号码不存在！", code: 401, data: {}}
        end
      end

      desc "更新用户名"
      params do
        requires :token, type: String, desc: "用户Token"
        requires :nick_name, type: String, desc: "用户名"
      end
      put "name_update" do
        if user = User.user_status.where(authentication_token: params[:token]).first
          user.username = params[:nick_name]
          if user.save
            return {msg: "用户名修改成功！", code: 200, data: {user: user.attributes.merge("user_nikename" => user.username)}}
          else
            return {msg: "用户名修改不成功！", code: 401, data: {}}
          end
        end
        return {msg: "无用户！", code: 401, data: {}}
      end

      desc "更新图片"
      params do
        requires :token, type: String, desc: "用户Token"
        optional :avatar, type: File, desc: "图片"
        optional :avatar_url, type: String, desc: "远程图片"
      end
      put "avatar_update" do
        if user = User.user_status.where(authentication_token: params[:token]).first
          user.avatar_file = params[:avatar] unless params[:avatar].blank?
          user.avatar = params[:avatar_url] unless params[:avatar_url].blank?
          if user.save
            user.update_column(:avatar, user.avatar_file_url)
            return {msg: "用户名修改成功！", code: 200, data: {user: user.attributes.merge("user_nikename" => user.try(:user_name))}}
          else
            return {msg: "用户名修改不成功！", code: 401, data: {}}
          end
        end
        return {msg: "无用户！", code: 401, data: {}}
      end

      desc "更新图片"
      params do
        requires :token, type: String, desc: "用户Token"
      end
      post "pictures" do
      end

      desc "用户订单"
      params do
      end
      get "order_pay" do
        # puts params.inspect
        str = "pingxx"
        return str
      end

      desc "用户订单"
      params do
        requires :token, type: String, desc: "用户Token"
        optional :page, type: String, desc: "翻页"
        optional :per, type: String, desc: "每页条数"
        optional :status, type: String, desc: "状态"
      end

      post "orders" do
        if user = User.user_status.where(authentication_token: params[:token]).first
          query = {}
          if !params[:status].blank?
            query[:status] = params[:status]
          end
          orders = user.orders.paid.where(query).page(params[:page] || 1).per(params[:per] || 5)
          data = []
          (orders || []).each do |order|
            deatils = []
            (order.order_details || []).each do |deatil|
              unless deatil.sku.blank?
                properties_id = deatil.sku.properties.unscoped.group(:id)
                attribute_names = deatil.sku.properties.collect(&:name).uniq
              end

              deatils << {
                id: deatil.id,
                order_id: order.id,
                is_pre_sale: order.pre_sale?,
                goods_id: deatil.product_id,
                num: deatil.sku_count,
                pre_price: deatil.skuprice,
                prodcut: {
                  name: deatil.product_name,
                  type_id: deatil.product.try(:type_id),
                  color: "",
                  cover: deatil.try(:skupictureurl),
                  white_image: deatil.product.try(:before_image_url),
                  crowd_image: "",
                  discount: deatil.sku.try(:sku_markedprice),
                  is_crowd: false,
                  status: deatil.product.try(:is_sale)
                },
                attribute: properties_id || "",
                attribute_name: attribute_names || ""
              }
            end

            data << {
              id: order.id,
              order_sn: order.number,
              member_id: order.user.try(:id),
              contact: order.receive_name,
              province: "",
              city: "",
              district: "",
              address: order.receive_address,
              total_price: "",
              postage: "",
              remark: order.user_node,
              status: order.status,
              exp_name: order.logistics_com,
              exp_sn: order.logistics_number,
              create_time: order.try(:created_at).strftime("%Y-%m-%d %H:%M:%S"),
              user: {
                nick_name: order.user_nickname
              },
              deatils: deatils

            }
          end
          return {msg: "发送成功", code: 200, data: data}
        end
        return {msg: "用户不对", code: 401, data: data}
      end

      desc "评论"
      params do
        requires :token, type: String, desc: "用户Token"
      end
      post "topics" do
      end
    end
  end
end
