class AfterSales < Base
  # 退货退款售后
  resources :after_sales do
    before do
      user_authenticate!
    end
    desc "新建售后单"
    params do
      optional :order_id, type: Integer, desc: "订单id"
      optional :cargostatus, type: String, desc: "货物状态"
      optional :reason_code, type: Integer, desc: "1.仅退款 11未收到货 111发货时间太长 112 不喜欢/不想要 12 已收到货 121 产品与描述不符 122产品质量问题 123产品有微小瑕疵 124少件/漏发 125包装/商品破损 126发错货 2.退货退款 21无理由退款货 22产品与描述不符 23产品质量问题 24产品有微小瑕疵 25少件/漏发 26包装/商品破损 27发错货 3.换货 31无理由退款货 32产品与描述不符 33产品质量问题 34产品有微小瑕疵 35少件/漏发 36包装/商品破损 37发错货"
      optional :description, type: String, desc: "描述"
      # optional :photos, type: File, desc: "图片"
      optional :photos, type: Array, desc: "图片数组"
    end
    post "" do
      unless (order = @user.orders.where(id: params[:order_id]).first)
        return {msg: "没有订单！", code: 400, data: {}}
      end
      if !order.after_sale.blank?
        return {msg: "该订单已经申请过售后，请勿重复申请！", code: 400, data: {}}
      end
      # 退款
      if order.status == 1 || order.status == 2 || order.status == 2000
        af_sale = AfterSale.new
        # af_sale.order = order
        # af_sale.user = @user
        af_sale.order_status = order.status
        order.status = 11 if order.status == 1
        order.status = 11 if order.status == 2000
        order.status = 21 if order.status == 2
        af_sale.order = order
        af_sale.detail = params[:description]
        # af_sale.images = params[:photos]
        af_sale.amout = order.amount
        af_sale.status = 0
        af_sale.reason = AfterSale::ReasonCode[params[:reason_code].to_s]

        if params[:photos].present?
          (params[:photos] || []).each do |p|
            af_sale.asset_imgs << AssetImg.new(image: p)
          end
        end
        order.audit_comment = "after_sale"

        if af_sale.save! && order.save!
          OrderAutomaticallyRefundedJob.perform_later(order.id)
          return {msg: "保存！", code: 200, data: af_sale.attributes.merge(reason_code: params[:reason_code])}
        else
          return {msg: "保存失败", code: 400, data: {}}
        end
      end

      return {msg: "售后失败", code: 400, data: {}}
    end
  end
end
