class FirmwareCheck < Base
  namespace :firmware_check do
    desc "获取最新固件"
    params do
      requires :deploy_env, type: String, values: Firmware.deploy_envs.keys.map(&:to_s)
      requires :device_id, type: Integer
    end
    get "firmware_check" do
      real_device_id = Firmware.get_real_device_id(params[:device_id])
      firmware = Firmware.where("JSON_CONTAINS(deploy_env, '\"#{params[:deploy_env]}\"')").where(device_id: real_device_id).order(id: :desc).first
      if firmware.present?
        {
          version: firmware.version,
          update_log: {
            description_zh: firmware.description_zh,
            description_tw: firmware.description_tw,
            description_en: firmware.description_en,
            description_ja: firmware.description_ja,
            description_ko: firmware.description_ko,
          },
          force_update: firmware.force_update,
          download_url: firmware.file_url
        }
      else
        {
          version: nil,
          update_log: nil,
          force_update: false,
          download_url: nil
        }
      end
    end
  end
end
