class Shops < Base
  resources :react do
    resources :shops do
      desc "广告页面"
      params do
        # requires :token, type: String, desc: "用户Token"
      end
      get "" do
        # 活跃用户统计
        user_id = User.find_by(authentication_token: headers["Usertoken"])&.id
        UserActiveStatisticJob.perform_later(user_id) if user_id.present?

        banners = Banner.where(is_closing: false).reorder("que asc")
        data = []
        (banners || []).each do |banner|
          if banner.is_crowd?
            show_corner = true # banner.crowd.sale_count.to_i < banner.crowd.count.to_i
            crowd = banner.crowd.attributes.merge(show_corner: show_corner, corner_image: banner.crowd&.status_icon.to_s)
            data << banner.attributes.merge(product_id: banner.crowd.product_id, image: {url: image_processing(banner.image_url)}, crowd: crowd)
          else
            data << banner.attributes.merge(image: {url: image_processing(banner.image_url)})
          end
        end

        return {msg: "广告页面", code: 200, data: {banners: data}}
      end

      desc "商品列表"
      params do
        optional :page, type: String, desc: "翻页"
        optional :per, type: String, desc: "每页条数"
      end
      get :products do
        user_id = User.find_by(authentication_token: headers["Usertoken"])&.id
        @products = Product.where(on_shelf: true).reorder("que asc").page(params[:page] || 1).per(params[:per] || 5)
        data = []
        @products.each do |pro|
          data << {
            id: pro.id,
            name: pro.name,
            detail: pro.detail,
            button_txt: pro.button_txt,
            subheadcomment: pro.title,
            # before_image: pro.before_image,
            # share_img_url: pro.aftermarket,
            list_img_url: {url: image_processing(pro.list_img_url_url, width: 500)},
            sale_count: pro.sale_count,
            is_sale: pro.is_sale,
            is_comment: pro.is_comment,
            is_open_comment: pro.is_open_comment,
            tag_name: pro.try(:tag).try(:name),
            created_at: pro.try(:created_at).strftime("%Y-%m-%d %H:%M:%S"),
            photos: pro.try(:product_images).collect(&:picture_url)
          }
        end
        return {msg: "商品列表！", code: 200, data: data}
      end

      desc "众筹的页面"
      params do
      end
      get :crowd do
      end
    end
  end
end

