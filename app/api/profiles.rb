class Profiles < Base
  namespace :profiles do
    desc "生成分享码"
    params do
      requires :data, type: String
    end
    post "generate_share_code" do
      final_data = JSON.parse(Base64.decode64(params[:data]))
      device = Device.find_by(pid: final_data['device_id'])
      profile = Profile.create!(data: final_data, device_id: device.id, share_code: SecureRandom.alphanumeric(6).upcase)
      {
        share_code: profile.share_code
      }
    end

    desc "根据分享码获取配置文件"
    params do
      requires :share_code, type: String
    end
    post "get_profile" do
      profile = Profile.find_by(share_code: params[:share_code])
      if profile.nil?
        {
          success: false,
          message: "Not Found"
        }
      else
        {
          success: true,
          data: Base64.encode64(profile.data.to_json)
        }
      end
    end
  end
end
