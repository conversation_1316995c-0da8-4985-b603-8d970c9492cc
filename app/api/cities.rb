class Cities < Base
  resources :home do
    desc "城市的json"
    params {}
    get "all_city" do
      parents = Region.where(level: 0)
      parent_arry = []
      parents.each do |parent|
        cities = Region.where(level: 1, parent_id: parent.id)
        city_arry = []
        cities.each do |city|
          areas = Region.where(level: 2, parent_id: city.id)
          areas_arr = []
          areas.each do |area|
            areas_arr << {
              id: area.id,
              name: area.name
            }
          end
          city_arry << {
            id: city.id,
            name: city.name,
            areas: areas_arr
          }
        end
        parent_arry << {
          id: parent.id,
          name: parent.name,
          city: city_arry
        }
      end

      {status: 200, data: parent_arry, msg: "返回成功"}
    end
  end
end
