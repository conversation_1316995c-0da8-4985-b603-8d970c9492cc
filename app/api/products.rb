class Products < Base
  namespace :react do
    resources :products do
      desc "产品的评论"
      params do
        optional :token, type: String, desc: "用户Token"
        optional :sort, type: String, desc: "排序"
        optional :page, type: String, desc: "翻页"
        optional :per, type: String, desc: "每页条数"
        optional :pre, type: String, desc: "每页条数"
        optional :product_id, type: String, desc: "产品的id"
      end
      get "comments" do
        # 是否有用户
        @user = User.user_status.where(authentication_token: params[:token]).first
        sort = params["sort"] || "time"
        if @product = Product.find_by_id(params[:product_id])
          @comments = case sort
          when "hot"
            @product.comments.comment_status.where(parent_id: nil).reorder(userlikes_count: :desc, id: :desc) # .page(params[:page]||1).per(params[:per]||20)
          when "time"
            @product.comments.comment_status.where(parent_id: nil).reorder(created_at: :desc, id: :desc) # .page(params[:page]||1).per(params[:per]||20)
          when "photos"
            @product.comments.comment_status.where(parent_id: nil).reorder(asset_imgs_count: :desc, id: :desc) # .page(params[:page]||1).per(params[:per]||20)
          else
            @product.comments.comment_status.where(parent_id: nil).reorder(que: :desc, id: :desc) # .page(params[:page]||1).per(params[:per]||20)
          end
          # @comments_count = @comments.count
          t_c_c = @comments.count.to_i
          t_c_c += @comments.sum(&:comments_count)
          @comments = @comments.page(params[:page] || 1).per(params[:per] || params[:pre] || 20)
          data = []
          (@comments || []).each do |comment|
            if comment.is_owner
              if comment.user != @user
                next
              end
            end

            chlid_comment = []
            (comment.comments.comment_status || []).each do |child|
              if child.is_owner
                if child.user != @user
                  # 不显示
                  next
                end
              end
              chlid_comment << {
                comment_id: child.id,
                member_id: child.user.id,
                content: child.content,
                photos: child.asset_imgs.collect(&:image_url),
                status: child.status,
                create_time: child.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                star: child.userlikes_count,
                is_star: child.userlikes.where(user_id: @user.try(:id)).first ? true : false,
                member: {
                  member_id: child.user.id,
                  nick_name: child.try(:user).try(:user_name),
                  avatar: child.user.try(:avatar)
                }
              }
            end

            data << {
              comment_id: comment.id,
              member_id: comment.user.id,
              content: comment.content,
              photos: comment.asset_imgs.collect(&:image_url),
              status: comment.status,
              create_time: comment.created_at.strftime("%Y-%m-%d %H:%M:%S"),
              count: comment.comments_count, # //回复数
              star: comment.userlikes_count,
              is_star: comment.userlikes.where(user_id: @user.try(:id)).first ? true : false,
              member: {
                member_id: comment.user.id,
                nick_name: comment.user.try(:user_name),
                avatar: comment.user.try(:avatar)
              },
              chlid_comment: chlid_comment
            }
          end

          return {
            msg: "成功",
            code: 200,
            count: @comments.total_count,
            total_comment_count: t_c_c,
            data: data
          }
        else
          return {
            msg: "失败",
            code: 400,
            count: 0,
            data: {}
          }
        end
      end

      desc "返回所有的属性sku"
      params do
        optional :sort, type: String, desc: "排序"
        optional :page, type: String, desc: "翻页"
        optional :per, type: String, desc: "每页条数"
        optional :product_id, type: String, desc: "产品的id"
      end
      get "attribute" do
        if @product = Product.find_by_id(params[:product_id])
          @all_cats = @product.skus
          skus = []
          p_properties = {}
          (@all_cats || []).each do |sku|
            properties = sku.properties
            sku_quantity = if sku.sku_quantity.to_i <= 0
                             0
                           else
                             if @product.is_pre_sale?
                               1
                             else
                               sku.sku_quantity
                             end
                           end
            skus << {
              sku_id: sku.id,
              sku_pictureurl: sku.sku_pictureurl,
              sku_markedprice: @product.origin_sale_price(sku),
              sku_price: @product.sale_price(sku),
              sku_quantity: sku_quantity,
              properties: sku.properties
            }
            properties.each do |p|
              p_properties[p.parent.name] ||= []
              p_properties[p.parent.name] << p.name
            end
          end

          values = []
          data = []
          (p_properties || []).each do |key, value|
            pp = Property.where(name: key).first
            p_name = {
              attribute_id: pp.id,
              attribute_name: pp.name
            }
            values = []
            value.each do |c_p|
              cpp = Property.where(name: c_p, parent_id: pp.id).first
              c_name = {
                attribute_id: cpp.id,
                attribute_name: cpp.name,
                parent: cpp.parent_id
              }
              # 多判断一个这个孩子有没有

              values << c_name
            end
            values.uniq!
            data << {
              name: p_name,
              value: values
            }
          end
          return {msg: "产品属性", code: 200, data: {attribute: data, attribute123: p_properties, inventory: skus}}
        else
          return {msg: "无产品属性", code: 400, data: {attribute: {}, inventory: {}}}
        end
      end

      desc "返回所有的属性sku-微信小程序"
      params do
        optional :product_id, type: String, desc: "产品的id"
      end
      get "attributes" do
        if @product = Product.find_by_id(params[:product_id])
          skus = []
          p_properties = {}
          @product.skus.each do |sku|
            properties = sku.properties.order(parent_id: :desc)
            sku_quantity = if sku.sku_quantity.to_i <= 0
                             0
                           else
                             if @product.is_pre_sale?
                               1
                             else
                               sku.sku_quantity
                             end
                           end
            skus << {
              _id: sku.id,
              goods_id: @product.id,
              goods_name: @product.name,
              image: sku.sku_pictureurl.present? ? sku.sku_pictureurl_url : '',
              price: @product.sale_price(sku) * 100,
              stock: sku_quantity,
              sku_name_arr: sku.properties.order(parent_id: :desc).pluck(:name)
            }
            properties.each do |p|
              p_properties[p.parent.name] ||= []
              p_properties[p.parent.name] << p.name
            end
          end

          values = []
          data = []
          (p_properties || []).each do |key, value|
            pp = Property.where(name: key).first

            values = []
            value.each do |c_p|
              cpp = Property.where(name: c_p, parent_id: pp.id).first
              values << {
                name: cpp.name
              }
            end
            values.uniq!
            data << {
              name: pp.name,
              list: values
            }
          end
          return {msg: "产品属性", code: 200, data: {_id: @product.id, name: @product.name, goods_thumb: @product.list_img_url_url, spec_list: data, sku_list: skus}}
        else
          return {msg: "无产品属性", code: 400, data: {spec_list: {}, sku_list: {}}}
        end
      end

      desc "获取单独的产品"
      params do
        optional :sku_id, type: String, desc: "产品的id"
      end
      get ":id" do
        if @product = Product.find_by_id(params[:id])
          # 主图
          @carousel = @product.product_images
          @orders_count = @product.order_details.count(:sku_count)
          @comment_count = @product.comments.comment_status.size
          @comment_avatar_array = @product.comments.comment_status.map { |c| c.user.try(:avatar) }
          @product_detail = @product.detail

          img_regex = /<img\s+src="(?<url>.*?)"/i
          @product_detail = @product_detail.gsub(img_regex) do |match|
            url = $~[:url]
            match.sub(url, image_processing(url)).to_s
          end

          unless sku = @product.skus.where(id: params[:sku_id]).first
            sku = @product.skus.first
          end
          if @product.is_pre_sale?
            user_id = User.find_by(authentication_token: headers["Usertoken"])&.id
            is_sale_status = @product.can_pay?(User.find_by(id: user_id))
          else
            user_id = User.find_by(authentication_token: headers["Usertoken"])&.id
            user = User.find_by(id: user_id)
            if @product.id == 55
              if user.orders.joins(:order_details).where("order_details.product_id = #{51}").paid.first.present? && !user.orders.joins(:order_details).where("order_details.product_id = #{55}").paid.first.present?
                is_sale_status = @product.is_sale
              else
                is_sale_status = false
              end
            else
              is_sale_status = @product.is_sale
            end
          end
          return {msg: "用户收货列表", code: 200, data: {carousel: @carousel.map { |product_image| image_processing(product_image.picture_url) },
                  product: {name: @product.name, before_image: @product.before_image_url, share_img_url: @product.aftermarket_url, list_img_url: @product.list_img_url_url,
                    cover: @product.aftermarket_url, sale_count: @orders_count, buy_comments: @product.button_txt,
                    show_comments_count: @product.is_comment, show_progress_bar: @product.try(:show_progress_bar),
                    status: is_sale_status, sku_id: sku.id, price: @product.outside_sale_price(sku), original_price: @product.origin_sale_price(sku)},
                    share: {link: "https://" + request.host_with_port.to_s + "/products/" + @product.id.to_s, cover: @product.aftermarket_image,
                    title: @product.name, subhead: @product.title}, comment: {count: @comment_count, avatar_array: @comment_avatar_array[0..5]},
                    product_detail: @product_detail}}
        else
          return {msg: "为空", code: 400, data: {}}
        end
      end
    end
  end
end
