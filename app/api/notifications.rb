class Notifications < Base
  resources :notifications do
    before do
      user_authenticate!
    end

    desc "个人消息列表" do
      detail "查询个人消息列表"
      success model: Entities::Notification, message: "返回格式"
      is_array true
    end
    params do
      use :paginate
    end
    get "aboutme" do
      present paginate(@user.notifications.order(id: :desc)), with: Entities::Notification
    end

    desc "删除消息" do
      detail "删除消息"
    end
    params do
      requires :id, type: Integer, desc: "消息通知ID"
    end
    delete ":id" do
      @notification = @user.notifications.find_by(id: params[:id])
      if @notification&.destroy
        no_content!
      else
        bad_request!("操作失败")
      end
    end

    desc "检查未读通知数"
    get "check" do
      @notifications_count = @user.notifications.where(is_read: false).count
      message = @notifications_count > 0
      data = {
        message: message,
        count: @notifications_count
      }
      return {msg: "检查通知！", code: 200, data: data}
    end

    desc "全部变成已读信息"
    get "read_all" do
      @notifications = @user.notifications.where(is_read: false)
      if @notifications.update_all(is_read: true)
        return {msg: "阅读所有通知", code: 200, data: {}}
      else
        return {msg: "操作不成功", code: 400, data: {}}
      end
    end
  end
end
