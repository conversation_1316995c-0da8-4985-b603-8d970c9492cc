class Orders < Base
  namespace :react do
    resources :orders do
      desc "repay"
      params do
        requires :token, type: String, desc: "token值"
        optional :order_id, type: Integer, desc: "订单id"
        optional :pay_type, type: String, values: ["alipay", "wxpay"], desc: "支付方式"
        optional :remark, type: String, desc: "用户留言"
      end

      post "repay" do
        if User.user_status.where(authentication_token: params[:token]).first
          if (order = Order.find_by_id(params[:order_id]))
            new_order_sn = Time.now.to_i.to_s + (0..9).to_a.sample(6).join
            if order.update(number: new_order_sn)
              str = order.pay_charge(request.ip)
              data = {
                order: order,
                pay_charge: str
              }
              if !str.blank? && !str["id"].blank?
                order.update_columns(charge_time: Time.zone.now, charge_number: str["id"])
              end
              return {
                status: 201,
                data: data,
                msg: "保存成功"
              }
            end

          end
        end

        return {msg: "数据有误！", code: 500, data: {}}
      end

      desc "生成订单"
      params do
        requires :token, type: String, desc: "token值"
        optional :sku_id, type: Integer, desc: "规格id"
        optional :good_id, type: Integer, desc: "产品id"
        optional :crowdId, type: Integer, desc: "众筹id"
        optional :number, type: Integer, desc: "数量"
        optional :addr_id, type: Integer, desc: "地址id"
        optional :pay_type, type: String, values: ["alipay", "wxpay"], desc: "支付方式"
        optional :remark, type: String, desc: "用户留言"
      end
      post "" do
        if (user = User.user_status.where(authentication_token: params[:token]).first)
          if (product = Product.find_by_id(params[:good_id]))
            if product.id == 55
              return {msg: "超出可购买的数量", code: 500, data: {}} if user.orders.joins(:order_details).where("order_details.product_id = #{51}").paid.first.present? && user.orders.joins(:order_details).where("order_details.product_id = #{55}").paid.first.present?
            end

            return {msg: "预售商品每次仅可拍下一件", code: 500, data: {}} if product.is_pre_sale? && params[:number].to_i != 1

            return {msg: "未检测到您的定金订单！", code: 500, data: {}} if !product.can_pay?(user)

            sku = product.skus.find_by_id(params[:sku_id])
            receive = user.receives.where(id: params[:addr_id]).first
            if user.blank? || product.blank? || sku.blank? || receive.blank?
              # 判断库存是否充足
              return {msg: "数据有误！", code: 500, data: {}}
            end
            if sku.sku_quantity < 1 || params[:number].to_i < 1 || params[:number].to_i > sku.sku_quantity
              return {msg: "库存小于1或者库存不够！", code: 500, data: {}}
            end
            order = Order.new(count: params[:number])
            order.user = user
            order.receive = receive
            order.user_node = params[:remark]
            order.status = 0
            order.pay_type = params[:pay_type]
            order.order_type = :pre_sale if product.is_pre_sale?
            # 构建订单详情
            order_detail = OrderDetail.new
            order_detail.product = product
            order_detail.sku = sku
            order_detail.sku_number = sku.sku_number
            order_detail.skuprice = product.sale_price(sku)
            order_detail.sku_count = params[:number]
            order.order_details << order_detail
            order.crowd_id = params[:crowdId]
            if order.save && (payment = order.payments.create(amount: order.amount, pay_method: order.pay_type, status: :unpaid))
              # 构建支付单
              str = order.pay_charge(request.ip)
              if !str.blank? && !str["id"].blank?
                order.update_columns(charge_time: Time.zone.now, charge_number: str["id"])
                payment.update(trade_no: str["id"])
              end
              data = {
                order: order,
                pay_charge: str
              }
              return {
                status: 201,
                data: data,
                msg: "保存成功"
              }
            else
              return {
                status: 500,
                data: {},
                msg: "保存成功"
              }
            end
          end
        end
        return {msg: "数据有误！", code: 500, data: {}}
      end

      desc "微信小程序下单"
      params do
        requires :token, type: String, desc: "token值"
        optional :sku_id, type: Integer, desc: "规格id"
        optional :good_id, type: Integer, desc: "产品id"
        optional :crowdId, type: Integer, desc: "众筹id"
        optional :number, type: Integer, desc: "数量"
        optional :addr_id, type: Integer, desc: "地址id"
        optional :remark, type: String, desc: "用户留言"
      end

      post "wxapp" do
        if (user = User.user_status.where(authentication_token: params[:token]).first)
          if (product = Product.find_by_id(params[:good_id]))
            return {msg: "预售商品每次仅可拍下一件", code: 500, data: {}} if product.is_pre_sale? && params[:number].to_i != 1

            return {msg: "未检测到您的定金订单！", code: 500, data: {}} if !product.can_pay?(user)

            sku = product.skus.find_by_id(params[:sku_id])
            receive = user.receives.where(id: params[:addr_id]).first
            if user.blank? || product.blank? || sku.blank? || receive.blank?
              # 判断库存是否充足
              return {msg: "数据有误！", code: 500, data: {}}
            end
            if sku.sku_quantity < 1 || params[:number].to_i < 1 || params[:number].to_i > sku.sku_quantity
              return {msg: "库存小于1或者库存不够！", code: 500, data: {}}
            end
            order = Order.new(count: params[:number])
            order.user = user
            order.receive = receive
            order.user_node = params[:remark]
            order.status = 0
            order.pay_type = 'wxpay'
            order.order_type = :pre_sale if product.is_pre_sale?
            # 构建订单详情
            order_detail = OrderDetail.new
            order_detail.product = product
            order_detail.sku = sku
            order_detail.sku_number = sku.sku_number
            order_detail.skuprice = product.sale_price(sku)
            order_detail.sku_count = params[:number]
            order.order_details << order_detail
            order.crowd_id = params[:crowdId]
            if order.save! && (payment = order.payments.create(amount: order.amount, pay_method: order.pay_type, status: :unpaid))
              prepay_id = order.unified_order
              data = WechatCommon.wxapp_pay_info(prepay_id)
              return {
                status: 201,
                data: data,
                msg: "保存成功"
              }
            else
              return {
                status: 500,
                data: {},
                msg: "保存失败"
              }
            end
          end
        end
      end

      desc "更新订单"
      params do
        requires :token, type: String, desc: "token值"
        optional :addr_id, type: Integer, desc: "地址id"
      end
      put ":id" do
        if (user = User.user_status.where(authentication_token: params[:token]).first)
          order = Order.find(params[:id])
          receive = user.receives.where(id: params[:addr_id]).first
          if user.blank? || order.blank? || receive.blank?
            # 判断库存是否充足
            return {msg: "3数据有误！", code: 500, data: {}}
          end
          unless order.status < 2
            return {msg: "订单不能修改", code: 500, data: {}}
          end
          order.receive = receive
          data = {
            order: order
          }
          if order.save
            return {
              status: 201,
              data: data,
              msg: "保存成功"
            }
          else
            {msg: "1数据有误！", code: 500, data: {}}
          end
        end
        return {msg: "2数据有误！", code: 500, data: {}}
      end

      desc "订单详细"
      params do
      end
      before do
        user_authenticate!
      end
      get ":id" do
        order = Order.find(params[:id])
        if order.blank?
          return {msg: "数据有误！", code: 500, data: {}}
        end
        products = []
        (order.order_details || []).each do |det|
          products << {
            cover: det.skupictureurl,
            white_image: det.skupictureurl,
            crowd_image: "",
            name: det.product_name,
            attribute_name: JSON.parse(det.property_name).join("-"),
            price: det.skuprice,
            num: det.sku_count,
            qrcode: det.product.qrcode.to_s
          }
        end

        status_time = order.status

        data = {
          id: order.id,
          username: order.user_nickname,
          name: order.receive_name,
          mobile: order.phone,
          location: order.receive_address,
          status: {code: status_time, desc: order.status_cn},
          product: products,
          pay: {total_price: order.amount, paytype: Order::PAYTYPE[order.pay_type]},
          order: {create_time: order.try(:created_at).strftime("%Y-%m-%d %H:%M:%S"), order_id: order.id, order_sn: order.number, order_exp_sn: order.logistics_number, is_pre_sale: order.pre_sale?},
          can_update: order.status < 2,
          show_order: order.status == 3 || order.status == 2,
          repair: false

        }

        return {msg: "请求成功！", code: 200, data: data}
      end
    end
  end
end
