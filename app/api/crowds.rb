class Crowds < Base
  namespace :react do
    resources :crowds do
      get ":id" do
        crowd = Crowd.find(params[:id])
        if (@product = crowd.product)
          @carousel = @product.product_images
          @orders_count = @product.order_details.count(:sku_count)
          @comment_count = @product.comments.comment_status.size
          @comment_avatar_array = @product.comments.comment_status.map { |c| c.user.try(:avatar) }
          @product_detail = @product.detail
          img_regex = /<img\s+src="(?<url>.*?)"/i
          @product_detail = @product_detail.gsub(img_regex) do |match|
            url = $~[:url]
            match.sub(url, image_processing(url)).to_s
          end

          unless (sku = @product.skus.where(id: crowd.sku_id).first)
            sku = @product.skus.first
          end

          if @product.is_pre_sale?
            user_id = User.find_by(authentication_token: headers["Usertoken"])&.id
            is_sale_status = @product.can_pay?(User.find_by(id: user_id))
          else
            is_sale_status = @product.is_sale
          end
          data = {
            carousel: @carousel.map { |product_image| image_processing(product_image.picture_url) },
            crowd:
              {
                name: crowd.name,
                image: crowd.status_icon,
                is_time_come: crowd.is_time_come,
                count: crowd.count,
                crowd_count: crowd.sale_count,
                start_time: crowd.start_time.blank? ? "" : crowd.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                end_time: crowd.end_time.blank? ? "" : crowd.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                end_status: 1,
                color_value: crowd.color_value
              },
            product:
              {
                name: @product.name,
                before_image: @product.before_image_url,
                share_img_url: @product.aftermarket_url,
                cover: @product.aftermarket_url,
                sale_count: @orders_count,
                buy_comments: @product.button_txt,
                show_comments_count: @product.is_comment,
                show_progress_bar: @product.try(:show_progress_bar),
                status: is_sale_status,
                sku_id: sku.id,
                price: @product.outside_sale_price(sku),
                original_price: @product.origin_sale_price(sku)
              },
            share:
              {
                link: "https://" + request.host_with_port.to_s + "/products/" + @product.id.to_s,
                cover: @product.aftermarket_image,
                title: @product.name,
                subhead: @product.title
              },
            comment:
              {
                count: @comment_count,
                avatar_array: @comment_avatar_array[0..5]
              },
            product_detail: @product_detail
          }
          return {msg: "用户收货列表", code: 200, data: data}
        else
          return {msg: "为空", code: 400, data: {}}
        end
      end
    end
  end
end
