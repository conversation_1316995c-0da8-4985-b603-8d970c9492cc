class Comments < Base
  resources :comments do
    before do
      user_authenticate!
    end
    desc "点赞评论"
    params do
      optional :comment_id, type: String, desc: "评论的id"
    end
    post "star" do
      comment = Comment.comment_status.where(id: params[:comment_id]).first
      if (ul = comment.userlikes.where(user_id: @user.id).first)
        if ul.destroy
          return {msg: "取消成功！", code: 200, data: {}}
        else
          return {msg: "失败！", code: 400, data: {star: ul}}
        end
      else
        ul = Userlike.new
        ul.resoure = comment
        ul.user = @user
        if ul.save
          CommentStarJob.perform_later(@user.id, comment.id)
          return {msg: "点赞成功！", code: 200, data: {star: ul}}
        else
          return {msg: "失败！", code: 400, data: {star: ul}}
        end
      end
    end

    desc "创建评论包含创建孩子接口"
    params do
      optional :comment_id, type: String, desc: "父类评论的id"
      optional :product_id, type: String, desc: "产品的id"
      optional :content, type: String, desc: "评论内容"
      optional :asset_imgs, type: Array, desc: "图片数组"
    end
    post "" do
      if (product = Product.find(params[:product_id]))
        comment = Comment.new(content: params[:content])
        comment.product = product
        comment.user = @user
        (params[:asset_imgs] || []).each do |image|
          asset_img = AssetImg.new
          asset_img.image = image
          comment.asset_imgs << asset_img
        end
        if !params[:comment_id].blank?
          if (parent = Comment.find(params[:comment_id]))
            comment.parent = parent
          end
        end
        if comment.save!
          if comment.parent
            GoodCommentJob.perform_later(comment.parent.user.id, @user.id, comment.id, product.id)
          end
          return {msg: "保存成功！", code: 200, data: {comment: comment}}
        else
          return {msg: "保存不成功！", code: 400, data: {comment: {}}}
        end
      end
    end

    desc "评论详情"
    params do
    end
    get ":id" do
      if (comment = Comment.where(id: params[:id]).first)
        chlid_comment = []
        (comment.comments.comment_status || []).each do |child|
          if child.is_owner
            if child.user != @user
              # 不显示
              next
            end
          end
          chlid_comment << {
            comment_id: child.id,
            member_id: child.user.id,
            content: child.content,
            photos: child.asset_imgs.collect(&:image_url),
            status: child.status,
            create_time: child.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            star: child.userlikes_count,
            is_star: child.userlikes.where(user_id: @user.try(:id)).first ? true : false,
            member: {
              member_id: child.user.id,
              nick_name: child.try(:user).try(:user_name),
              avatar: child.user.try(:avatar)
            }
          }
        end
        data = {
          comment_id: comment.id,
          member_id: comment.user.id,
          content: comment.content,
          photos: comment.asset_imgs.collect(&:image_url),
          status: comment.status,
          create_time: comment.created_at.strftime("%Y-%m-%d %H:%M:%S"),
          count: comment.comments_count, # //回复数
          star: comment.userlikes_count,
          is_star: comment.userlikes.where(user_id: @user.try(:id)).first ? true : false,
          member: {
            member_id: comment.user.id,
            nick_name: comment.user.try(:user_name),
            avatar: comment.user.try(:avatar)
          },
          chlid_comment: chlid_comment
        }
        return {msg: "保存成功！", code: 200, data: {comment: data}}
      else
        return {msg: "详情不正确", code: 400, data: {}}
      end
    end

    desc "删除评论"
    delete ":id" do
      if (comment = Comment.where(id: params[:id], user_id: @user.id).first)
        if comment.destroy
          return {msg: "删除成功！", code: 200, data: {}}
        else
          return {msg: "删除不成功！", code: 400, data: {}}
        end
      else
        return {msg: "删除不成功！", code: 400, data: {}}
      end
    end
  end
end
