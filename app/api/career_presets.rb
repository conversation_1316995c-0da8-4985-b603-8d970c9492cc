class CareerPresets < Base
  namespace :career_presets do
    desc "获取职业预设"
    params do
      requires :deploy_env, type: String, values: CareerPreset.deploy_envs.keys
    end
    get "career_presets" do
      career_presets = CareerPreset.where(deploy_env: params[:deploy_env])
      final_data = []
      career_presets.each do |career_preset|
        tmp_data = {
          img: career_preset.background.url,
          title_zh: career_preset.title_zh,
          desc_zh: career_preset.description_zh,
          title_tw: career_preset.title_tw,
          desc_tw: career_preset.description_tw,
          title_en: career_preset.title_en,
          desc_en: career_preset.description_en,
          title_jp: career_preset.title_ja,
          desc_jp: career_preset.description_ja,
          title_kr: career_preset.title_ko,
          desc_kr: career_preset.description_ko
        }
        tmp_data[:settings] = {}
        career_preset.keycap_configs.each do |keycap_config|
          rt = keycap_config.enable_rt_mode ? "01" : "00"
          trigger_point = keycap_config.trigger_point
          press_trigger_point = keycap_config.enable_rt_mode ? keycap_config.press_trigger_point : 0
          release_trigger_point = keycap_config.enable_rt_mode ? keycap_config.release_trigger_point : 0
          tmp_data[:settings][keycap_config.keycap.position] = [rt, trigger_point, press_trigger_point, release_trigger_point, 0.2]
        end
        final_data << tmp_data
      end
      return {msg: "获取职业预设成功", code: 200, data: final_data}
    end
  end
end
