class Sales < Base
  resources :sales do
    before do
      user_authenticate!
    end
    desc "保存订阅"
    params do
      optional :token, type: String, desc: "用户Token"
      optional :product_id, type: String, desc: "产品的id"
      optional :sku_id, type: String, desc: "产品属性规格"
      optional :flag, type: <PERSON><PERSON><PERSON>, desc: "是否订阅"
    end
    post "" do
      if @product = Product.find_by_id(params[:product_id])
        subs = @user.subscriptions.where(product_id: params[:product_id]).first
        if params[:flag]
          subs = Subscription.find_or_create_by(product_id: params[:product_id], user_id: @user.id, sku_id: params[:sku_id])
          subs.sku_id = params[:sku_id]
          subs.is_subscription = true
        elsif subs = @user.subscriptions.where(product_id: params[:product_id], sku_id: params[:sku_id]).first
          subs.is_subscription = false
        end
        if subs.save
          return {msg: "#{subs.is_subscription ? "已订阅" : "取消"}开售提醒", code: 200, data: {status: subs.is_subscription}}
        else
          return {msg: "后台出问题了", code: 400, data: {}}
        end
      end
    end

    desc "获取订阅状态"
    params do
      optional :product_id, type: String, desc: "产品的id"
      optional :sku_id, type: String, desc: "产品属性规格"
    end
    get "status" do
      if @product = Product.find_by_id(params[:product_id])
        if subs = @user.subscriptions.where(product_id: @product.id).first
          return {msg: "用户开售信息", code: 200, data: {status: subs.is_subscription}}
        end
        return {msg: "用户开售信息", code: 200, data: {status: false}}
      else
        return {msg: "订阅不存在 id 不能为空！", code: 500, data: {}}
      end
    end
  end
end
