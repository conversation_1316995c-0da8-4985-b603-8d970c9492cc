class Concerns < Base
  resources :concerns do
    before do
      user_authenticate!
    end
    desc "用户是否收藏"
    params do
      optional :product_id, type: Integer, desc: "商品的id"
    end
    get "user_goods_concern" do
      if (product = Product.find(params[:product_id]))
        if (assb = @user.assembles.where(product_id: product.id).first)
          return {msg: "请求成功！", code: 200, data: assb.is_like}
        else
          return {msg: "无点赞", code: 200, data: false}
        end
      else
        return {msg: "无产品！", code: 400, data: {}}
      end
    end

    desc "保存收藏"
    params do
      requires :product_id, type: Integer, desc: "商品的id"
    end
    post "" do
      if (product = Product.find(params[:product_id]))
        # 可以优化
        if (assb = @user.assembles.where(product_id: product.id).first)
        else
          assb = Assemble.new
          assb.product = product
          assb.user = @user
        end
        assb.is_like = true
        if assb.save
          return {msg: "请求成功！", code: 200, data: assb}
        else
          return {msg: "保存不成功！", code: 400, data: {}}
        end
      else
        return {msg: "无产品！", code: 400, data: {}}
      end
    end

    desc "取消已收藏"
    params do
      requires :product_id, type: Integer, desc: "商品的id"
    end
    post "delete_id" do
      if Product.find(params[:product_id])
        if (assb = Assemble.where(product_id: params[:product_id], user_id: @user.id).first)
          assb.is_like = false
          if assb.save
            return {msg: "请求成功！", code: 200, data: assb}
          else
            return {msg: "保存不成功！", code: 400, data: {}}
          end
        else
          return {msg: "无收藏！", code: 400, data: {}}
        end
      else
        return {msg: "无产品！", code: 400, data: {}}
      end
    end

    desc "收藏信息"
    params do
      optional :page, type: String, desc: "翻页"
      optional :per, type: String, desc: "每页条数"
    end
    get "" do
      assembles = @user.assembles.where(is_like: true).page(params[:page] || 1).per(params[:per] || 10)
      data = []
      assembles.each do |assem|
        data << {
          id: assem.id,
          product_id: assem.product_id,
          product_name: assem.product.try(:name),
          white_image: assem.product.try(:list_img_url),
          # crowd_image: assem.product.try(:share_img_url),
          concerns_count: assem.product.try(:assembles_count)
        }
      end
      return {msg: "请求成功！", code: 200, data: data}
    end
  end
end
