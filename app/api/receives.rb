class Receives < Base
  resources :receives do
    before do
      user_authenticate!
    end
    desc "用户的所有地址管理"
    params do
      # requires :token, type: String, desc: "用户Token"
    end
    get "" do
      receives = @user.receives
      date = []
      (receives || []).each do |re|
        date << {
          id: re.id,
          nickname: re.name,
          name: re.name,
          province: re.region.try(:parent).try(:parent).try(:name),
          city: re.region.try(:parent).try(:name),
          district: re.region.try(:name),
          region_id: re.region.try(:id),
          address: re.address,
          mobile: re.phone,
          phone: re.phone,
          is_default: re.is_default
        }
      end
      return {msg: "用户收货列表", code: 200, data: {address: date}}
    end

    desc "创建用户收货地址"
    params do
      # requires :token, type: String, desc: "用户Token"
      optional :contact, type: String, desc: "收货人"
      optional :mobile, type: String, desc: "收货人手机"
      # optional :province, type: String, desc: "省"
      #         optional :city, type: String, desc: "城市"
      #         optional :district, type: String, desc: "街道"
      optional :region_id, type: Integer, desc: "最后一层id"
      optional :address, type: String, desc: "地址"
      optional :is_default, type: Boolean, desc: "是否为默认"
    end
    post "" do
      region = Region.where(id: params[:region_id], level: 2).first
      if region.blank?
        return {msg: "新建地址不成功", code: 500, data: {address: {}}}
      end

      re = Receive.new(name: params[:contact], phone: params[:mobile], region_id: region.id, address: params[:address], is_default: params[:is_default])
      re.user = @user
      if re.save
        data = {
          id: re.id,
          nickname: re.name,
          name: re.name,
          province: re.region.try(:parent).try(:parent).try(:name),
          city: re.region.try(:parent).try(:name),
          district: re.region.try(:name),
          region_id: re.region.try(:id),
          address: re.address,
          mobile: re.phone,
          phone: re.phone,
          is_default: re.is_default
        }
        @user.receives.where.not(id: re.id).update_all(is_default: false) if re.is_default
        return {msg: "新建收货地址", code: 200, data: {address: data}}

      else
        return {msg: "新建地址不成功", code: 500, data: {address: {}}}
      end
    end

    desc "更新用户收货地址"
    params do
      # requires :token, type: String, desc: "用户Token"
      optional :contact, type: String, desc: "收货人"
      optional :mobile, type: String, desc: "收货人手机"
      optional :region_id, type: Integer, desc: "最后一层id"
      optional :address, type: String, desc: "地址"
      optional :is_default, type: String, desc: "地址"
    end
    put ":id" do
      re = @user.receives.find(params[:id])
      re.name = params[:contact] unless params[:contact].blank?
      re.phone = params[:mobile] unless params[:mobile].blank?
      region = Region.where(id: params[:region_id]).first
      re.region_id = region.id unless region.blank?
      re.address = params[:address] unless params[:address].blank?
      re.is_default = params[:is_default] unless params[:is_default].blank?
      if re.save
        data = {
          id: re.id,
          nickname: re.name,
          name: re.name,
          province: re.region.try(:parent).try(:parent).try(:name),
          city: re.region.try(:parent).try(:name),
          district: re.region.try(:name),
          region_id: re.region.try(:id),
          address: re.address,
          mobile: re.phone,
          phone: re.phone,
          is_default: re.is_default
        }
        return {msg: "新建收货地址", code: 200, data: {address: data}}
      else
        return {msg: "新建地址不成功", code: 500, data: {address: {}}}
      end
    end

    desc "删除用户的收货地址"
    params do
    end
    delete ":id" do
      re = @user.receives.find(params[:id])
      if re.destroy
        return {msg: "删除成功", code: 200, data: {address: {}}}
      else
        return {msg: "删除不成功", code: 500, data: {address: {}}}
      end
    end
  end
end
