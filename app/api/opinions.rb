# frozen_string_literal: true

class Opinions < Base
  resources :opinions do
    before do
      user_authenticate!
    end

    desc '新建反馈建议/补充说明/取消说明' do
      detail '新建反馈建议/补充说明/取消说明'
      success code: 201, message: '返回格式'
    end
    params do
      requires :opinion_code, type: String, values: %w[0 1 2 3 4 5 6],
                              desc: '举报的类型 0：软件使用 1：物流 2：商品 3：退换货 4：其他 5：补充说明 6：取消说明'
      requires :description, type: String, desc: '描述'
      optional :photos, type: Array, desc: '图片'
      optional :mobile, type: String, desc: '电话'
      optional :parent_opinion_id, type: Integer, desc: '父级反馈建议 ID'
    end
    post '/' do
      if params[:opinion_code] == Opinion.option_types[:cancel_instruction].to_s && params[:parent_opinion_id].blank?
        bad_request!('params parent_opinion_id is missing')
      end
      opinion = Opinion.new(option_type: params[:opinion_code].to_i, detail: params[:description],
                            mobile: params[:mobile], parent_id: params[:parent_opinion_id])

      (params[:photos] || []).each do |photo|
        asset_img = AssetImg.new
        asset_img.image = photo
        opinion.asset_imgs << asset_img
      end

      opinion.user = current_user

      if opinion.save
        created!
      else
        bad_request!('操作失败')
      end
    end

    desc '反馈建议列表' do
      detail '查询反馈建议列表'
      success model: Entities::Opinions::OpinionList, message: '返回格式'
      is_array true
    end
    params do
      requires :status, type: String, values: Opinion.statuses_i18n.keys, desc: "状态: #{Opinion.statuses_i18n}"
      use :paginate
    end
    get '/' do
      present paginate(current_user.opinions.only_parent.send(params[:status]).order(id: :desc)),
              with: Entities::Opinions::OpinionList
    end

    desc '反馈建议详情' do
      detail '查询反馈建议详情'
      success model: Entities::Opinions::OpinionDetail, message: '返回格式'
    end
    params do
      requires :id, type: String, desc: '反馈建议id'
      use :paginate
    end
    get ':id' do
      opinion = current_user.opinions.only_parent.find_by(id: params[:id])
      not_found! if opinion.blank?

      final_child_opinions = opinion.child_opinions.order(id: :desc).to_a << opinion
      present opinion, with: Entities::Opinions::OpinionDetail, child_opinions: paginate(final_child_opinions)
    end
  end
end
