class OperationController < ActionController::Base
  include Pundit::Authorization
  include Pagy::Backend
  skip_before_action :verify_authenticity_token
  before_action :authenticate_admin_user!
  before_action :admin_required
  before_action :check_ip_address
  after_action :verify_authorized

  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized

  private

  def admin_required
    user = current_admin_user

    if user == nil
      return render plain: '401', :status => 401, :layout => false
    else
      cookies[:token] = '0bd52d30eb61ca0d41200957c9c3fe57'
    end
  end

  def current_user
    current_admin_user
  end

  def user_not_authorized
    render "operation/exceptions/forbidden"
  end

  def check_ip_address
    Rails.logger.error "request.remote_ip: #{request.remote_ip}"

    if current_user && !current_user.is_admin? && current_user.login_logs.find_by(is_forbidden: true, ip: request.remote_ip)
      render "operation/exceptions/forbidden"
    end
  end
end
