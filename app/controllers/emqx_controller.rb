class EmqxController < ActionController::Base
  skip_before_action :verify_authenticity_token

  # 使用 Redis 替代内存哈希
  REDIS_ONLINE_DEVICES_KEY = "emqx:online_devices"

  def events
    event = params[:event] || params[:type]
    client_id = params[:clientid] || params.dig(:clientinfo, :clientid)

    return head :bad_request unless client_id.present?

    # device_id, session_id = client_id.split("_", 2)

    case event
    when "client.connected"
      ConnectionLog.create!(
        device_id: client_id, # 这里只是用 uuid 替代 device_id
        status: "connected",
        timestamp: Time.current
      )
      # handle_connect(device_id, session_id)
    when "client.disconnected"
      ConnectionLog.create!(
        device_id: client_id,
        status: "disconnected",
        timestamp: Time.current
      )
      # handle_disconnect(device_id, session_id)
    end

    render json: { status: "ok" }
  end

  private

  def redis
    @redis ||= Redis.new # 可根据需要配置连接参数
  end

  def handle_connect(device_id, session_id)
    old_session = redis.hget(REDIS_ONLINE_DEVICES_KEY, device_id)
    puts "old_session: #{old_session}"
    puts "session_id: #{session_id}"
    if old_session && old_session != session_id
      Rails.logger.info "Duplicate login: kicking old session #{old_session}"

      # 踢掉旧 session
      publish_kickout(device_id, old_session)
    end

    redis.hset(REDIS_ONLINE_DEVICES_KEY, device_id, session_id)
  end

  def handle_disconnect(device_id, session_id)
    puts "handle_disconnect: #{device_id} #{session_id}"
    if redis.hget(REDIS_ONLINE_DEVICES_KEY, device_id) == session_id
      redis.hdel(REDIS_ONLINE_DEVICES_KEY, device_id)
    end
  end

  def publish_kickout(device_id, session_id)
    require 'mqtt'

    Thread.new do
      begin
        MQTT::Client.connect(host: 'localhost', port: 1883) do |client|
          topic = "kickout/#{device_id}"
          client.publish(topic, session_id)
        end
      rescue => e
        Rails.logger.error "MQTT publish failed: #{e.message}"
      end
    end
  end
end
