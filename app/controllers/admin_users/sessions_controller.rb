# frozen_string_literal: true

class AdminUsers::SessionsController < Devise::SessionsController
  after_action :record_login_logs, only: [:create]

  # GET /resource/sign_in
  # def new
  #   super
  # end

  # POST /resource/sign_in
  def create
    super
  end

  # DELETE /resource/sign_out
  # def destroy
  #   super
  # end

  protected

  # If you have extra params to permit, append them to the sanitizer.
  def record_login_logs
    begin
      remote_ip = request.env['HTTP_X_FORWARDED_FOR']
      result = JSON.parse(`curl --location 'http://opendata.baidu.com/api.php?query=#{remote_ip}&co=&resource_id=6006&oe=utf8'`)
      login_logs = current_admin_user.login_logs
      if (login_log = login_logs.find_by(ip: remote_ip))
        login_log.update(login_at: Time.current, is_lastest: true)
      else
        login_log = login_logs.create(ip: remote_ip, login_at: Time.current, address: result["data"].first["location"], is_lastest: true)
      end

      login_logs.where.not(id: login_log.id).update_all(is_lastest: false)
    rescue => exception
      Rails.logger.error("record_login_logs error: #{exception.message}")
    end
  end
end
