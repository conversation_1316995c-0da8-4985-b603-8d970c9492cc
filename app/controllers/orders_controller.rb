class OrdersController < ActionController::Base
  skip_before_action :verify_authenticity_token

  def index
    render :json => "pingxx-message"
  end

  def create
    if Pingpp::Webhook.verify?(request) # 验证回调
      params_post = JSON.parse(request.raw_post)
      if params_post["type"] == "charge.succeeded"
        order_no = params_post["data"]["object"]["order_no"]
        if order = Order.where(number: order_no, status: 0).first
          order.pay!
        end
      elsif params_post["type"] == "refund.succeeded"
        charge_number = params_post["data"]["object"]["charge"]
        if Order.refund_succeeded(charge_number) == false
          Rails.logger.error "退款成功，但是更新订单状态失败，charge_number: #{charge_number}"
        end
      elsif params_post["type"] == "refund.failed"
        charge_number = params_post["data"]["object"]["charge"]
        failure_msg = params_post["data"]["object"]["failure_msg"]
        Order.refund_failed(charge_number, failure_msg)
      end
    end
    render :json => "pingxx-message"
  end

  # 微信小程序付款成功通知
  def wechat_notify
    notify_result = Hash.from_xml(request.body.read)["xml"]
    Rails.logger.error notify_result
    params_sign = notify_result.delete("sign")
    right_sign = WechatCommon.computational_signature(notify_result)[:sign]
    @return_code = notify_result.present? ? "SUCCESS" : "FAIL"
    order = Order.where(number: notify_result["out_trade_no"], status: 0).first

    if notify_result["result_code"] == "SUCCESS" && order.present? && (params_sign == right_sign)
      order.update!(charge_number: notify_result["transaction_id"])
      order.payments.last.update!(trade_no: notify_result["transaction_id"], pay_at: Time.zone.now, status: :paid)
      order.pay!
    end

    notify_result = {return_code: @return_code}
    render xml: notify_result.to_xml(root: "xml", :skip_instruct => true, dasherize: false)
  end

  def wechat_refund_notify
    params_result = Hash.from_xml(request.body.read)["xml"]
    Rails.logger.error params_result
    xml_result = WechatCommon.decrypt_aes_256_ecb(params_result["req_info"])
    notify_result = Hash.from_xml(xml_result)["root"]
    Rails.logger.error notify_result
    @return_code = notify_result.present? ? "SUCCESS" : "FAIL"
    order = Order.where(number: notify_result["out_refund_no"].slice!(0..-2)).first

    if params_result["return_code"] == "SUCCESS" && order.present? && (order.status != 6)
      charge_number = notify_result["transaction_id"]
      if notify_result["err_code"].blank?
        charge_number = notify_result["transaction_id"]
        if Order.refund_succeeded(charge_number) == false
          Rails.logger.error "退款成功，但是更新订单状态失败，charge_number: #{charge_number}"
        end
      else
        failure_msg = notify_result["err_code_des"]
        Order.refund_failed(charge_number, failure_msg)
      end
    end
    notify_result = {return_code: @return_code}
    render xml: notify_result.to_xml(root: "xml", :skip_instruct => true, dasherize: false)
  end

  def create_update
    order = Order.find(params[:order_id])
    if order.update_columns(admin_node: params[:admin_node])
      render json: { status: 200, data: {}, msg: "有异常" } and return
    else
      render json: { status: 201, data: {}, msg: "有异常" } and return
    end
  end
end
