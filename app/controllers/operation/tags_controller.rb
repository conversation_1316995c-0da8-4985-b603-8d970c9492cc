class Operation::TagsController < OperationController
  before_action do
    authorize Tag
  end

  def index
    @tags = Tag.all
    @pagy, @tags = pagy(@tags)
  end

  def new
    @tag = Tag.new
  end

  def create
    @tag = Tag.new(tag_params)
    if @tag.save
      redirect_to operation_tags_path, :notice => "创建成功！"
    else
      redirect_to operation_tags_path, :notice => "创建不成功！"
    end
  end

  private

  def tag_params
    params.require(:tag).permit(:name, :que)
  end
end
