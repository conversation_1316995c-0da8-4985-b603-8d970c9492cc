module Operation
  class BannersController < OperationController
    before_action do
      authorize Banner
    end

    def index
      @q = Banner.all.order(id: :desc).ransack(params[:q])
      @q.sorts = 'que asc' if @q.sorts.empty?
      @all_banners = @q.result(distinct: true)
      @pagy, @banners = pagy(@all_banners)
    end

    def new
      @banner = Banner.new
    end

    def create
      @banner = Banner.new(banner_params)
      @banner.que = 1
      if @banner.save
        redirect_to operation_banners_path, :notice => "广告创建成功！"
      else
        respond_to do |format|
          format.turbo_stream { render action: :edit }
        end
      end

    end

    def edit
      @banner = Banner.find(params[:id])
    end

    def update
      @banner = Banner.find(params[:id])
      if @banner.update(banner_params)
        redirect_to operation_banners_path, :notice => "广告更新成功！"
      else
        respond_to do |format|
          format.turbo_stream { render action: :edit }
        end
      end
    end

    def show_status
      if (@banner = Banner.find(params[:id]))
        is_closing = !@banner.is_closing
        if @banner.update(is_closing: is_closing)
          render json: {status: 200, data: {}, msg: "保存成功"} and return
        else
          render json: {status: 500, data: {}, msg: "保存失败"} and return
        end
      end
    end

    def update_que
      @banner = Banner.find(params[:id])
      if @banner.insert_at(params[:banner][:que].to_i)
        redirect_to params[:origin_url], :notice => "更新成功！"
      else
        redirect_to params[:origin_url], :notice => "更新失败！"
      end
    end

    private

    def banner_params
      params.require(:banner).permit(:is_closing, :image, :picture_url, :title, :product_id, :crowd_id,
        :content, :click_count, :color_value, :banner_type)
    end
  end
end