class Operation::ManageRolesController < OperationController
  before_action do
    authorize ManageRole
  end

  before_action :set_operation_manage_role, only: %i[ show destroy ]

  # GET /operation/manage_roles or /operation/manage_roles.json
  def index
    @operation_manage_roles = ManageRole.all
    @pagy, @operation_manage_roles = pagy(@operation_manage_roles)
  end

  # GET /operation/manage_roles/1 or /operation/manage_roles/1.json
  def show
    @tmp_hash = {}
    ManageController.all.each do |manage_controller|
      @tmp_hash[manage_controller.word] = manage_controller.manage_actions.ids&.map { |x| x.to_s }
    end
  end

  # POST /operation/manage_roles or /operation/manage_roles.json
  def create
    @operation_manage_role = ManageRole.new(operation_manage_role_params)

    respond_to do |format|
      if @operation_manage_role.save
        format.html { redirect_to operation_manage_role_url(@operation_manage_role), notice: "角色创建成功, 请配置权限" }
        format.json { render :show, status: :created, location: @operation_manage_role }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @operation_manage_role.errors, status: :unprocessable_entity }
      end
    end
  end

  def update_auths
    @manage_role = ManageRole.find(params[:id])
    params[:manage_action_ids].each do |manage_action_id|
      ManageRoleAction.find_or_create_by(manage_role_id: @manage_role.id, manage_action_id: manage_action_id)
    end
    @manage_role.manage_role_actions.where.not(manage_action_id: params[:manage_action_ids]).destroy_all
  end

  # DELETE /operation/manage_roles/1 or /operation/manage_roles/1.json
  def destroy
    @operation_manage_role.destroy

    respond_to do |format|
      format.html { redirect_to operation_manage_roles_url, notice: "角色删除成功" }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_operation_manage_role
      @operation_manage_role = ManageRole.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def operation_manage_role_params
      params.require(:manage_role).permit(:name)
    end
end
