module Operation
  class UsersController < OperationController
    before_action do
      authorize User
    end

    def index
      @q = User.ransack(params[:q])
      users = @q.result(distinct: true)
      if params[:seach_name].present?
        users = users.where("phone like ?  or username like ?", "%#{params[:seach_name]}%", "%#{params[:seach_name]}%")
        if params[:seach_name].to_i > 0
          users += User.where(id: params[:seach_name])
          users = User.where(id: users.pluck(:id))
        end
      elsif params[:seach_id].present?
        users = users.where(id: params[:seach_id])
      end
      @pagy, @users = pagy(users.where.not(phone: nil).order(id: :desc))
    end

    def users_status
      if @user = User.find(params[:user_id])
        status = !@user.status
        if @user.update_columns(status: status)
          render json: { status: 200, data: {}, msg: "保存成功" } and return
        else
          render json: { status: 500, data: {}, msg: "保存成功" } and return
        end
      end
    end
  end
end
