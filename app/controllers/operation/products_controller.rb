class Operation::ProductsController < OperationController
  before_action do
    authorize Product
  end

  before_action :set_product, only: [:edit, :update, :destroy, :update_que]

  def index
    @tags = Tag.order(que: :asc).pluck(:name, :id)
    @q = Product.ransack(params[:q])
    @q.sorts = 'que asc' if @q.sorts.empty?
    products = @q.result
    @pagy, @products = pagy(products)
  end

  def new
    @product = Product.new
  end

  def create
    @product = Product.new(product_params)
    @product.que = 1
    if @product.save
      redirect_to operation_products_path, notice: "商品创建成功！"
    else
      render action: :new
    end
  end

  def edit
  end

  def update
    if @product.update!(product_params)
      redirect_to operation_products_path, notice: "商品更新成功！"
    else
      respond_to do |format|
        format.turbo_stream { render action: :edit }
      end
    end
  end

  def update_que
    @product.insert_at(params[:product][:que].to_i)
    redirect_to params[:origin_url], notice: "商品更新成功！"
  end

  def destroy
    @product.destroy
    redirect_to operation_products_path, notice: "删除成功！"
  end

  def show_status
    if (@product = Product.find(params[:product_id]))
      on_shelf = !@product.on_shelf
      if @product.update(on_shelf: on_shelf)
        render json: {status: 200, data: {}, msg: "保存成功"} and return
      else
        render json: {status: 500, data: {}, msg: "保存失败"} and return
      end
    end
  end

  def product_status
    if (@product = Product.find(params[:product_id]))
      is_sale = !@product.is_sale
      if @product.update(is_sale: is_sale)
        render json: {status: 200, data: {}, msg: "保存成功"} and return
      else
        render json: {status: 500, data: {}, msg: "保存失败"} and return
      end
    end
  end

  private

  def set_product
    @product = Product.find(params[:id])
  end

  def product_params
    params.require(:product).permit(:aftermarket, :name, :title, :button_txt, :before_image, :share_img_url,
      :list_img_url, :detail, :sale_count, :is_sale, :is_sale_at, :on_shelf, :on_sale, :pre_sale_stage,
      :is_comment, :is_open_comment, :tag_id, :que, :is_pre_sale, :deposit, :is_sale_at_button,
      skus_attributes: [:id, :sku_number, :sku_pictureurl, :sku_markedprice, :sku_price, :sku_quantity, :sku_properties_arrs, :_destroy],
      product_images_attributes: [:id, :picture, :sorting, :_destroy])
  end
end
