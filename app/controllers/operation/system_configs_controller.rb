class Operation::SystemConfigsController < OperationController
  before_action do
    authorize ::SystemConfig
  end

  before_action :set_system_config, only: [:index, :update]

  def index
  end

  def update
    @system_config.update!(system_config_params)

    redirect_to operation_system_configs_path, notice: "更新成功"
  end

  private

  def set_system_config
    @system_config = SystemConfig.first
  end

  def system_config_params
    params.require(:system_config).permit(:auto_refund)
  end
end
