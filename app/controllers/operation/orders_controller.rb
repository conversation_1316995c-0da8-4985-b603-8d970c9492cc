class Operation::OrdersController < OperationController
  helper_method :filter_url

  before_action do
    authorize Order
  end

  def index
    @q = Order.ransack(params[:q])
    orders = @q.result(distinct: true)
    params[:search_q] = params[:q].dup
    params[:search_q][:status_eq] = nil if params[:search_q].present?

    @orders_without_status = Order.ransack(params[:search_q]).result(distinct: true)
    @orders_all = orders
    @pagy, @orders = pagy(@orders_all)

    begin
      respond_to do |format|
        format.html
        format.xlsx {
          authorize(Order, :export?)
          response.headers["Content-Disposition"] = "attachment; filename=订单#{Time.now.strftime("%Y%m%d-%H%M%S")}.xlsx"
        }
      end
    rescue Pundit::NotAuthorizedError => err
      redirect_to operation_orders_path, notice: "没有权限！"
    rescue err
      redirect_to operation_orders_path, notice: "订单导出失败！"
    end
  end

  def show
    @order = Order.find(params[:id])
  end

  def refunds
    @order = Order.find(params[:order_id])
    result = @order.refund
    if result[:success]
      render json: {status: 200, data: {}, msg: "退款成功"} and return
    else
      render json: {status: 500, data: result[:reason], msg: "退款失败"} and return
    end
  end

  def cancel_refund
    @order = Order.find(params[:id])
    origin_status = @order.audits.where(comment: "after_sale").last.audited_changes["status"].first
    if @order.update(status: origin_status)
      @order.after_sale.destroy!
      redirect_to operation_order_path(@order), notice: "取消退款成功！"
    else
      redirect_to operation_order_path(@order), notice: "取消退款失败！"
    end
  end

  def filter_url
    url = request.url
    uri = URI.parse(url)
    query_params = URI.decode_www_form(uri.query || '')

    filtered_params = query_params.reject { |param| param[0] == 'q[status_eq]' }

    filtered_url = uri.dup
    filtered_url.query = URI.encode_www_form(filtered_params)

    filtered_url.to_s
  end
end
