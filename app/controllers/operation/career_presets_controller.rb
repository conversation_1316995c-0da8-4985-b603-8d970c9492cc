
class Operation::CareerPresetsController < OperationController
  before_action do
    authorize CareerPreset
  end

  before_action :set_career_preset, only: [:show, :edit, :update, :destroy]

  # GET /operation/career_presets
  # GET /operation/career_presets.json
  def index
    @career_presets = CareerPreset.all
  end

  # GET /operation/career_presets/1
  # GET /operation/career_presets/1.json
  def show
  end

  # GET /operation/career_presets/new
  def new
    @career_preset = CareerPreset.new
  end

  # GET /operation/career_presets/1/edit
  def edit
  end

  # POST /operation/career_presets
  # POST /operation/career_presets.json
  def create
    @career_preset = CareerPreset.new(career_preset_params)

    respond_to do |format|
      if @career_preset.save
        format.html { redirect_to operation_career_presets_url, notice: '职业预设创建成功' }
        format.json { render action: 'show', status: :created, location: @career_preset }
      else
        format.html { render action: 'new' }
        format.json { render json: @career_preset.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /operation/career_presets/1
  # PATCH/PUT /operation/career_presets/1.json
  def update
    respond_to do |format|
      if @career_preset.update(career_preset_params)
        format.html { redirect_to operation_career_presets_url, notice: '职业预设更新成功' }
        format.json { head :no_content }
      else
        format.html { render action: 'edit' }
        format.json { render json: @career_preset.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /operation/career_presets/1
  # DELETE /operation/career_presets/1.json
  def destroy
    @career_preset.destroy
    respond_to do |format|
      format.html { redirect_to operation_career_presets_url, notice: '职业预设删除成功' }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_career_preset
      @career_preset = CareerPreset.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def career_preset_params
      params.require(:career_preset).permit(:background, :title_zh, :description_zh, :title_en, :description_en,
        :title_tw, :description_tw, :title_ja, :description_ja, :title_ko, :description_ko, :deploy_env,
        keycap_configs_attributes: [:id, :keycap_id, :enable_rt_mode, :trigger_point, :press_trigger_point, :release_trigger_point, :_destroy])
    end
end
