module Operation
  class CommentsController < OperationController
    before_action do
      authorize Comment
    end

    before_action :set_product
    before_action :store_origin_url, only: [:index]

    def index
      sort = params[:sort] || "hot"

      comments = case sort
      when "hot"
        @product.comments.where(parent_id: nil).reorder(userlikes_count: :desc, id: :desc)
      when "time"
        @product.comments.where(parent_id: nil).reorder(created_at: :desc, id: :desc)
      when "photos"
        @product.comments.where(parent_id: nil).reorder(asset_imgs_count: :desc, id: :desc)
      end
      @comments = comments.includes(:user, :asset_imgs, :parent)
      # @pagy, @comments = pagy(comments)
    end

    def update
      @comment = Comment.find(params[:id])
      case params[:status]
      when "normal"
        @comment.update(is_owner: false, status: true)
      when "visible_oneself"
        @comment.update(is_owner: true, status: true)
      when "ban"
        @comment.update(is_owner: false, status: false)
      end
      redirect_to session[:origin_url] || operation_product_comments_path(@product), notice: "操作成功"
    end

    private

    def set_product
      @product = Product.find(params[:product_id])
    end

    def store_origin_url
      session[:origin_url] = request.original_url
    end
  end
end
