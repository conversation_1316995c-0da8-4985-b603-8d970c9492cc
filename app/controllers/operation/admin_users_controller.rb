module Operation
  class AdminUsersController < OperationController
    before_action do
      authorize AdminUser
    end

    def index
      @pagy, @admin_users = pagy(AdminUser.all)
    end

    def new
      @admin_user = AdminUser.new
    end

    def create
      @admin_user = AdminUser.new(user_params)
      if @admin_user.save!
        redirect_to operation_admin_users_path, :notice => "创建成功！"
      else
        redirect_to operation_admin_users_path, :notice => "创建不成功！"
      end
    end

    def update
      @admin_user = AdminUser.find(params[:id])
      @admin_user.name = params[:admin_user][:name]
      @admin_user.account = params[:admin_user][:account]
      @admin_user.password = params[:admin_user][:password] if params[:admin_user][:password].present?
      @admin_user.manage_roles = ManageRole.where(id: params[:admin_user][:manage_role_ids]) if params[:admin_user][:manage_role_ids].present?

      if @admin_user.save!
        redirect_to operation_admin_users_path, :notice => "更新成功！"
      else
        redirect_to operation_admin_users_path, :notice => "更新失败！"
      end
    end

    def destroy
      @admin_user = AdminUser.find(params[:id])
      if @admin_user.destroy
        redirect_to operation_admin_users_path, :notice => "删除成功！"
      else
        redirect_to operation_admin_users_path, :notice => "删除不成功！"
      end
    end

    private

    def user_params
      params.require(:admin_user).permit(:name, :account, :password)
    end
  end
end