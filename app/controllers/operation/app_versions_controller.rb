module Operation
  class AppVersionsController < OperationController
    before_action do
      authorize AppVersion
    end

    def index
      @versions = AppVersion.where(ver_type: ["1", "2"])
      @pagy, @versions = pagy(@versions)
    end

    def edit
      @version = AppVersion.find(params[:id])
    end

    def update
      @version = AppVersion.find(params[:id])
      if @version.update(version_params)
        redirect_to operation_app_versions_path, :notice => "更新版本成功！"
      else
        redirect_to operation_app_versions_path, :notice => "更新版本不成功！"
      end
    end

    private

    def version_params
      params.require(:app_version).permit(:ver_type, :version, :node, :file_load, :title)
    end
  end
end