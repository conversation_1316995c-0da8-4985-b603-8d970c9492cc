module Operation
  class DevicesController < OperationController
    before_action do
      authorize <PERSON><PERSON>
    end

    before_action :set_device, only: [:edit, :update, :destroy]

    def index
      @pagy, @devices = pagy(Device.all)
    end

    def new
      @device = Device.new
    end

    def create
      @device = Device.new(device_params)
      if @device.save
        redirect_to operation_devices_path, notice: "设备创建成功"
      else
        render :new
      end
    end

    def edit
    end

    def update
      if @device.update(device_params)
        redirect_to operation_devices_path, notice: "设备更新成功"
      else
        render :edit
      end
    end

    def destroy
      if @device.destroy
        redirect_to operation_devices_path, notice: "设备删除成功"
      else
        redirect_to operation_devices_path, alert: "设备删除失败"
      end
    end

    private

    def set_device
      @device = Device.find(params[:id])
    end

    def device_params
      params.require(:device).permit(:name, :pid, :dfu_pid)
    end
  end
end
