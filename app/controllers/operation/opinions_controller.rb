module Operation
  class OpinionsController < OperationController
    before_action do
      authorize Opinion
    end

    before_action :set_list, only: [:index]

    def index
      @q = Opinion.where(parent_id: nil).ransack(params[:q])
      opinions = @q.result.order(id: :desc)
      @pagy, @opinions = pagy(opinions, items: 5)
    end

    def update_right_reply
      @opinion = Opinion.find(params[:id])
      if params[:detail]
        @opinion.processing!
        @opinion.child_opinions.create!(user_id: Settings.official_user_id, detail: params[:detail], option_type: 5)
        @opinion.user.notifications.create(send_user_id: Settings.official_user_id, notify_type: :reply_opinion, content: params[:detail], target: @opinion)
        send_notification_jpush_mess("#{User.find(Settings.official_user_id).user_name} 回复了你的反馈", 'default', @opinion.user.register_id) rescue ''
      elsif params[:status]
        @opinion.update(status: params[:status])
      end
    end

    # 标记
    def mark
      @opinion = Opinion.find(params[:id])
      is_mark = params[:is_mark] == "true" ?  false : true
      @opinion.update(is_mark: is_mark)
      render json: {status: 200, is_mark: @opinion.is_mark}
    end

    private

    def set_list
      opinions = Opinion.pending.where(parent_id: nil).order(id: :desc)
      @pending_pagy, @pending_opinions = pagy(opinions, items: 5, page_param: :page_pending)

      opinions = Opinion.processing.where(parent_id: nil).order(id: :desc)
      @processing_pagy, @processing_opinions = pagy(opinions, items: 5, page_param: :page_processing)
    end
  end
end
