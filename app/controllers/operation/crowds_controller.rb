module Operation
  class CrowdsController < OperationController
    before_action do
      authorize Crowd
    end

    def index
      @pagy, @crowds = pagy(Crowd.all)
    end

    def create
      @crowd = Crowd.new(crowd_params)
      if @crowd.save
        notice = '创建成功!'
      else
        notice = '创建不成功!'
      end
      redirect_to operation_crowds_path, notice: notice
    end

    def update
      @crowd = Crowd.find(params[:id])
      if @crowd.update(crowd_params)
        notice = '更新成功!'
      else
        notice = '更新失败!'
      end
      respond_to do |format|
        format.turbo_stream { render action: :update_success }
        format.html { redirect_to operation_crowds_path, notice: notice }
      end
    end

    def destroy
      @crowd = Crowd.find(params[:id])
      if @crowd.destroy
        notice = '删除成功!'
      else
        notice = '删除失败!'
      end
      redirect_to operation_crowds_path, notice: notice
    end

    private

    def crowd_params
      params.require(:crowd).permit(:color_value, :product_id, :is_time_come, :crowd_status_id, :count)
    end
  end
end