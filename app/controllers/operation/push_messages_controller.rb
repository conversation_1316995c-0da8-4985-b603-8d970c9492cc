# frozen_string_literal: true

module Operation
  # asdasd
  class PushMessagesController < OperationController
    before_action do
      authorize PushMessage
    end
    before_action :set_push_message, only: %i[show edit update destroy revoke]

    # GET /operation/push_messages
    # GET /operation/push_messages.json
    def index
      @push_messages = PushMessage.order(id: :desc)
    end

    # GET /operation/push_messages/1
    # GET /operation/push_messages/1.json
    def show; end

    # GET /operation/push_messages/new
    def new
      @push_message = PushMessage.new
    end

    # GET /operation/push_messages/1/edit
    def edit; end

    # POST /operation/push_messages
    # POST /operation/push_messages.json
    def create
      @push_message = PushMessage.new(push_message_params)

      respond_to do |format|
        if @push_message.save
          format.html { redirect_to [:operation, @push_message], notice: '推送通知创建成功' }
          format.json { render action: 'show', status: :created, location: @push_message }
        else
          format.html { render action: 'new' }
          format.json { render json: @push_message.errors, status: :unprocessable_entity }
        end
      end
    end

    # PATCH/PUT /operation/push_messages/1
    # PATCH/PUT /operation/push_messages/1.json
    def update
      respond_to do |format|
        if @push_message.update(push_message_params)
          format.html { redirect_to [:operation, @push_message], notice: '推送通知更新成功' }
          format.json { head :no_content }
        else
          format.html { render action: 'edit' }
          format.json { render json: @push_message.errors, status: :unprocessable_entity }
        end
      end
    end

    # DELETE /operation/push_messages/1
    # DELETE /operation/push_messages/1.json
    def destroy
      @push_message.destroy
      respond_to do |format|
        format.html { redirect_to operation_push_messages_url, notice: '推送通知删除成功' }
        format.json { head :no_content }
      end
    end

    def template_content
      push_message = PushMessage.find_by(id: params[:push_message_id])
      select_product_name = Product.find_by(id: params[:select_product_id])&.name
      push_template_content = PushMessage.push_template(select_product_name, params[:select_template_id], push_message) || {}
      crowd_count_hash = {
        subscription_count: Subscription.where(product_id: params[:select_product_id]).count,
        collect_count: Assemble.where(product_id: params[:select_product_id]).count,
      }
      render json: push_template_content.merge(crowd_count_hash)
    end

    # 撤销
    def revoke
      @push_message.update(status: :revoked)
      redirect_to operation_push_messages_url, notice: '推送通知撤销成功'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_push_message
      @push_message = PushMessage.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def push_message_params
      params.require(:push_message).permit(:title, :include_sms_message, :include_jpush, :template_id, :product_id,
                                           :template_content, :msg_id, :pushed_at, :expected_push_count,
                                           :actual_push_count, :timing, :status, crowd: [])
    end
  end
end
