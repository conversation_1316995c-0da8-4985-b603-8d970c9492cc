module Operation
  class CrowdStatusesController < OperationController
    before_action do
      authorize CrowdStatus
    end

    before_action :set_operation_crowd_status, only: %i[show edit update destroy]

    # GET /operation/crowd_statuses or /operation/crowd_statuses.json
    def index
      @crowd_statuses = CrowdStatus.order(:que)
      @pagy, @crowd_statuses = pagy(@crowd_statuses)
    end

    # POST /operation/crowd_statuses or /operation/crowd_statuses.json
    def create
      @crowd_status = CrowdStatus.new(operation_crowd_status_params)
      redirect_to operation_crowd_statuses_url, notice: '创建成功' if @crowd_status.save
    end

    # PATCH/PUT /operation/crowd_statuses/1 or /operation/crowd_statuses/1.json
    def update
      redirect_to operation_crowd_statuses_url, notice: '更新成功' if @crowd_status.update(operation_crowd_status_params)
    end

    # DELETE /operation/crowd_statuses/1 or /operation/crowd_statuses/1.json
    def destroy
      @crowd_status.destroy

      respond_to do |format|
        format.html { redirect_to operation_crowd_statuses_url, notice: '删除成功!' }
        format.json { head :no_content }
      end
    end

    def update_que
      params[:sort].each_value do |crowd_status_sort|
        CrowdStatus.find(crowd_status_sort['id']).update_columns(que: crowd_status_sort['que'])
      end
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_operation_crowd_status
      @crowd_status = CrowdStatus.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def operation_crowd_status_params
      params.require(:crowd_status).permit(:name, :icon, :que)
    end
  end
end
