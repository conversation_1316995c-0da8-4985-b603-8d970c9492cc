class Operation::FirmwaresController < OperationController
  before_action do
    authorize Firmware
  end

  before_action :set_firmware, only: [:show, :edit, :update, :destroy]

  # GET /operation/firmwares
  # GET /operation/firmwares.json
  def index
    @devices = Device.all.pluck(:name, :pid)

    # 构建查询条件
    firmwares = Firmware.order(id: :desc)

    # 设备筛选 (device_id存储的是pid值)
    if params[:device_id].present?
      firmwares = firmwares.where(device_id: params[:device_id])
    end

    # 版本搜索
    if params[:version].present?
      firmwares = firmwares.where("version LIKE ?", "%#{params[:version]}%")
    end

    # 环境筛选
    if params[:deploy_env].present?
      firmwares = firmwares.where("JSON_CONTAINS(deploy_env, ?)", "\"#{params[:deploy_env]}\"")
    end

    @pagy, @firmwares = pagy(firmwares, items: params[:items] || 10)
  end

  # GET /operation/firmwares/1
  # GET /operation/firmwares/1.json
  # def show
  # end

  # GET /operation/firmwares/new
  def new
    @firmware = Firmware.new
  end

  # GET /operation/firmwares/1/edit
  def edit
  end

  # POST /operation/firmwares
  # POST /operation/firmwares.json
  def create
    @firmware = Firmware.new(firmware_params)

    respond_to do |format|
      if @firmware.save
        format.html { redirect_to operation_firmwares_url, notice: '固件创建成功' }
      else
        format.html { render action: 'new' }
      end
    end
  end

  # PATCH/PUT /operation/firmwares/1
  # PATCH/PUT /operation/firmwares/1.json
  def update
    respond_to do |format|
      if @firmware.update(firmware_params)
        format.html { redirect_to operation_firmwares_url, notice: '固件更新成功' }
      else
        format.html { render action: 'edit' }
      end
    end
  end

  # DELETE /operation/firmwares/1
  # DELETE /operation/firmwares/1.json
  def destroy
    @firmware.destroy
    respond_to do |format|
      format.html { redirect_to operation_firmwares_url, notice: '固件删除成功' }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_firmware
      @firmware = Firmware.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def firmware_params
      params.require(:firmware).permit(:version, :device_id, :force_update, :file, :title_zh, :description_zh, :title_en,
      :description_en, :title_tw, :description_tw, :title_ja, :description_ja, :title_ko, :description_ko, deploy_env: [])
    end
end
