module Operation
  class LogsController < OperationController
    before_action do
      authorize "Log", policy_class: LogPolicy
    end

    def login_logs
      @login_logs = LoginLog.order(login_at: :desc)
    end

    def forbidden_address
      @login_log = LoginLog.find_by(id: params[:id])
      @login_log.update(is_forbidden: !@login_log.is_forbidden)
      render json: { status: 200, message: "操作成功" }
    end
  end
end

