class Operation::MainController < OperationController
  before_action do
    authorize "Main", policy_class: MainPolicy
  end

  def dashboard
    # 用户分析
    @date_times = []
    @users_count = []
    @active_users_count = []
    @users_sale_amount = []

    (0..29).to_a.reverse.each do |number|
      @date_times << (Time.current - number.days).to_date.to_s
      @users_count << User.where(created_at: (Time.current - number.days).all_day).where.not(phone: nil).count
      @active_users_count << UserStatistic.find_by(stat_type: :active, created_on: (Time.current - number.days).to_date).try(:total_count).to_i
    end

    @subscriptions_count = []
    (0..29).to_a.reverse.each do |number|
      @subscriptions_count << Subscription.valid.where(updated_at: (Time.current - number.days).all_day).count
    end

    @pre_sales_count = []
    (0..29).to_a.reverse.each do |number|
      @pre_sales_count << Order.where(created_at: (Time.current - number.days).all_day).paid.pre_sale.joins(:payments)
                               .group('orders.id').having('COUNT(payments.id) = 1').count.count
    end

    @today_users_count = User.today.where.not(phone: nil).count
    @yesterday_users_count = User.yesterday.where.not(phone: nil).count

    @users_count_status = if @today_users_count > @yesterday_users_count
                            'ri-arrow-up-fill'
                          elsif @today_users_count < @yesterday_users_count
                            'ri-arrow-down-fill'
                          else
                            'ri-subtract-fill'
                          end

    @today_sale_amount = Order.where(created_at: Time.current.all_day).paid.sum(:amount).to_i
    @today_sale_refunding_amount = Order.where(created_at: Time.current.all_day).refunding.sum(:amount).to_i
    @yesterday_sale_amount = Order.where(created_at: (Time.current - 1.day).all_day).paid.sum(:amount).to_i

    @sale_amount_status = if @today_sale_amount > @yesterday_sale_amount
                            'ri-arrow-up-fill'
                          elsif @today_sale_amount < @yesterday_sale_amount
                            'ri-arrow-down-fill'
                          else
                            'ri-subtract-fill'
                          end
  end

  def subscription_statistic
    sleep 1 if params[:search].blank?
    @result = []
    search_key = params[:search] || 'today'
    subscriptions = case search_key
    when 'today'
      Subscription.valid.where(updated_at: Time.current.all_day)
    when 'last_7_days'
      Subscription.valid.where(updated_at: (Time.current - 7.days)..Time.current)
    when 'last_30_days'
      Subscription.valid.where(updated_at: (Time.current - 30.days)..Time.current)
    when 'last_60_days'
      Subscription.valid.where(updated_at: (Time.current - 60.days)..Time.current)
    end

    subscription_count = subscriptions.group(:product_id).count
    subscription_count = subscription_count.sort_by { |_, count| count }.reverse.to_h

    product_ids = subscription_count.keys
    products = Product.where(id: product_ids)
    product_ids.each do |product_id|
      product = products.map { |p| p if p.id == product_id }.compact.first
      @result << {
        product_name: product.name,
        product_img_url: product.list_img_url,
        subscriptions_count: subscription_count[product_id],
      }
    end
  end

  def sale_count_statistic
    @users_sale_amount = []
    @date_times = []
    @users_count = []
    @average_user_sale = []

    search_key = params[:search] || 'last_30_days'
    days = case search_key
    when 'last_30_days'
      29
    when 'last_60_days'
      59
    when 'last_90_days'
      89
    end

    (0..days).to_a.reverse.each do |number|
      @date_times << (Time.current - number.days).to_date.to_s
      user_ids = User.where(created_at: (Time.current - number.days).all_day).where.not(phone: nil).ids
      @users_count << user_ids.count
      current_time_users_amount = Order.where(user_id: user_ids).paid.sum(:amount).to_i
      @users_sale_amount << current_time_users_amount
      average_user_sale = (current_time_users_amount == 0 || user_ids.count == 0) ? 0 : (current_time_users_amount / user_ids.count)
      @average_user_sale << average_user_sale
    end
  end
end
