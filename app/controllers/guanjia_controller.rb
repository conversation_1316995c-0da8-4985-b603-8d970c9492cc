class GuanjiaController < ActionController::Base
  protect_from_forgery with: :exception
  skip_before_action :verify_authenticity_token
  before_action :sign_account

  def index
    begin
      Rails.logger.error "params_token: #{params['token']}"
    rescue => e
      Rails.logger.error "params_token: #{e.message}"
    end
    last_word = params['method'].split('.').last
    command = Guanjia.const_get(last_word).new(params['bizcontent']).call
    @result = command.result
    template = last_word.scan(/[A-Z][^A-Z]*/).map { |word| word.downcase }.join('_')
    render "guanjia/#{template}"
  end

  private

  def sign_account
    _params_ = params
    appkey = _params_["appkey"]
    bizcontent = _params_["bizcontent"]
    _method = _params_["method"]
    token = _params_["token"]
    sign = _params_["sign"]
    app_secret = "4b23a336400446bba6df6e98e7d53c60"
    _signsc_ = app_secret + "appkey" + appkey.to_s + "bizcontent" + bizcontent.to_s + "method" + _method.to_s + "token" + token.to_s + app_secret
    sigin_down = _signsc_.downcase
    md5_sign = Digest::MD5.hexdigest(sigin_down)
    if sign != md5_sign
      render json: { "code": "40000", "message": "Logical Error", "subcode": "GSE.VERIFYSIGN_FAILURE", "submessage": "签名验证失败" }.to_json and return
    end
  end
end