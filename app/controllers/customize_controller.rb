class CustomizeController < ActionController::Base
  skip_before_action :verify_authenticity_token
  def index
    Rails.logger.error "----------------index------------"
    Rails.logger.error params
  end

  def create
    request.headers["HTTP_X_SHOPIFY_TOPIC"]
    data = request.body.read
    verified = verify_webhook(data, request.headers["HTTP_X_SHOPIFY_HMAC_SHA256"])
    Rails.logger.error "-----------Webhook#create-----------------"
    Rails.logger.error request.headers["HTTP_X_SHOPIFY_TOPIC"]
    Rails.logger.error params
    Rails.logger.error params[:name]
    Rails.logger.error params[:line_items]
    unless verified
      Rails.logger.error "-----------Webhook 验证未通过-----------------"
      head :unauthorized
      return "验证未通过"
    end
    params[:line_items].each do |item|
      property = item[:properties].find { |prop| prop[:name] == "design_id" }
      if property
        Rails.logger.error "-----------响应结果开始--property-symbol---------------"
        Rails.logger.error property[:value]
        Rails.logger.error "-----------响应结果开始--name-symbol---------------"
        Rails.logger.error params[:name]
      end
    end
  end

  def verify_webhook(data, hmac_header)
    calculated_hmac = Base64.strict_encode64(OpenSSL::HMAC.digest('sha256', '1e5fc83a62bb8bdde6dd0f9b2d0b91173b8f153e8f148d82f2f76d725756e11c', data))
    ActiveSupport::SecurityUtils.secure_compare(calculated_hmac, hmac_header)
  end
end
