module Web
  class ShortUrlsController < ApplicationController
    def show
      return if params[:id].blank?

      short_url = ShortUrl.find_by(uniq_key: params[:id])
      if short_url.present?
        short_url.click_count += 1
        short_url.short_url_statistics.find_or_create_by(ip: request.env['HTTP_X_FORWARDED_FOR'])
        short_url.save
        redirect_to short_url.redirect_to, allow_other_host: true
      else
        redirect_to "https://iqunix.iqunix.com/downloadh5.html", allow_other_host: true
      end
    end
  end
end
