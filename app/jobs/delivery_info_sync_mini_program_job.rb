class DeliveryInfoSyncMiniProgramJob
  include Sidekiq::Job
  sidekiq_options :failures => true

  def perform(order_id)
    # https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/order-shipping/order-shipping.html#%E4%B8%80%E3%80%81%E5%8F%91%E8%B4%A7%E4%BF%A1%E6%81%AF%E5%BD%95%E5%85%A5%E6%8E%A5%E5%8F%A3
    order = Order.find_by(id: order_id)
    return if order.blank?

    order.payments.paid.each do |payment|
      next if payment.trade_no.size != 28

      transaction_id = payment.trade_no
      tracking_no = order.logistics_number
      receiver_contact = order.receive_phone.gsub(/(\d{3})(\d{4})(\d{4})/, '\1****\3')
      item_desc = order.order_details.last.product_name
      openid = order.user.mini_program_openid
      url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=#{mini_program_token}"
      data = {
        order_key: {
          order_number_type: 2,
          transaction_id: transaction_id
        },
        logistics_type: 1,
        delivery_mode: 1,
        shipping_list: [
            {
              tracking_no: tracking_no,
              express_company: "SF",
              item_desc: item_desc + ' (如已发货请忽略)',
              contact: {
                receiver_contact: receiver_contact
              }
            }
        ],
        upload_time: Time.now.rfc3339,
        payer: {
          openid: openid
        }
      }.to_json
      response = Faraday.post(url, data)
      Rails.logger.info "------------DeliveryInfoSyncMiniProgramJob-------------------"
      Rails.logger.info JSON.parse(response.body)
    end
  end

  def mini_program_token
    Rails.cache.fetch("mini_program_token", expires_in: 1.hour) do
      response = Faraday.get("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=#{Settings.mini_program.appid}&secret=#{Settings.mini_program.app_secret}")
      result = JSON.parse(response.body)
      Rails.logger.info '----------wechat_mini_program----access_token--------'
      Rails.logger.info result['access_token']
      result['access_token']
    end
  end
end
