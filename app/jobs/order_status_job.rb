class OrderStatusJob < ApplicationJob
  queue_as :default

  def perform(order_id)
    order = SpOrder.find(order_id)
    receiver_member_id = order.member_id
    receiver_member = SpMember.find(receiver_member_id)
    product_name = SpGood.find_by_goods_id(order.detail.goods_id).name

    if order.status == 0
      str = "您有一笔订单未付款,付款后我们将尽快为您安排发货！"
    end
    if order.status == 2
      str = "您购买的【#{product_name}】已经发货啦！"
    end
    if order.status == 6
      str = "您购买的【#{product_name}】已经发货啦！"
    end
    # 如果表里面已经推送过了，就不再进行推送，这个可以放在前面去,也可以双保险，推送成功后进行更改已经推送完成
    if (order_status = OrderSendStatus.where(order_id: order.id,status: order.status).first)
      if order_status.flag
        return false
      end
    else
      order_status = OrderSendStatus.create(order_id: order.id, status: order.status)
    end

    order_status.count += 1

    if order.status == 0
      if order_status.count > 1
        order_status.flag = true
      end
    else
      order_status.flag = true
    end
    order_status.save
    send_notification_jpush_mess(str, "default", receiver_member.register_id)
  end
end