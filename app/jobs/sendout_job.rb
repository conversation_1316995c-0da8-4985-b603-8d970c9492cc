class SendoutJob < ApplicationJob
  queue_as :default

  def perform(order_id)
    order = SpOrder.find(order_id)
    receiver_member_id = order.member_id
    receiver_member = SpMember.find(receiver_member_id)
    product_name = SpGood.find_by_goods_id(order.detail.goods_id).name

    if order.exp_name != nil && order.exp_time != nil
      send_notification_jpush_mess("您购买的#{ product_name } 已经发货", 'default', receiver_member.register_id)
    end
  end
end
