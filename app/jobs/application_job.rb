class ApplicationJob < ActiveJob::Base
  def send_notification_jpush_mess(alert, sound, register_id)
    return if register_id.nil?

    jpush = JPush::Client.new("e736481b1352c276be6d4818", "e116cd9c55a4e64af0fdf627")

    notification = JPush::Push::Notification.new

    notification.set_ios(alert: alert, sound: sound).set_android(alert: alert)

    audience = JPush::Push::Audience.new.set_registration_id(register_id)

    push_payload = JPush::Push::PushPayload.new(
      platform: "all",
      audience: audience,
      notification: notification
    )

    pusher = jpush.pusher
    pusher.push(push_payload)
  end
end
