class UserActiveStatisticJob < ApplicationJob
  queue_as :default

  def perform(user_id)
    unless UserOperation.find_by(user_id: user_id, operation_type: :active, created_on: Date.today)
      UserOperation.create(user_id: user_id, operation_type: :active, created_on: Date.today)
      UserStatistic.find_or_create_by(stat_type: :active, created_on: Date.today).increment!(:total_count)
    end
  end
end
