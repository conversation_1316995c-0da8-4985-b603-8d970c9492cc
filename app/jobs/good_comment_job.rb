class GoodCommentJob < ApplicationJob
  queue_as :default

  def perform(receiver_member_id, sender_member_id, goods_comment_id, goods_id)
    comment = Comment.find(goods_comment_id)
    Notification.create(user_id: receiver_member_id, send_user_id: sender_member_id, notify_type: :reply_comment, content: comment.content, target: comment.parent)

    # 接收者和发送者不相同时才发送
    if receiver_member_id != sender_member_id
      receiver_member = User.find(receiver_member_id)
      sender_member = User.find(sender_member_id)
      return if receiver_member.blank? || sender_member.blank?

      send_notification_jpush_mess("#{sender_member.user_name} 评论了你", "default", receiver_member.register_id)
    end
  end
end
