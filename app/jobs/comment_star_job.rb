# frozen_string_literal: true

# 评论的的点赞已经完成
class CommentStarJob < ApplicationJob
  queue_as :default

  def perform(sender_member_id, goods_comment_id)
    comment = Comment.find(goods_comment_id)
    receiver_member_id = comment.user.id

    # 接收者和发送者不相同时才发送
    return unless receiver_member_id != sender_member_id

    Notification.create(user_id: receiver_member_id, send_user_id: sender_member_id, notify_type: :comment_star,
                        content: comment.content, target: comment)

    sender_member = User.find_by(id: sender_member_id)
    receiver_member = User.find_by(id: receiver_member_id)
    return unless receiver_member&.register_id

    send_notification_jpush_mess("#{sender_member.user_name} 赞了你", 'default', receiver_member.register_id)
  end
end
