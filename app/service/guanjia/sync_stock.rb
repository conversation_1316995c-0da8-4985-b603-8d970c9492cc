# 商品库存同步
module Guanjia
  class SyncStock
    prepend SimpleCommand

    def initialize(content)
      @content = content
    end

    def call
      content = JSON.parse(@content)
      sku_id = content['SkuID']
      product_id = content['PlatProductID']
      sku = Sku.find(sku_id)
      result = {}
      if sku.blank?
        result['errcode'] = 1
        result['errmsg'] = 'ERROR'
        return result
      end
      if sku.product_id.to_s != product_id.to_s
        result['errcode'] = 1
        result['errmsg'] = 'ERROR'
        return result
      end
      if sku.update_columns(sku_quantity: content['Quantity'])
        result['errcode'] = 0
        result['errmsg'] = 'SUCCESS'
        result['Quantity'] = content['Quantity']
        return result
      else
        result['errcode'] = 1
        result['errmsg'] = 'ERROR'
        return result
      end
      return result
    end
  end
end