# 订单发货
module Guanjia
  class Send
    prepend SimpleCommand

    def initialize(content)
      @content = content
    end

    def call
      content = JSON.parse(@content)

      result = {}
      order_number = content['PlatOrderNo']
      order = Order.find_by_number(order_number)
      express_name = content['LogisticName']
      express_code = content['LogisticNo']

      if order.blank?
        result['errcode'] = 2
        result['errmsg'] = "订单： #{ order_number } 没有改订单"
        return result
      end

      if order.pay_time.blank? || order.status.to_i == 0 || order.charge_number.blank?
        result['errcode'] = 2
        result['errmsg'] = "订单： #{ order_number } 还未付款"
        return result
      end

      if !order.refund_time.blank? || order.status.to_i == 6 || order.status.to_i == 11
        result['errcode'] = 3
        result['errmsg'] = "订单： #{ order_number } 已经退款"
        return result
      end

      if !order.logistics_com.blank? || !order.logistics_number.blank? || !order.delivery_time.blank?
        result['errcode'] = 4
        result['errmsg'] = "订单： #{ order_number } 已经发货"
        return result
      end

      if express_name.blank?
        result['errcode'] = 5
        result['errmsg'] = "快递名称不能为空"
        return result
      end

      if express_code.blank?
        result['errcode'] = 6
        result['errmsg'] = "快递单号不能为空"
        return result
      end

      if order.status != 1
        result['errcode'] = 7
        result['errmsg'] = "订单状态非正常发货"
        return result
      end

      order.update!(status: 2, logistics_com: express_name, logistics_number: express_code, delivery_time: Time.zone.now)
      DeliveryInfoSyncMiniProgramJob.perform_async(order.id)
      result['errcode'] = 0
      result['errmsg'] = "成功填写单号"
      return result
    end
  end
end
