# 订单退款状态查询
module Guanjia
  class CheckRefundStatus
    prepend SimpleCommand

    def initialize(content)
      @content = content
    end

    def call
      content = JSON.parse(@content)
      result = {}
      if content['OrderID'].blank?
        result['errcode'] = 5
        result['errmsg'] = "订单不存在"
        return result
      end
      order = Order.find_by_number(content['OrderID'])
      repair = order.after_sale
      result['order'] = order
      result['repair'] = repair
      result
    end
  end
end