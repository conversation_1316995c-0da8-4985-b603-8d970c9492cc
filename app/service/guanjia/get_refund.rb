# 退货退款单下载
module Guanjia
  class GetRefund
    prepend SimpleCommand

    def initialize(content)
      @content = content
    end

    def call
      content = JSON.parse(@content)
      result = {}
      result['totalcount'] = AfterSale.count
      page = content['PageIndex'].to_i
      per = content['PageSize'].to_i
      # {"Status":"JH_01","RefundType":"JH_04"}请求状态的例子，后面需要补上

      result['refunds'] = AfterSale.where('created_at > ?', content['BeginTime']).where('created_at < ?', content['EndTime']).page(page).per(per).map { |order| order.as_json }
      result['refunds'].each do |refund|
        # 售后中的订单信息
        unless order = Order.find(refund['order_id'])
          next
        end
        refund['totalamount'] = order.amount
        refund['payamount'] = order.amount
        refund['order_number'] = order.number
        refund['buyernick'] = order.receive_name
        refund['status'] = order.status
        order_details = order.order_details
        refund['goodinfos'] = []
        order_details.each do |detail|
          refund['goodinfos'] << {
            'product_id' => detail.product_id,
            'Sku' => detail.sku_id,
            'ProductName' => detail.try(:product_name),
            'RefundAmount' => (detail.skuprice * 100).to_i * detail.sku_count.to_i,
            'Reason' => refund['reason'],
            'ProductNum' => detail.sku_count
          }
        end
      end
      result
    end
  end
end