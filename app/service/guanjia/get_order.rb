# 订单下载
module Guanjia
  class GetOrder
    prepend SimpleCommand

    def initialize(content)
      @content = content
    end

    def call
      content = JSON.parse(@content)
      result = {}
      # PlatOrderNo 平台订单号
      # 如果有值，那么只获取一个订单
      # 否则获取订单列表
      plat_order_no = content['PlatOrderNo']
      if plat_order_no.present?
        # order = Order.where(number: plat_order_no).first
        result['orders'] = Order.where(number: plat_order_no).map { |order| order.as_json }
        result['order_size'] = Order.where(number: plat_order_no).count
      else
        start_time = Time.parse(content['StartTime'])
        end_time = Time.parse(content['EndTime'])
        page = content['PageIndex']
        per = content['PageSize']
        status = get_status(content['OrderStatus'])
        if status != 99
          orders = Order.where('created_at > ?', start_time).where('created_at < ?', end_time).where(status: status)
          if content['OrderStatus'] == "JH_05"
            today_refund_orders = Order.where('refund_time > ?', start_time).where('refund_time < ?', end_time).where(status: status)
            orders = Order.where(id: (orders.ids + today_refund_orders.ids + [3253, 3370]))
          end
          orders = orders.page(page).per(per)
          result['orders'] = orders.map { |order| order.as_json }
          result['order_size'] = orders.total_count
        else
          orders = Order.where('created_at > ?', start_time).where('created_at < ?', end_time).page(page).per(per)
          result['orders'] = orders.map { |order| order.as_json }
          result['order_size'] = orders.total_count
        end
      end

      result['orders'].each do |order|
        order_details = OrderDetail.where(order_id: order['id'])
        order['goodinfos'] = []

        order_details.each do |detail|
          order['goodinfos'] << {
            'product_id' => detail.product_id,
            'suborderno' => detail.product_id,
            'tradegoodsno' => detail.try(:sku_number), # SpGoodsAttribute.find(detail.ad_id).try(:code),
            'tradegoodsname' => detail.try(:product_name),
            'tradegoodsspec' => detail.try(:property_name),
            'goodscount' => detail.try(:sku_count),
            'price' => detail.try(:skuprice)
          }
        end
      end
      return result
    end

    private

    def get_status(status)
      result = 99
      case status
        # 等待买家付款=JH_01，等待卖家发货=JH_02，等待买家确认收货=JH_03，交易成功=JH_04，交易关闭=JH_05，所有订单=JH_99
        # TATUS={"0"=>"待支付" , "1"=>"已支付待发货" , "2"=>"已发货" , "3"=>"已完成待评价","4"=> "已评价","5"=>"订单取消","6"=>"已退款","11"=>"发货前退款","7"=>"申请退款中","21"=>"发货后退款","200"=>"交易完成"}
      when 'JH_01'
        result = 0
      when 'JH_02'
        result = 1
      when 'JH_03'
        result = 2
      when 'JH_04'
        result = [3, 4, 200]
      when 'JH_05'
        result = [5, 6]
      when 'JH_99'
        result = 99
      else
        result = 99
      end
      return result
    end

  end
end
