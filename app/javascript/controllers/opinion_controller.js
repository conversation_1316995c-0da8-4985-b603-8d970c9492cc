import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [ "detail", "opinion-id", "status" ]

  greet() {
    var opinion_id = $(this.element).find("button").attr("opinion-id");
    var detail = this.detailTarget.value;
    if (detail == "") {
      message("请输入回复内容");
      return false;
    }
    $(this).addClass("disabled");
    $.ajax({
      url: "/operation/opinions/" + opinion_id + "/update_right_reply",
      type: "POST",
      data: {
        detail: detail
      }
    });
  }

  update_status() {
    console.log($(this.element).parent().parent().attr("opinion-id"));
    var opinion_id = $(this.element).parent().parent().attr("opinion-id")
    $(this).parents('.dropdown').find('.dropdown-toggle').text($(this.element).attr("status"))
    $(this).addClass("disabled");
    $.ajax({
      url: "/operation/opinions/" + opinion_id + "/update_right_reply",
      type: "POST",
      data: {
        status: $(this.element).attr("status")
      }
    });
  }
}
