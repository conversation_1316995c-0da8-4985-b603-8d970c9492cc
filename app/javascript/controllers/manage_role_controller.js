import { Controller } from "@hotwired/stimulus"
// Connects to data-controller="manage-role"
export default class extends Controller {
  connect() {
    const App = {
      setup() {
        const themeOverrides = {
          common: {
            primaryColor: '#1677ff',
            primaryColorHover: '#1677ff',
            primaryColorPressed: '#1677ff'
          }
        }
        return {
          themeOverrides
        }
      }
    };
    const app = Vue.createApp(App);
    app.use(naive);
    app.mount("#manage_roles");
  }
}
