import { Controller } from "@hotwired/stimulus"
export default class extends Controller {
  connect() {
    $(document).off('click', '.push_message_timing').on('click', '.push_message_timing', function(){
      if ($('#push_message_timing_1').is(':checked')) {
        $("#push_message_pushed_at").show();
      } else {
        $("#push_message_pushed_at").hide();
      }
    });

    getTemplate();

    $(document).off('change', '#push_message_template_id, #push_message_product_id').on('change', '#push_message_template_id, #push_message_product_id', function(){
      getTemplate();
    });
  }
  disconnect() {
    console.log('disconnect');
  }
}

function getTemplate() {
  var product_id = $('#push_message_product_id').val();
  var template_id = $('#push_message_template_id').val();
  var push_message_id = $('.push-message-form').attr('push_message_id');

  if(template_id == 'custom_notify'){
    $("#push_message_include_sms_message").prop("disabled", true);
  } else {
    $("#push_message_include_sms_message").prop("disabled", false);
  }

  $.ajax({
    url: '/operation/push_messages/template_content',
    type: 'GET',
    data: {select_product_id: product_id, select_template_id: template_id, push_message_id: push_message_id},
    success: function(data) {
      console.log(data);
      $('#push_message_title').val(data.title);
      $('#push_message_template_content').val(data.content);
      $('.subscription_count').text('订阅商品的用户(' + data.subscription_count + ')');
      $('.collect_count').text('收藏商品的用户(' + data.collect_count + ')');
    }
  });
}