import { Controller } from "@hotwired/stimulus"
export default class extends Controller {
  connect() {
    $(document).on('change', '#banner_banner_type', function(){
      var select_value = $(this).val()

      if(select_value == 'is_product'){
        $('.banner_product_id').show();
        $('.banner_crowd_id').hide();
        $('.banner_picture_url').hide();
        $('.banner_content').hide();
      }else if(select_value == 'is_crowd'){
        $('.banner_product_id').hide();
        $('.banner_crowd_id').show();
        $('.banner_picture_url').hide();
        $('.banner_content').hide();
      }else if(select_value == 'is_url'){
        $('.banner_product_id').hide();
        $('.banner_crowd_id').hide();
        $('.banner_picture_url').show();
        $('.banner_content').hide();
      } else if(select_value == 'is_content'){
        $('.banner_product_id').hide();
        $('.banner_crowd_id').hide();
        $('.banner_picture_url').hide();
        $('.banner_content').show();
      }
    });

    ClassicEditor.create(document.querySelector('#ckeditor'), {
      ckfinder: { uploadUrl: '/uploads' },
      image: {
        insert: {
          type: 'inline',
        }
      }
    }).then(
      editor => {
        this.editor = editor;
      }
    ).catch(
      error => {
        console.error(error);
      }
    );
  }
  disconnect() {
    console.log('disconnect');
    if (this.editor) {
      this.editor.destroy();
    }
  }
}
