import { Controller } from "@hotwired/stimulus"
import { toast } from "wc-toast"
import { patch } from "@rails/request.js"
// Connects to data-controller="manage-role-action"
export default class extends Controller {
  connect() {
    var selected_actions = document.getElementById("selected_actions");
    var role_id = selected_actions.getAttribute("data-manage-role-id");
    var auth_list = selected_actions.getAttribute("data-json");
    const App = {
      setup() {
        const themeOverrides = {
          common: {
            primaryColor: '#1677ff',
            primaryColorHover: '#1677ff',
            primaryColorPressed: '#1677ff'
          }
        }

        const checkedRef = Vue.ref(true);
        const checkedAllRef = Vue.ref({});
        const citiesRef = Vue.ref(JSON.parse(selected_actions.value));
        const authsRef = JSON.parse(auth_list);
        console.log(auth_list);
        const arraysContainment = (arr1, arr2) => {
          const fullyIncludedKeys = [];
          const partiallyIncludedKeys = [];
          const notIncludedKeys = [];

          for (const key in arr1) {
            const values = arr1[key];
            const includedValues = values.filter(value => arr2.includes(value));

            if (includedValues.length === values.length) {
              fullyIncludedKeys.push(key);
            } else if (includedValues.length > 0) {
              partiallyIncludedKeys.push(key);
            } else {
              notIncludedKeys.push(key);
            }
          }
          return {full_keys: fullyIncludedKeys, partial_keys: partiallyIncludedKeys, not_keys: notIncludedKeys}
        }

        const changeChecked = () => {
          let finalAllCheckBox = arraysContainment(authsRef, citiesRef.value);
          finalAllCheckBox.full_keys.forEach(key => {
            indeterminateRef.value[key] = false;
            checkedAllRef.value[key] = `${key}-true`;
          });

          finalAllCheckBox.partial_keys.forEach(key => {
            indeterminateRef.value[key] = true;
            checkedAllRef.value[key] = `${key}-false`;
          });

          finalAllCheckBox.not_keys.forEach(key => {
            indeterminateRef.value[key] = false;
            checkedAllRef.value[key] = `${key}-false`;
          });
          patch(`/operation/manage_roles/${role_id}/update_auths`, {
            headers: {
              "Accept": "text/vnd.turbo-stream.html, text/html, application/xhtml+xml",
            },
            body: {
              manage_action_ids: citiesRef.value
            }
          })
        }
        const indeterminateRef = Vue.ref({});
        changeChecked();

        return {
          themeOverrides,
          checked_all: checkedAllRef,
          checked: checkedRef,
          cities: citiesRef,
          handleCheckedChange(checked) {
            let auth_name = checked.split("-")[0];
            let result = checked.split("-")[1];
            checkedAllRef.value[auth_name] = checked
            let origin_array = citiesRef.value
            if(result == "true"){
              const concatenatedArray = origin_array.concat(authsRef[auth_name]);
              const uniqueArray = [...new Set(concatenatedArray)];
              citiesRef.value = uniqueArray
            }else{
              const deletedArray = origin_array.filter(item => !authsRef[auth_name].includes(item));
              citiesRef.value = deletedArray
            }
            changeChecked();
            toast.success("操作成功");
          },
          handleUpdateValue(value) {
            citiesRef.value = value;
            changeChecked();
            toast.success("操作成功");
          },
          indeterminate: indeterminateRef,
          // options: [
          //   {
          //     label: "管理员",
          //     value: "管理员",
          //     key: "1"
          //   },
          //   {
          //     label: "运营",
          //     value: "运营",
          //     key: "2"
          //   },
          //   {
          //     label: "客服",
          //     value: "客服",
          //     key: "4"
          //   }
          // ],
          // handleSelectRole(value, option) {
          //   Turbo.visit(`/operation/manage_roles/${option.key}`)
          // },
        }
      }
    };
    const app = Vue.createApp(App);
    app.use(naive);
    app.mount("#manage_role_actions");
  }
}
