import { Controller } from "@hotwired/stimulus"
import { get } from "@rails/request.js"

// Connects to data-controller="dashboard"
export default class extends Controller {
  connect() {
    const App = {
      setup() {
        const themeOverrides = {
          common: {
            primaryColor: '#1677ff',
            primaryColorHover: '#1677ff',
            primaryColorPressed: '#1677ff'
          }
        }

        return {
          value: Vue.ref("今日实时"),
          themeOverrides,
          options: [
            {
              label: "今日实时",
              value: "今日实时",
              key: "today"
            },
            {
              label: "最近7天",
              value: "最近7天",
              key: "last_7_days"
            },
            {
              label: "最近30天",
              value: "最近30天",
              key: "last_30_days"
            },
            {
              label: "最近60天",
              value: "最近60天",
              key: "last_60_days"
            }
          ],
          handleSelect(value, option) {
            get(`/operation/main/subscription_statistic?search=${option.key}`, {
              headers: {
                "Accept": "text/vnd.turbo-stream.html, text/html, application/xhtml+xml",
              }
            })
          },

        };
      }
    };
    const app = Vue.createApp(App);
    app.use(naive);
    app.mount("#app");


    const SaleCountStatistic = {
      setup() {
        const themeOverrides = {
          common: {
            primaryColor: '#1677ff',
            primaryColorHover: '#1677ff',
            primaryColorPressed: '#1677ff'
          }
        }

        return {
          value: Vue.ref("最近30天"),
          themeOverrides,
          options: [
            {
              label: "最近30天",
              value: "最近30天",
              key: "last_30_days"
            },
            {
              label: "最近60天",
              value: "最近60天",
              key: "last_60_days"
            },
            {
              label: "最近90天",
              value: "最近90天",
              key: "last_90_days"
            }
          ],
          handleSelect(value, option) {
            // 两种方法都可以
            // $("#sale_count_chart_frame").attr("src", `/operation/main/sale_count_statistic?search=${option.key}`)
            $("#sale_count_chart_frame").html(`
              <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status"></div>
              </div>
            `)
            get(`/operation/main/sale_count_statistic?search=${option.key}`, {
              headers: {
                "Accept": "text/vnd.turbo-stream.html, text/html, application/xhtml+xml",
              }
            })
          },

        };
      }
    };
    const sale_count_statistic = Vue.createApp(SaleCountStatistic);
    sale_count_statistic.use(naive);
    sale_count_statistic.mount("#sale_count_statistic");
  }
}
