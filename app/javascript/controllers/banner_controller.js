import { Controller } from "@hotwired/stimulus"
export default class extends Controller {
  connect() {
    $(".banner_show_status").click(function () {
      var id = $(this).attr("id");
      var id_number = id.split("show_status")
      change_show_status(id_number[1]);
    });

    function change_show_status(id) {
      $.ajax({
        url: "/operation/banners/" + id + "/show_status",
        method: 'patch',
        success: function (msg) {
          if (msg["status"] == 500) {
            message("操作失败");
          }
          if (msg["status"] == 200) {
            message("操作成功");
          }
        }
      })
    }
  }
  // disconnect() {
  //   console.log('disconnect');
  //   if (this.editor) {
  //     this.editor.destroy();
  //   }
  // }
}
