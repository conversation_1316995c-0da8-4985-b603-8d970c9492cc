import { Controller } from "@hotwired/stimulus"
import { patch } from "@rails/request.js"

export default class extends Controller {
  update_status() {
    patch(`/operation/crowds/${$(this.element).parent().parent().attr("crowd-id")}`, {
      headers: {
        "Accept": "text/vnd.turbo-stream.html, text/html, application/xhtml+xml",
      },
      body: {
        crowd: {
          crowd_status_id: $(this.element).attr("status-id"),
        }
      }
    })
    message("修改状态成功")
  }

  update() {
    patch(`/operation/crowds/${$(this.element).parent().parent().attr("crowd-id")}`, {
      headers: {
        "Accept": "text/vnd.turbo-stream.html, text/html, application/xhtml+xml",
      },
      body: {
        crowd: {
          crowd_status_id: $(this.element).attr("status-id"),
        }
      }
    })
    message("修改状态成功")
  }
}
