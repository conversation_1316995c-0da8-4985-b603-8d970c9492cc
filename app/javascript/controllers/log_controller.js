import { Controller } from "@hotwired/stimulus"
import { toast } from "wc-toast"
export default class extends Controller {
  connect() {
    $(".forbidden_login").click(function () {
      var id = $(this).attr("id");
      var id_number = id.split("forbidden_login")
      change_forbidden_status(id_number[1]);
    });

    function change_forbidden_status(id) {
      $.ajax({
          url: "/operation/logs/forbidden_address",
          method: 'post',
          data: {id: id},
          success: function (data) {
              if (data.status == 200) {
                  toast.success(data.message);
              } else {
                  toast.error(data.message);
              }
          }
      });
    }
  }
}


