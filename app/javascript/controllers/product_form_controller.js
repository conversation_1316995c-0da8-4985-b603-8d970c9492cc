import { Controller } from "@hotwired/stimulus"
export default class extends Controller {
  connect() {
    $(document).off('click', '.product-image-pic').on('click', '.product-image-pic', function(){
      $(this).parent().find('.product-image-input').click();
    });

    $(document).on('mouseenter', '.product-image-pic', function(){
      $(this).parent().find('.toolbar').show();
    });

    $(document).on('mouseleave', '.product-image-pic', function(){
      $(this).parent().find('.toolbar').hide();
    });

    $(document).on('mouseenter', '.toolbar', function(){
      $(this).parent().find('.toolbar').show();
    });

    $(document).on('mouseleave', '.toolbar', function(){
      $(this).parent().find('.toolbar').hide();
    });

    $(document).off('click', '.image-file-input').on('click', '.image-file-input', function(){
      $(this).parent().find('.product-image-input').click();
    });

    $(document).off('change', '.product-image-input').on('change', '.product-image-input', function(){
      var _this = $(this)
      // 先获取用户上传的文件对象
      var fileObj = this.files[0];
      // 生成一个文件读取的内置对象
      var fileReader = new FileReader();
      // 将文件对象传递给内置对象
      fileReader.readAsDataURL(fileObj); //这是一个异步执行的过程，所以需要onload回调函数执行读取数据后的操作
      // 将读取出文件对象替换到img标签
      fileReader.onload = function(){  // 等待文件阅读器读取完毕再渲染图片
        _this.parent().find(".product-image-pic").attr("src", fileReader.result);
      }
    });

    // $(document).on('change', '#product_is_sale', function(){
    //   if($(this).prop('checked')){
    //     $('.button_txt_id').show();
    //   }else{
    //     $('.button_txt_id').hide();
    //   }
    // });

    $(document).on('change', '#product_is_sale_at_button', function(){
      if($(this).prop('checked')){
        $('.product_is_sale_at').show();
      }else{
        $('.product_is_sale_at').hide();
      }
    });

    $(document).on('change', '#product_is_pre_sale', function(){
      if($(this).prop('checked')){
        $('.deposit_id').show();
        $('.pre_sale_stage_id').show();
      }else{
        $('.deposit_id').hide();
        $('.pre_sale_stage_id').hide();
      }
    });

    ClassicEditor.create(document.querySelector('#ckeditor'), {
      ckfinder: { uploadUrl: '/uploads' },
      image: {
        insert: {
          type: 'inline',
        }
      }
    }).then(
      editor => {
        this.editor = editor;
      }
    ).catch(
      error => {
        console.error(error);
      }
    );
  }
  disconnect() {
    console.log('disconnect');
    if (this.editor) {
      this.editor.destroy();
    }
  }
}
