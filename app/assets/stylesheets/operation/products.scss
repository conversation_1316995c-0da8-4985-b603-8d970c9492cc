.product-footer{
  position: fixed;
  bottom: 0;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 0.5px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  padding: 10px 0px;
  text-align: center;
  margin-left: -12px;
}
.footer {
  display: none;
}
body {
  height: 100%;
  width: 100%;
  overflow: auto;
}
html {
  height: 100%;
  overflow: hidden;
}
.product-image-pic {
  width: 112px;
  height: 112px;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
}
h4 {
  font-size: 20px;
  font-weight: bold;
  color: #000;
}
label {
  margin-bottom: 5px;
  margin-top: 15px;
  font-size: 16px;
  color: #353535;
  font-weight: 400;
  margin-right: 10px;
}
.card-body {
  padding-bottom: 36px;
}
.share-img {
  width: 120px;
  height: 160px;
}

.toolbar {
  width: 112px;
  height: 28px;
  background-color:#00000066;
  border-radius: 0px 0px 5px 5px;
  text-align: center;
  margin-top: -28px;
  position: absolute;
  display: none;
}

.top-toolbar {
  width: 112px;
  height: 22px;
  background-color:#00000066;
  border-radius: 5px 5px 0px 0px;
  text-align: center;
  margin-top: -122px;
  position: absolute;
}

.label-tip {
  color: #B1B2B3;
  font-size: 14px;
}

.add_fields {
  position: absolute;
  margin-top: 48px;
}

.lv-width {
  width: 140px;
  display: initial;
}
.property-value-input {
  width: 140px;
  margin-bottom: 11px;
  display: initial;
}
.remove_lv2 {
  position: absolute;
  margin-left: -8px;
  margin-top: -16px;
  font-size: 22px;
  color: #aeaeae;
}
.property-label {
  width: 88px;
  margin-right: 0px;
  padding-top: 8px;
}
.property-value-label {
  width: 88px;
  margin-right: 0px;
  padding-top: 12px;
}
.lv2s {
  display: flex;
  flex-flow: wrap;
}
.add_lv2 {
  margin-top: 13px;
}
.property-value-list {
  box-sizing: border-box;
  margin-top: 5px;
  width: 150px;
  margin-right: 5px;
}
.lv1 {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 30px;
  margin-top: 10px;
}
.remove_lv1 {
  margin-left: 10px;
}
.ck-editor__editable_inline {
  height: calc(100vh - 300px);
}
.ck-editor {
  width: 500px !important;
}