div,
  i,
  button {
    margin: 0;
    padding: 0;
  }

  /*预定义样式，通过js动态生成dom时，加上指定类名*/
  .dpn-message {
    font-family: "\5FAE\8F6F\96C5\9ED1", Helvetica, sans-serif;
    font-size: 12px;
    z-index: 99999;
  }

  .dpn-message {
    box-sizing: border-box;
    position: fixed;
    top: -200px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 99999;
    padding: 5px;
    padding-right: 16px;
    max-width: 50%;
    border-radius: 8px;
    transition: all 1s;
    visibility: visible;
    opacity: 1;
    top: 50px;
  }

  /*info 消息*/
  .dpn-message.dpn-info {
    background: #ffffff;
    border: 1px solid #EBEEF5;
    color: #000000;
    transition: all .5s;
  }

  /*success消息*/
  .dpn-message.dpn-success {
    background: #f0f9eb;
    border: 1px solid #e1f3d8;
    color: #67C23A;
  }

  /*error消息*/
  .dpn-message.dpn-error {
    background: #fef0f0;
    border: 1px solid #fde2e2;
    color: #F56C6C;
  }
  /*warning消息*/

  .dpn-message.dpn-warning {
    background: #fdf6ec;
    border: 1px solid #faecd8;
    color: #E6A23C;
  }

  .dpn-message .dpn-close {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    font-style: normal;
    cursor: pointer;
  }

  .msg-info {
    color: #1677ff;
    font-size: 20px;
    margin-left: 5px;
  }

  .notice-msg {
    font-size: 14px;
    color: #000000;
    margin-left: 5px;
    margin-bottom: 10px;
  }