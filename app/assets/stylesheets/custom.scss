:root {
  --ct-topbar-height: 50px;
  --ct-leftbar-width: 240px;
}

.content-page{
  background: #F6F7F8;
}

body {
  -webkit-font-smoothing: antialiased;
}

.card {
  border-radius: 12px;
}

.input-group-text {
  background-color: #f2f2f2 !important;
  color: #747474 !important;
}

.bg-light-lighten {
  background-color: #f8f8f8 !important;
}

* {
  font-family: "PingFang SC", "Microsoft YaHei", 微软雅黑, <PERSON>iti, 黑体, sans-serif;
}

.btn-light:focus {
  color: black !important;
  background-color: #eef2f7 !important;
  border-color: #dee2e6;
}

.validate-error {
  color: #f56c6c;
  margin-right: 3px;
}

.col-form-label {
  text-align: right;
}

[v-cloak]{
  display: none;
}

.logo-lg img {
  margin-top: 20px;
  margin-left: 17px;
}

.logo-sm {
  display: none;
}

.sidebar-enable {
  .logo-lg img {
    display: none;
  }

  .logo-sm {
    display: block;
    margin-top: 20px;
  }
}

.file-input-replace {
  cursor: pointer;
}