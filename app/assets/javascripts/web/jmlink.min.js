!function(e) {
  var t = {};
  function n(o) {
      if (t[o])
          return t[o].exports;
      var i = t[o] = {
          i: o,
          l: !1,
          exports: {}
      };
      return e[o].call(i.exports, i, i.exports, n),
      i.l = !0,
      i.exports
  }
  n.m = e,
  n.c = t,
  n.d = function(e, t, o) {
      n.o(e, t) || Object.defineProperty(e, t, {
          enumerable: !0,
          get: o
      })
  }
  ,
  n.r = function(e) {
      "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
          value: "Module"
      }),
      Object.defineProperty(e, "__esModule", {
          value: !0
      })
  }
  ,
  n.t = function(e, t) {
      if (1 & t && (e = n(e)),
      8 & t)
          return e;
      if (4 & t && "object" == typeof e && e && e.__esModule)
          return e;
      var o = Object.create(null);
      if (n.r(o),
      Object.defineProperty(o, "default", {
          enumerable: !0,
          value: e
      }),
      2 & t && "string" != typeof e)
          for (var i in e)
              n.d(o, i, function(t) {
                  return e[t]
              }
              .bind(null, i));
      return o
  }
  ,
  n.n = function(e) {
      var t = e && e.__esModule ? function() {
          return e.default
      }
      : function() {
          return e
      }
      ;
      return n.d(t, "a", t),
      t
  }
  ,
  n.o = function(e, t) {
      return Object.prototype.hasOwnProperty.call(e, t)
  }
  ,
  n.p = "",
  n(n.s = 7)
}([function(e, t) {
  e.exports = function(e, t) {
      if (!(e instanceof t))
          throw new TypeError("Cannot call a class as a function")
  }
}
, function(e, t) {
  function n(e, t) {
      for (var n = 0; n < t.length; n++) {
          var o = t[n];
          o.enumerable = o.enumerable || !1,
          o.configurable = !0,
          "value"in o && (o.writable = !0),
          Object.defineProperty(e, o.key, o)
      }
  }
  e.exports = function(e, t, o) {
      return t && n(e.prototype, t),
      o && n(e, o),
      e
  }
}
, function(e, t, n) {
  e.exports = n(6)
}
, function(e, t) {
  function n(e, t, n, o, i, r, a) {
      try {
          var s = e[r](a)
            , u = s.value
      } catch (e) {
          return void n(e)
      }
      s.done ? t(u) : Promise.resolve(u).then(o, i)
  }
  e.exports = function(e) {
      return function() {
          var t = this
            , o = arguments;
          return new Promise((function(i, r) {
              var a = e.apply(t, o);
              function s(e) {
                  n(a, i, r, s, u, "next", e)
              }
              function u(e) {
                  n(a, i, r, s, u, "throw", e)
              }
              s(void 0)
          }
          ))
      }
  }
}
, function(e, t) {
  e.exports = function(e, t, n) {
      return t in e ? Object.defineProperty(e, t, {
          value: n,
          enumerable: !0,
          configurable: !0,
          writable: !0
      }) : e[t] = n,
      e
  }
}
, function(e, t) {
  function n(t) {
      return "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? e.exports = n = function(e) {
          return typeof e
      }
      : e.exports = n = function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
      }
      ,
      n(t)
  }
  e.exports = n
}
, function(e, t, n) {
  var o = function(e) {
      "use strict";
      var t = Object.prototype
        , n = t.hasOwnProperty
        , o = "function" == typeof Symbol ? Symbol : {}
        , i = o.iterator || "@@iterator"
        , r = o.asyncIterator || "@@asyncIterator"
        , a = o.toStringTag || "@@toStringTag";
      function s(e, t, n, o) {
          var i = t && t.prototype instanceof l ? t : l
            , r = Object.create(i.prototype)
            , a = new S(o || []);
          return r._invoke = function(e, t, n) {
              var o = "suspendedStart";
              return function(i, r) {
                  if ("executing" === o)
                      throw new Error("Generator is already running");
                  if ("completed" === o) {
                      if ("throw" === i)
                          throw r;
                      return _()
                  }
                  for (n.method = i,
                  n.arg = r; ; ) {
                      var a = n.delegate;
                      if (a) {
                          var s = w(a, n);
                          if (s) {
                              if (s === c)
                                  continue;
                              return s
                          }
                      }
                      if ("next" === n.method)
                          n.sent = n._sent = n.arg;
                      else if ("throw" === n.method) {
                          if ("suspendedStart" === o)
                              throw o = "completed",
                              n.arg;
                          n.dispatchException(n.arg)
                      } else
                          "return" === n.method && n.abrupt("return", n.arg);
                      o = "executing";
                      var l = u(e, t, n);
                      if ("normal" === l.type) {
                          if (o = n.done ? "completed" : "suspendedYield",
                          l.arg === c)
                              continue;
                          return {
                              value: l.arg,
                              done: n.done
                          }
                      }
                      "throw" === l.type && (o = "completed",
                      n.method = "throw",
                      n.arg = l.arg)
                  }
              }
          }(e, n, a),
          r
      }
      function u(e, t, n) {
          try {
              return {
                  type: "normal",
                  arg: e.call(t, n)
              }
          } catch (e) {
              return {
                  type: "throw",
                  arg: e
              }
          }
      }
      e.wrap = s;
      var c = {};
      function l() {}
      function h() {}
      function p() {}
      var d = {};
      d[i] = function() {
          return this
      }
      ;
      var f = Object.getPrototypeOf
        , m = f && f(f(x([])));
      m && m !== t && n.call(m, i) && (d = m);
      var v = p.prototype = l.prototype = Object.create(d);
      function g(e) {
          ["next", "throw", "return"].forEach((function(t) {
              e[t] = function(e) {
                  return this._invoke(t, e)
              }
          }
          ))
      }
      function y(e, t) {
          var o;
          this._invoke = function(i, r) {
              function a() {
                  return new t((function(o, a) {
                      !function o(i, r, a, s) {
                          var c = u(e[i], e, r);
                          if ("throw" !== c.type) {
                              var l = c.arg
                                , h = l.value;
                              return h && "object" == typeof h && n.call(h, "__await") ? t.resolve(h.__await).then((function(e) {
                                  o("next", e, a, s)
                              }
                              ), (function(e) {
                                  o("throw", e, a, s)
                              }
                              )) : t.resolve(h).then((function(e) {
                                  l.value = e,
                                  a(l)
                              }
                              ), (function(e) {
                                  return o("throw", e, a, s)
                              }
                              ))
                          }
                          s(c.arg)
                      }(i, r, o, a)
                  }
                  ))
              }
              return o = o ? o.then(a, a) : a()
          }
      }
      function w(e, t) {
          var n = e.iterator[t.method];
          if (void 0 === n) {
              if (t.delegate = null,
              "throw" === t.method) {
                  if (e.iterator.return && (t.method = "return",
                  t.arg = void 0,
                  w(e, t),
                  "throw" === t.method))
                      return c;
                  t.method = "throw",
                  t.arg = new TypeError("The iterator does not provide a 'throw' method")
              }
              return c
          }
          var o = u(n, e.iterator, t.arg);
          if ("throw" === o.type)
              return t.method = "throw",
              t.arg = o.arg,
              t.delegate = null,
              c;
          var i = o.arg;
          return i ? i.done ? (t[e.resultName] = i.value,
          t.next = e.nextLoc,
          "return" !== t.method && (t.method = "next",
          t.arg = void 0),
          t.delegate = null,
          c) : i : (t.method = "throw",
          t.arg = new TypeError("iterator result is not an object"),
          t.delegate = null,
          c)
      }
      function k(e) {
          var t = {
              tryLoc: e[0]
          };
          1 in e && (t.catchLoc = e[1]),
          2 in e && (t.finallyLoc = e[2],
          t.afterLoc = e[3]),
          this.tryEntries.push(t)
      }
      function b(e) {
          var t = e.completion || {};
          t.type = "normal",
          delete t.arg,
          e.completion = t
      }
      function S(e) {
          this.tryEntries = [{
              tryLoc: "root"
          }],
          e.forEach(k, this),
          this.reset(!0)
      }
      function x(e) {
          if (e) {
              var t = e[i];
              if (t)
                  return t.call(e);
              if ("function" == typeof e.next)
                  return e;
              if (!isNaN(e.length)) {
                  var o = -1
                    , r = function t() {
                      for (; ++o < e.length; )
                          if (n.call(e, o))
                              return t.value = e[o],
                              t.done = !1,
                              t;
                      return t.value = void 0,
                      t.done = !0,
                      t
                  };
                  return r.next = r
              }
          }
          return {
              next: _
          }
      }
      function _() {
          return {
              value: void 0,
              done: !0
          }
      }
      return h.prototype = v.constructor = p,
      p.constructor = h,
      p[a] = h.displayName = "GeneratorFunction",
      e.isGeneratorFunction = function(e) {
          var t = "function" == typeof e && e.constructor;
          return !!t && (t === h || "GeneratorFunction" === (t.displayName || t.name))
      }
      ,
      e.mark = function(e) {
          return Object.setPrototypeOf ? Object.setPrototypeOf(e, p) : (e.__proto__ = p,
          a in e || (e[a] = "GeneratorFunction")),
          e.prototype = Object.create(v),
          e
      }
      ,
      e.awrap = function(e) {
          return {
              __await: e
          }
      }
      ,
      g(y.prototype),
      y.prototype[r] = function() {
          return this
      }
      ,
      e.AsyncIterator = y,
      e.async = function(t, n, o, i, r) {
          void 0 === r && (r = Promise);
          var a = new y(s(t, n, o, i),r);
          return e.isGeneratorFunction(n) ? a : a.next().then((function(e) {
              return e.done ? e.value : a.next()
          }
          ))
      }
      ,
      g(v),
      v[a] = "Generator",
      v[i] = function() {
          return this
      }
      ,
      v.toString = function() {
          return "[object Generator]"
      }
      ,
      e.keys = function(e) {
          var t = [];
          for (var n in e)
              t.push(n);
          return t.reverse(),
          function n() {
              for (; t.length; ) {
                  var o = t.pop();
                  if (o in e)
                      return n.value = o,
                      n.done = !1,
                      n
              }
              return n.done = !0,
              n
          }
      }
      ,
      e.values = x,
      S.prototype = {
          constructor: S,
          reset: function(e) {
              if (this.prev = 0,
              this.next = 0,
              this.sent = this._sent = void 0,
              this.done = !1,
              this.delegate = null,
              this.method = "next",
              this.arg = void 0,
              this.tryEntries.forEach(b),
              !e)
                  for (var t in this)
                      "t" === t.charAt(0) && n.call(this, t) && !isNaN(+t.slice(1)) && (this[t] = void 0)
          },
          stop: function() {
              this.done = !0;
              var e = this.tryEntries[0].completion;
              if ("throw" === e.type)
                  throw e.arg;
              return this.rval
          },
          dispatchException: function(e) {
              if (this.done)
                  throw e;
              var t = this;
              function o(n, o) {
                  return a.type = "throw",
                  a.arg = e,
                  t.next = n,
                  o && (t.method = "next",
                  t.arg = void 0),
                  !!o
              }
              for (var i = this.tryEntries.length - 1; i >= 0; --i) {
                  var r = this.tryEntries[i]
                    , a = r.completion;
                  if ("root" === r.tryLoc)
                      return o("end");
                  if (r.tryLoc <= this.prev) {
                      var s = n.call(r, "catchLoc")
                        , u = n.call(r, "finallyLoc");
                      if (s && u) {
                          if (this.prev < r.catchLoc)
                              return o(r.catchLoc, !0);
                          if (this.prev < r.finallyLoc)
                              return o(r.finallyLoc)
                      } else if (s) {
                          if (this.prev < r.catchLoc)
                              return o(r.catchLoc, !0)
                      } else {
                          if (!u)
                              throw new Error("try statement without catch or finally");
                          if (this.prev < r.finallyLoc)
                              return o(r.finallyLoc)
                      }
                  }
              }
          },
          abrupt: function(e, t) {
              for (var o = this.tryEntries.length - 1; o >= 0; --o) {
                  var i = this.tryEntries[o];
                  if (i.tryLoc <= this.prev && n.call(i, "finallyLoc") && this.prev < i.finallyLoc) {
                      var r = i;
                      break
                  }
              }
              r && ("break" === e || "continue" === e) && r.tryLoc <= t && t <= r.finallyLoc && (r = null);
              var a = r ? r.completion : {};
              return a.type = e,
              a.arg = t,
              r ? (this.method = "next",
              this.next = r.finallyLoc,
              c) : this.complete(a)
          },
          complete: function(e, t) {
              if ("throw" === e.type)
                  throw e.arg;
              return "break" === e.type || "continue" === e.type ? this.next = e.arg : "return" === e.type ? (this.rval = this.arg = e.arg,
              this.method = "return",
              this.next = "end") : "normal" === e.type && t && (this.next = t),
              c
          },
          finish: function(e) {
              for (var t = this.tryEntries.length - 1; t >= 0; --t) {
                  var n = this.tryEntries[t];
                  if (n.finallyLoc === e)
                      return this.complete(n.completion, n.afterLoc),
                      b(n),
                      c
              }
          },
          catch: function(e) {
              for (var t = this.tryEntries.length - 1; t >= 0; --t) {
                  var n = this.tryEntries[t];
                  if (n.tryLoc === e) {
                      var o = n.completion;
                      if ("throw" === o.type) {
                          var i = o.arg;
                          b(n)
                      }
                      return i
                  }
              }
              throw new Error("illegal catch attempt")
          },
          delegateYield: function(e, t, n) {
              return this.delegate = {
                  iterator: x(e),
                  resultName: t,
                  nextLoc: n
              },
              "next" === this.method && (this.arg = void 0),
              c
          }
      },
      e
  }(e.exports);
  try {
      regeneratorRuntime = o
  } catch (e) {
      Function("r", "regeneratorRuntime = r")(o)
  }
}
, function(e, t, n) {
  "use strict";
  n.r(t);
  var o = {};
  n.r(o),
  n.d(o, "getScreenResolution", (function() {
      return v
  }
  ));
  var i = n(4)
    , r = n.n(i)
    , a = n(5)
    , s = n.n(a)
    , u = n(2)
    , c = n.n(u)
    , l = n(3)
    , h = n.n(l)
    , p = n(0)
    , d = n.n(p)
    , f = n(1)
    , m = n.n(f);
  function v() {
      var e, t = window.screen;
      "number" == typeof t.width && "number" == typeof t.height || (t = {
          width: window.screen.availHeight || window.innerWidth || document.body.offsetWidth || document.body.clientWidth,
          height: window.screen.availHeight || window.innerHeight || document.body.offsetHeight || document.body.clientHeight
      }),
      e = window.devicePixelRatio || 1,
      e = Math.max(2, e),
      t.width >= 1080 && (e = 1);
      var n = t.width * e
        , o = t.height * e;
      return {
          width: n = parseInt(n),
          height: o = parseInt(o),
          dpr: e
      }
  }
  var g = window.navigator.userAgent
    , y = [{
      value: "android",
      key: "Android"
  }, {
      value: "webos",
      key: "hpwOS"
  }, {
      value: "ios",
      key: "iPhone OS"
  }, {
      value: "linux",
      key: "Linux"
  }, {
      value: "windows",
      key: "Windows NT"
  }, {
      value: "mac",
      key: "Macintosh"
  }, {
      value: "ipad",
      key: "iPad; CPU OS"
  }, {
      value: "osx",
      key: "Mac OS X"
  }, {
      value: "ubuntu",
      key: "Ubuntu"
  }, {
      value: "mobile",
      key: "Mobile"
  }, {
      value: "xiaomi",
      key: "XiaoMi"
  }]
    , w = "(" + y.map((function(e) {
      return e.key
  }
  )).join("|") + ")[\\s\\/]*([\\d\\_\\.]*)"
    , k = new (function() {
      function e() {
          d()(this, e),
          this.exportOsInfo()
      }
      return m()(e, [{
          key: "exportOsInfo",
          value: function() {
              var e = this
                , t = g.match(new RegExp(w,"g")) || [];
              y.forEach((function(n) {
                  t.forEach((function(t) {
                      var o = t.match(new RegExp(w))
                        , i = n.value
                        , r = [0];
                      o && o[1] === n.key ? (r = (r = o[2] ? o[2].split(/[_\.]/) : r).map((function(e) {
                          return parseInt(e)
                      }
                      )),
                      e[i] = !0,
                      e[i + "Version"] = r) : void 0 === e[i] && (e[i] = !1,
                      e[i + "Version"] = r)
                  }
                  ))
              }
              )),
              this.ipad && (this.iosVersion = this.ipadVersion),
              this.xiaomi && (this.ios = !1,
              this.android = !0),
              this.ios = this.ios || this.ipad,
              this.osx = this.osx && this.mac,
              this.desktop = this.windows || this.mac || this.ubuntu || this.linux && !this.android && !this.mobile,
              this.mobile = this.mobile || this.android || this.ios
          }
      }]),
      e
  }())
    , b = function() {
      function e() {
          var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "";
          d()(this, e);
          var n = t.match(/(\w+):\/\/([^:\/\?]*):?(\d*)([^\?]*)\??([^#$]*)#?([^#]*)/) || [];
          this.scheme = n[1] || "",
          this.protocol = n[1] || "",
          this.domain = n[2] || "",
          this.host = n[2] || "",
          this.port = n[3] || "",
          this.path = n[4] || "",
          this.queryString = n[5] || "",
          this.search = n[5] || "",
          this.hash = n[6] || ""
      }
      return m()(e, [{
          key: "getParams",
          value: function() {
              var e = this.queryString.split("&")
                , t = {};
              return e.forEach((function(e) {
                  var n = e.split("=");
                  n[0] && (t[n[0]] = n[1])
              }
              )),
              t
          }
      }, {
          key: "getParamsStartWith",
          value: function(e) {
              var t = new RegExp("^" + e)
                , n = this.getParams()
                , o = {};
              for (var i in n)
                  t.test(i) && ("mw_cp_" === e || "mw_dynp_" === e ? o[i.replace(e, "")] = n[i] : o[i] = n[i]);
              return o
          }
      }, {
          key: "getParam",
          value: function(e) {
              return this.getParams()[e]
          }
      }, {
          key: "setParam",
          value: function(e, t) {
              var n = this.getParams()
                , o = [];
              for (var i in void 0 === t ? delete n[e] : n[e] = t,
              n)
                  o.push(i + "=" + n[i]);
              return this.queryString = o.join("&"),
              this
          }
      }, {
          key: "setParams",
          value: function(e) {
              var t = this.getParams()
                , n = [];
              for (var o in e)
                  null === t[o] ? delete t[o] : t[o] = e[o];
              for (var i in t)
                  n.push(i + "=" + t[i]);
              return this.queryString = n.join("&"),
              this
          }
      }, {
          key: "toString",
          value: function() {
              var e = [];
              return e.push(this.scheme ? this.scheme + "://" : ""),
              e.push(this.host),
              e.push(this.port ? ":" + this.port : ""),
              e.push(this.path),
              e.push(this.queryString ? "?" + this.queryString : ""),
              e.push(this.hash ? "#" + this.hash : ""),
              e.join("")
          }
      }]),
      e
  }()
    , S = window.navigator.userAgent
    , x = [{
      key: "Chrome",
      value: "chrome"
  }, {
      key: "CriOS",
      value: "CriOS",
      unambiguous: !0
  }, {
      key: "AppleWebkit",
      value: "webkit"
  }, {
      key: "AppleWebKit",
      value: "webkit"
  }, {
      key: "Safari",
      value: "safari"
  }, {
      key: "Opera",
      value: "opera",
      unambiguous: !0
  }, {
      key: "Firefox",
      value: "firefox",
      unambiguous: !0
  }, {
      key: "Firefox",
      value: "firefox",
      unambiguous: !0
  }, {
      key: "FxiOS",
      value: "fxios",
      unambiguous: !0
  }, {
      key: "MQQBrowser",
      value: "qqbrowser",
      unambiguous: !0
  }, {
      key: "QQ",
      value: "qq",
      unambiguous: !0
  }, {
      key: "Qzone",
      value: "qzone",
      unambiguous: !0
  }, {
      key: "Maxthon",
      value: "maxthon",
      unambiguous: !0
  }, {
      key: "UCWEB",
      value: "ucweb",
      unambiguous: !0
  }, {
      key: "SogouMobileBrowser",
      value: "sogou",
      unambiguous: !0
  }, {
      key: "UCBrowser",
      value: "uc",
      unambiguous: !0
  }, {
      key: "SE",
      value: "se",
      unambiguous: !0
  }, {
      key: "360SE",
      value: "360se",
      unambiguous: !0
  }, {
      key: "IEMobile",
      value: "iem",
      unambiguous: !0
  }, {
      key: "MSIE",
      value: "ie",
      unambiguous: !0
  }, {
      key: "weibo",
      value: "weibo",
      unambiguous: !0
  }, {
      key: "MagicWindow",
      value: "mw",
      unambiguous: !0
  }, {
      key: "SamsungBrowser",
      value: "samsung"
  }, {
      key: "QHBrowser",
      value: "qh360",
      unambiguous: !0
  }, {
      key: "Mb2345Browser",
      value: "mb2345",
      unambiguous: !0
  }, {
      key: "TencentTraveler",
      value: "tt",
      unambiguous: !0
  }, {
      key: "MxBrowser",
      value: "mx",
      unambiguous: !0
  }, {
      key: "BAIDUBrowser",
      value: "baidu",
      unambiguous: !0
  }, {
      key: "baidubrowser",
      value: "baidu",
      unambiguous: !0
  }, {
      key: "MicroMessenger",
      value: "wx",
      unambiguous: !0
  }, {
      key: "LieBaoFast",
      value: "liebao",
      unambiguous: !0
  }, {
      key: "AlipayClient",
      value: "aliPay",
      unambiguous: !0
  }, {
      key: "Huawei",
      value: "huawei",
      unambiguous: !0
  }]
    , _ = "(" + x.map((function(e) {
      return e.key
  }
  )).join("|") + ")[\\s_{3}\\/]*([\\d\\_\\.]*)"
    , j = new (function() {
      function e() {
          var t = this;
          d()(this, e);
          var n = S.match(new RegExp(_,"g")) || [];
          x.forEach((function(e) {
              n.forEach((function(n) {
                  var o, i = n.match(new RegExp(_)), r = e.value;
                  i && i[1] === e.key ? (o = (o = (o = (o = i[2] || "").replace(/(^[\._\/]*|[\._\/]*$)/g, "")) ? o.split(/[_\.]/) : [0]).map((function(e) {
                      return parseInt(e)
                  }
                  )),
                  t[r] = !0,
                  t[r + "Version"] = o,
                  !0 === e.unambiguous && void 0 === t.unambiguous && (t.unambiguous = !0)) : void 0 === t[r] && (t[r + "Version"] = [0])
              }
              ))
          }
          ));
          var o = this.unambiguous;
          this.chrome = this.chrome || this.CriOS,
          this.chrome = this.chrome && -1 !== navigator.vendor.indexOf("Google") && window.chrome,
          this.chrome = this.chrome && !o,
          this.chrome = !!this.chrome,
          this.safari = this.safari && -1 !== navigator.vendor.indexOf("Apple"),
          this.safari = this.safari && (k.ios || k.osx),
          this.safari = this.safari && !this.chrome && !o,
          this.safari = !!this.safari,
          this.iosWebView = k.ios && this.webkit && !this.unambiguous && !this.safari && !this.chrome,
          this.androidWebView = k.android && this.webkit && !this.unambiguous && !this.chrome,
          this.wx && (this.qqbrowser = !1,
          this.qqbrowserVersion = []),
          this.qzone && (this.qq = !1)
      }
      return m()(e, [{
          key: "isSupportedScheme",
          value: function() {
              arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
              return !this.weibo && !this.wx
          }
      }, {
          key: "isSupportedSchemeAuto",
          value: function() {
              arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
              return !(this.weibo || this.wx || this.qq || this.qzone || this.qqbrowser)
          }
      }, {
          key: "isSupportedSchemeOnlyClick",
          value: function() {
              arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
              return !!(k.android && this.isSupportedScheme() || this.chrome || this.qq || this.aliPay || this.baidu || this.qqbrowser)
          }
      }, {
          key: "isSupportedSchemeOnlyHref",
          value: function() {
              arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
              if (k.ios) {
                  if (this.qq)
                      return !0
              } else if (this.SamsungBrowser)
                  return !0;
              return !1
          }
      }, {
          key: "isSupportedUniversalLink",
          value: function() {
              if (k.ios && k.iosVersion[0] >= 9) {
                  return k.iosVersion[0] >= 10 ? this.weibo || this.wx || this.safari || this.aliPay || !this.unambiguous : this.weibo || this.wx || this.safari || this.aliPay
              }
              return !1
          }
      }, {
          key: "isSupportedAppLink",
          value: function() {
              arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
              return !1
          }
      }, {
          key: "isSupportedYYB",
          value: function() {
              var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}
                , t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : ""
                , n = new b(t);
              if ("dc" === n.getParam("mw_dc_order"))
                  return !1;
              var o = k.ios ? e.iosYyb : e.androidYyb
                , i = k.ios && this.qzone
                , r = this.wx || this.qq || this.qqbrowser || i;
              return o && r
          }
      }, {
          key: "isSupportedIntent",
          value: function() {
              arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
              return !1
          }
      }]),
      e
  }())
    , C = function() {
      var e, t;
      (e = new b(document.location.href).getParam("mw_debug") || ue.debug,
      e) && (t = console).log.apply(t, arguments)
  }
    , P = function() {
      function e() {
          var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
          d()(this, e),
          this.mlink = t
      }
      return m()(e, [{
          key: "init",
          value: function(e) {
              this.data = e || {},
              this.getCustomStyles()
          }
      }, {
          key: "getCustomStyles",
          value: function() {
              if (this.customStyles)
                  return this.customStyles;
              var e, t = {}, n = "custom_mp_data";
              if (this.data.custom_mp_data && "{}" !== this.data.custom_mp_data ? e = this.data.custom_mp_data : (e = this.data.custom_data,
              n = ""),
              "string" == typeof e)
                  try {
                      e = JSON.parse(e)
                  } catch (t) {
                      e = {}
                  }
              for (var o in n || (e.ios_json = {
                  bgUrl: e.iosBgUrl
              },
              e.android_json = {
                  bgUrl: e.androidBgUrl
              },
              e.open_app_json = {
                  bgUrl: e.link && e.link.bgUrl,
                  btnText: e.link && e.link.openText,
                  btnTextColor: e.link && e.link.textColor,
                  btnColor: e.link && e.link.btnColor,
                  btnDistanceBottom: "20%"
              },
              e.safari_down_json = {
                  bgUrl: e.link && e.link.bgUrl,
                  btnTextColor: e.link && e.link.textColor,
                  btnColor: e.link && e.link.btnColor,
                  btnDistanceBottom: "20%"
              },
              e.app_back_json = {
                  bgUrl: e.link && e.link.bgUrl
              }),
              t.iosTipsImg = e.ios_json && e.ios_json.bgUrl || "",
              t.androidTipsImg = e.android_json && e.android_json.bgUrl || "",
              t.open_app_json = e.open_app_json || {},
              t.safari_down_json = e.safari_down_json || {},
              t.app_back_json = e.app_back_json || {},
              t.count_down_json = e.count_down_json || {},
              t)
                  "iosTipsImg" !== o && "androidTipsImg" !== o && (t[o].type = n);
              return t.ios_wechat_json = e.ios_wechat_json || e.customMiddlePageIosWechatCommonJson || !1,
              t.open_app_json.btnDistanceBottom && (/^[0-9]{1,2}\%$/.test(e.open_app_json.btnDistanceBottom) || (t.open_app_json.btnDistanceBottom = "20%")),
              this.customStyles = t,
              t
          }
      }, {
          key: "on",
          value: function(e, t, n) {
              e && t && n && (t && t.constructor === Array ? t : [t]).forEach((function(t) {
                  e.addEventListener(t, n)
              }
              ))
          }
      }, {
          key: "off",
          value: function(e, t, n) {
              e && t && n && (t && t.constructor === Array ? t : [t]).forEach((function(t) {
                  e.removeEventListener(t, n)
              }
              ))
          }
      }, {
          key: "hideAllPage",
          value: function() {
              var t = this;
              return new Promise((function(n) {
                  var o = document.querySelectorAll(".mw-page");
                  e.each(o, (function(e) {
                      var o, i = ["WebkitAnimationEnd", "MozAnimationEnd", "OAnimationEnd", "MsAnimationEnd", "animationEnd", "animationend"], r = function r() {
                          t.off(e, i, r),
                          clearTimeout(o),
                          n()
                      };
                      t.on(e, i, r),
                      o = setTimeout(r, 320),
                      e.classList.remove("show"),
                      e.classList.add("hide")
                  }
                  ))
              }
              ))
          }
      }, {
          key: "showPage",
          value: function(e) {
              var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}
                , n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}
                , o = arguments.length > 3 ? arguments[3] : void 0
                , i = o ? document.querySelector(o + " " + e) : document.querySelector(".mw-page" + e);
              if ("custom_mp_data" === t.type && t.bg)
                  for (var r = i.querySelectorAll(".middle"), a = 0; a < r.length; a++) {
                      var s = r[a].getAttribute("style");
                      r[a].setAttribute("style", s + ";display:none !important")
                  }
              return this.renderPage(i, t, n),
              this.hideAllPage().then((function() {
                  i && (i.classList.add("show"),
                  i.classList.remove("hide"))
              }
              )),
              i
          }
      }, {
          key: "renderPage",
          value: function(e) {
              var t = this
                , n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
              arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
              e && (n.bg && this.setBg(e, n.bg),
              n.title && this.setTitle(e, n.title),
              n.text && this.setText(e, n.text),
              n.tipsImg && this.setTipsImg(e, n.tipsImg),
              n.appIcon && this.setAppIcon(e, n.appIcon, n.appLogoIsShow),
              (n.productName || n.appName) && this.setAppName(e, n),
              n.btn && (Array.isArray(n.btn) && n.btn.forEach((function(n) {
                  t.setBtn(e, n || {})
              }
              )),
              this.setBtn(e, n.btn || {})),
              n.btn2 && this.setBtn(e, n.btn || {}))
          }
      }, {
          key: "showDownloadTips",
          value: function(e) {
              if (e.match(/:\/\/itunes\.apple\.com/) || e.match(/\.apk($|\?)/)) {
                  var t = this.data
                    , n = t.productName || t.appName
                    , o = t.iconUrl || t.appIcon;
                  o += "?imageMogr/v2/thumbnail/300x300";
                  var i = this.getCustomStyles().open_app_json || {}
                    , r = '\n                <h3>正在等您安装</h3>\n                <div class="app-icon"><img id="appIcon" src="'.concat(o, '" alt="" onload="this.style.display=\'block\';" onerror="this.style.display=\'none\';"/></div>\n                <p><b onclick="window.location.reload();">').concat(n, '</b></p>\n\n                <br/>\n                <a class="mw-button no-margin" onclick="window.location.reload();">我已完成安装</a>\n                <div>如果您刚才不小心取消了安装, 请点击: </div>\n                <p>\n                </p>\n                <a class="mw-button no-margin min outline" href="').concat(e, '">再次安装</a>');
                  this.showPage(".mw-alert", {
                      text: r,
                      btn: {
                          color: i.btnTextColor || "",
                          backgroundColor: i.btnColor || ""
                      },
                      bg: i.bgUrl
                  })
              }
          }
      }, {
          key: "showSchemeNotSupport",
          value: function(e) {
              var t = this.getCustomStyles();
              this.showPage(".scheme-notsupported", {
                  appName: this.data.productName || this.data.appName,
                  btn: {
                      innerHTML: t && t.safari_down_json && t.safari_down_json.btnText,
                      url: e
                  }
              })
          }
      }, {
          key: "showContentPage",
          value: function(e) {
              var t = e.productName || e.appName
                , n = e.iconUrl || e.appIcon
                , o = e.mwContent
                , i = this.showPage(".custom-content", {
                  appName: t,
                  appIcon: n
              })
                , r = i.querySelector("h1")
                , a = i.querySelector(".mw-cc-banner")
                , s = a.querySelector("img")
                , u = i.querySelector(".content")
                , c = o.content || o.summary;
              return r.innerHTML = o.title,
              a.style.backgroundImage = 'url("' + o.contentImageUri + '")',
              s.src = o.contentImageUri,
              u.innerHTML = c,
              o.contentImageUri || (a.style.display = "none"),
              i
          }
      }, {
          key: "setBg",
          value: function(e, t) {
              t ? (e.style.backgroundImage = 'url("' + t + '")',
              e.classList.remove("default")) : (e.style.backgroundImage = "",
              e.classList.add("default"))
          }
      }, {
          key: "setTipsImg",
          value: function(e, t) {
              var n = e.querySelector(".tips");
              t ? (n.style.backgroundImage = 'url("' + t + '") ',
              e.classList.remove("default")) : (n.style.backgroundImage = "",
              e.classList.add("default"))
          }
      }, {
          key: "setTitle",
          value: function(e, t) {
              var n = e.querySelector("h3");
              n && (n.innerHTML = t)
          }
      }, {
          key: "setText",
          value: function(e, t) {
              var n = e.querySelector(".tips");
              n && (n.innerHTML = t)
          }
      }, {
          key: "setAppName",
          value: function(t, n) {
              e.each(t.querySelectorAll(".app-name"), (function(e) {
                  e.innerHTML = n.productName || n.appName
              }
              ))
          }
      }, {
          key: "setAppIcon",
          value: function(t, n, o) {
              n && (n += "?imageMogr/v2/thumbnail/300x300"),
              e.each(t.querySelectorAll(".app-icon img"), (function(e) {
                  !1 !== o ? (e.parentElement.style.backgroundImage = "none",
                  e.setAttribute("src", n)) : e.style.visibility = "hidden"
              }
              ))
          }
      }, {
          key: "setBtn",
          value: function(t) {
              var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}
                , o = n;
              e.each(t.querySelectorAll(o.btnName || ".mw-button"), (function(e) {
                  o.backgroundColor && (e.style.backgroundColor = o.backgroundColor),
                  o.color && (e.style.color = o.color),
                  o.btnDistanceBottom && (e.parentElement.style.bottom = o.btnDistanceBottom),
                  n.innerHTML && (e.innerHTML = n.innerHTML),
                  n.initAction ? n.initAction(e) : (n.url && (e.href = n.url),
                  n.onclick && (e.onclick = n.onclick))
              }
              ))
          }
      }, {
          key: "hidePage",
          value: function(e) {
              var t = document.querySelector(e);
              t && (t.classList.remove("show"),
              t.classList.add("hide"))
          }
      }, {
          key: "appenStyle",
          value: function(e) {
              var t = document.createElement("style");
              t.textContent = e,
              document.getElementsByTagName("head")[0].appendChild(t)
          }
      }], [{
          key: "each",
          value: function(e, t) {
              for (var n = 0; n < e.length; n++)
                  t(e[n], n)
          }
      }]),
      e
  }()
    , T = function() {
      function e() {
          d()(this, e)
      }
      return m()(e, null, [{
          key: "getCookie",
          value: function(e) {
              var t = new RegExp("(^|;)[ ]*" + e + "=([^;]*)").exec(document.cookie);
              return t ? decodeURIComponent(t[2]) : null
          }
      }, {
          key: "setCookie",
          value: function(e, t, n, o, i, r) {
              var a;
              n && (a = new Date).setTime(a.getTime() + n),
              document.cookie = e + "=" + encodeURIComponent(t) + (n ? ";expires=" + a.toGMTString() : "") + ";path=" + (o || "/") + (i ? ";domain=" + i : "") + (r ? ";secure" : "")
          }
      }]),
      e
  }()
    , L = 1
    , A = 2
    , E = 3;
  function U() {
      var e = I();
      if (!1 === e)
          return !1;
      var t = e ? "".concat(e, "Hidden") : "hidden";
      return document[t]
  }
  function I() {
      var e;
      return "hidden"in document ? "" : (["webkit", "moz", "ms", "o"].forEach((function(t) {
          "".concat(t, "Hidden")in document && (e = t)
      }
      )),
      e || !1)
  }
  function O() {
      for (var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 8, t = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", n = "", o = e; o > 0; --o)
          n += t[Math.floor(Math.random() * t.length)];
      return n
  }
  function D(e, t) {
      for (var n = 0; n < e.length; n++)
          t(e[n], n)
  }
  function q(e) {
      var t;
      if ("SELECT" === e.nodeName)
          e.focus(),
          t = e.value;
      else if ("INPUT" === e.nodeName || "TEXTAREA" === e.nodeName) {
          var n = e.hasAttribute("readonly");
          n || e.setAttribute("readonly", ""),
          e.select(),
          e.setSelectionRange(0, e.value.length),
          n || e.removeAttribute("readonly"),
          t = e.value
      } else {
          e.hasAttribute("contenteditable") && e.focus();
          var o = window.getSelection()
            , i = document.createRange();
          i.selectNodeContents(e),
          o.removeAllRanges(),
          o.addRange(i),
          t = o.toString()
      }
      return t
  }
  var N = function(e) {
      var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}
        , n = !1;
      t || (t = {});
      try {
          (n = document.createElement("span")).innerHTML = '<a id="JMLink_'.concat(e, '">&nbsp;</a>'),
          n.style.position = "fixed",
          n.style.top = "-99999px",
          n.addEventListener("copy", (function(e) {
              e.stopPropagation(),
              t.onCopy && (e.preventDefault(),
              t.onCopy(e.clipboardData))
          }
          )),
          document.body.appendChild(n),
          q(n);
          var o = document.execCommand("copy");
          if (!o)
              throw new Error("copy command was unsuccessful")
      } catch (e) {
          throw new Error("copy command was unsuccessful")
      }
      n && document.body.removeChild(n)
  }
    , M = /^(192\.168\.|169\.254\.|10\.|172\.(1[6-9]|2\d|3[01]))/
    , R = /([0-9]{1,3}(\.[0-9]{1,3}){3})/
    , B = /[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7}/;
  function H() {
      var e = window.localStorage.getItem("mw_ips");
      return null != e ? e.split(",") : []
  }
  function W() {
      return new Promise((function(e) {
          try {
              !function(e, t) {
                  var n = !1;
                  if (["RTCPeerConnection", "webkitRTCPeerConnection", "mozRTCPeerConnection", "RTCIceGatherer"].forEach((function(e) {
                      n || e in window && (n = !0)
                  }
                  )),
                  !n)
                      return C("webRTC is not supported"),
                      void e();
                  var o = !0
                    , i = !0;
                  !function(e, t) {
                      var n = {}
                        , o = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
                      if (!o) {
                          var i = document.getElementById("iframe");
                          if (!i)
                              return !1;
                          var r = i.contentWindow;
                          o = r.RTCPeerConnection || r.mozRTCPeerConnection || r.webkitRTCPeerConnection
                      }
                      if (!o)
                          return !1;
                      var a = new o({
                          iceServers: []
                      });
                      function s(t) {
                          if (t) {
                              var o = R.exec(t);
                              if (o) {
                                  var i = o[1]
                                    , r = t.match(M);
                                  void 0 === n[i] && e(i, r, !0),
                                  n[i] = !0
                              }
                          } else
                              e()
                      }
                      if (t && (a.addStream ? a.addStream(t) : a.addTrack && t.getTracks()[0] && a.addTrack(t.getTracks()[0], t)),
                      a.onicecandidate = function(e) {
                          e.candidate && e.candidate.candidate ? s(e.candidate.candidate) : s()
                      }
                      ,
                      !t)
                          try {
                              a.createDataChannel("jmlink", {})
                          } catch (e) {}
                      function u() {
                          a.localDescription.sdp.split("\n").forEach((function(e) {
                              e && 0 === e.indexOf("a=candidate:") && s(e)
                          }
                          ))
                      }
                      a.createOffer().then((function(e) {
                          a.setLocalDescription(e).then(u)
                      }
                      ))
                  }((function(t) {
                      t ? t.match(M) ? e(t, o = !1, i) : t.match(B) ? e(t, o, i = !1) : e(t, o, i) : e()
                  }
                  ), t)
              }((function(t, n, o) {
                  if (t && !n && o) {
                      var i = H();
                      t && !i.includes(t) && function(e) {
                          var t = window.localStorage.getItem("mw_ips");
                          if (null != t) {
                              var n = t + "," + e;
                              window.localStorage.setItem("mw_ips", n)
                          } else
                              window.localStorage.setItem("mw_ips", e)
                      }(t)
                  }
                  e(H())
              }
              ))
          } catch (t) {
              e(H())
          }
      }
      ))
  }
  function G(e, t) {
      var n = Object.keys(e);
      if (Object.getOwnPropertySymbols) {
          var o = Object.getOwnPropertySymbols(e);
          t && (o = o.filter((function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable
          }
          ))),
          n.push.apply(n, o)
      }
      return n
  }
  function K(e) {
      for (var t = 1; t < arguments.length; t++) {
          var n = null != arguments[t] ? arguments[t] : {};
          t % 2 ? G(Object(n), !0).forEach((function(t) {
              r()(e, t, n[t])
          }
          )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : G(Object(n)).forEach((function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
          }
          ))
      }
      return e
  }
  var V = P
    , Y = T
    , F = b
    , J = k
    , z = j
    , Q = window.navigator.userAgent
    , X = 0
    , $ = []
    , Z = {}
    , ee = {}
    , te = (localStorage,
  !1)
    , ne = /:\/\/(itunes|apps)\.apple\.com/
    , oe = /\.apk($|\?)/
    , ie = /app\.qq\.com/
    , re = function() {
      var e = new b(location.href).getParam("mw_fp");
      if (e)
          return e;
      var t = T.getCookie("mw_fp");
      if (t)
          return t;
      var n = O(32);
      return T.setCookie("mw_fp", n),
      n
  }()
    , ae = function() {
      function e() {
          d()(this, e);
          for (var t = arguments.length, n = new Array(t), o = 0; o < t; o++)
              n[o] = arguments[o];
          var i = this.formatConfig(n);
          this.jsonData = {},
          this.config = i.config;
          var r = this.config.autoInit;
          this.autoUl = i.config.autoUl,
          this.render = new V(this),
          this.clueType = L,
          this.wakeId = O(),
          C("~~~~ jmlink init begin"),
          C("env: ", e.env.model),
          C("ua: ", Q),
          C("os: ", J.ios ? "ios" : "android"),
          C("jmlink: ", this.config.jmlink),
          !1 !== r && this.init()
      }
      var t, n, o;
      return m()(e, [{
          key: "setClueType",
          value: function(e) {
              e && (this.clueType = e)
          }
      }, {
          key: "subDomainCheck",
          value: function(e) {
              var t = e.jmlink
                , n = new b(t);
              return e.jmlink = n.toString(),
              e.domainUrl = t,
              e
          }
      }, {
          key: "init",
          value: (o = h()(c.a.mark((function t() {
              var n, o, i, r, a, s, u, l;
              return c.a.wrap((function(t) {
                  for (; ; )
                      switch (t.prev = t.next) {
                      case 0:
                          if (n = this.config,
                          C("options: ", n),
                          !(c = n) || c.constructor !== Array) {
                              t.next = 7;
                              break
                          }
                          C("options length: ", n.length),
                          D(n, (function(t) {
                              C("option: ", t),
                              !0 !== (void 0 !== t.autoLaunchApp ? t.autoLaunchApp : t.autoRedirect) || o ? (t.autoLaunchApp = !1,
                              new e(t)) : (o = !0,
                              new e(t))
                          }
                          )),
                          t.next = 28;
                          break;
                      case 7:
                          if (!0 !== this.checkConfig(n)) {
                              t.next = 28;
                              break
                          }
                          return n = this.subDomainCheck(n),
                          !(i = n.button) || i.constructor !== Array && i.constructor !== NodeList || (r = [],
                          D(i, (function(e) {
                              e && (e.tagName && r.push(e),
                              e.constructor === NodeList && D(e, (function(e) {
                                  r.push(e)
                              }
                              )))
                          }
                          )),
                          n.button = r),
                          this.trackingH5(n.jmlink, !1),
                          t.next = 14,
                          this.initButton(n);
                      case 14:
                          if (a = t.sent,
                          !0 !== (s = void 0 !== n.autoLaunchApp ? n.autoLaunchApp : n.autoRedirect)) {
                              t.next = 28;
                              break
                          }
                          if ("shorturl" !== e.env.model) {
                              t.next = 22;
                              break
                          }
                          C("autoLaunchApp: ", s),
                          this.redirect(n, !1),
                          t.next = 28;
                          break;
                      case 22:
                          if (!z.isSupportedSchemeAuto()) {
                              t.next = 28;
                              break
                          }
                          if (!(u = J.android ? a.androidLink : a.iosLink)) {
                              t.next = 28;
                              break
                          }
                          return l = this.passParamsToApp(u, {
                              mw_auto: !0
                          }),
                          this.openScheme(l),
                          t.abrupt("return", a);
                      case 28:
                      case "end":
                          return t.stop()
                      }
                  var c
              }
              ), t, this)
          }
          ))),
          function() {
              return o.apply(this, arguments)
          }
          )
      }, {
          key: "formatConfig",
          value: function() {
              var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []
                , t = {}
                , n = e.length
                , o = [];
              return e.forEach((function(e, i) {
                  i !== n - 1 || "boolean" != typeof e ? e && e.constructor === Object ? o.push(e) : e && e.constructor === Array && (o = e) : t.autoInit = e
              }
              )),
              1 === o.length && o[0].constructor === Object && (o = o[0]),
              t.config = o,
              t
          }
      }, {
          key: "checkConfig",
          value: function(e) {
              if (!e.jmlink || !e.button)
                  return console.log("new JMLink([{"),
                  e.jmlink ? console.log('    "jmlink" : "' + e.jmlink + '",') : console.error('  "jmlink" : "{xxxx}", // 短链KEY是必要参数, 不能为空.'),
                  e.button ? console.log('    "button" : "a标签(DOM类型)"') : console.error('  "button" : "DOM类型, 您页面中打开App的链接." // 必要参数, 不能为空'),
                  console.log("}]);"),
                  !1;
              if (e.universal) {
                  var t = e.universal;
                  if (!t.startsWith("https://"))
                      return console.error(' "universal : ' + t + '", 必须是https://开头'),
                      !1;
                  if ("string" != typeof t)
                      return console.error(' "universal : ' + t + '", 必须是String类型'),
                      !1
              }
              return e.jmlink.match(/^https?/) || (e.jmlink = window.location.origin + "/" + e.jmlink.replace(/(^[\/\s]*)/, "").replace(/[\s\/]*$/, "")),
              !0
          }
      },
      {
          key: "copyText",
          value: function(e) {
              var t = ee[this.config.domainUrl] || this.jsonData
                , n = t.appKey;
              e || (e = J.ios ? t.iosLink : t.androidLink);
              var o = (new Date).getTime() + 60 * (t.sceneRecoveryTime || 0) * 1e3;
              C("overTime: ", o);
              try {
                  var i = this.passParamsToApp(e);
                  i += "&mw_ot=".concat(o),
                  console.log("copy text:", i);
                  var r = "[".concat(function(e, t) {
                      var n = "_qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM1234567890+/=";
                      e += "JMLINK";
                      try {
                          t = btoa(t)
                      } catch (e) {
                          t = btoa(encodeURIComponent(t))
                      }
                      for (var o = e.length, i = t.length, r = n.length, a = 1, s = 0; s < o; s++)
                          for (var u = (s + 199) * e.substring(s, s + 1).charCodeAt(); u > r; )
                              a += u % r,
                              u = parseInt(u / r);
                      var c = [];
                      for (s = 0; s < i + 6; s++) {
                          var l = n.indexOf(t.substring(s, s + 1));
                          c[s] || (c[s] = 0),
                          c[s] += a * l
                      }
                      var h = -1;
                      for (s = 0; s < c.length - 1; s++) {
                          (l = c[s]) > 0 && (h = s),
                          l >= r && (c[s + 1] += c[s] / r,
                          c[s] = c[s] % r)
                      }
                      for (u = "",
                      s = 0; s <= h; s++)
                          u += n.substring(c[s], c[s] + 1);
                      return u
                  }(n, i), "]");
                  N(r)
              } catch (e) {
                  console.error(e)
              }
          }
      },
      {
          key: "initButton",
          value: (n = h()(c.a.mark((function t(n, o) {
              var i, r, a, s, u, l, h, p, d, f, m, v, g, y, w, k, b, S, x, _, j, P, T, L = this;
              return c.a.wrap((function(t) {
                  for (; ; )
                      switch (t.prev = t.next) {
                      case 0:
                          for (i = n.button && "number" == typeof n.button.length ? n.button : [n.button],
                          r = (r = o ? "number" == typeof o.length ? o : [o] : null) && r.length > 0 ? r : i,
                          C("init btns", r),
                          s = 0,
                          u = r.length; s < u; s++)
                              (a = r[s]) && "function" == typeof a.setAttribute && (a.getAttribute("mlink-handling") || (a.setAttribute("href", "javascript:void(0)"),
                              l = function() {
                                  L.tracking(!0),
                                  L.copyText(),
                                  "function" == typeof n.beforeClick && n.beforeClick(),
                                  L.redirect(n, !0)
                              }
                              ,
                              a.onclick = l));
                          return t.next = 7,
                          this.loadData();
                      case 7:
                          if (h = t.sent,
                          p = this.config.jmlink,
                          d = this.getUniversalLink(h),
                          f = z.isSupportedUniversalLink() && h.iosUniversalLinkEnabled && d && !0 !== this.config.inapp,
                          m = J.ios ? h.iosLink : h.androidLink,
                          v = m && z.isSupportedSchemeOnlyClick(h),
                          g = f ? d : m,
                          y = this.getDownloadUrl(h),
                          this.applink = d,
                          this.config.universal && z.isSupportedUniversalLink() ? (w = JSON.parse(h.staticParams).landingPage,
                          g = this.passParamsToApp(this.config.universal, w)) : g = this.passParamsToApp(g),
                          C("scheme: ", g),
                          te) {
                              t.next = 22;
                              break
                          }
                          return te = !0,
                          t.next = 22,
                          this.sendEvent(g);
                      case 22:
                          if (this.setClueType(A),
                          "open" === e.env.model && (v || f) && !z.qq || (J.ios && J.iosVersion[0] < 9 || J.android) && z.qq)
                              for (k = function() {
                                  if (L.tracking(!0),
                                  L.copyText(g),
                                  "function" == typeof n.beforeClick && n.beforeClick(),
                                  setTimeout((function() {
                                      document.location.href = g
                                  }
                                  ), 600),
                                  !(f && J.ios && J.iosVersion[0] >= 9 && z.qq) && (L.sendEvent(g, !0),
                                  v)) {
                                      L.byClick = !0;
                                      var t = L.config.delayTime ? L.config.delayTime : 2e3
                                        , o = setTimeout((function() {
                                          if ("open" === e.env.model && (z.wx || z.weibo)) {
                                              var t = new F(L.config.domainUrl)
                                                , n = !1;
                                              !0 === L.config.resetWeixinMenu && (t.setParam("resetWeixinMenu", 1),
                                              n = n || !t.getParam("resetWeixinMenu")),
                                              !0 === L.config.preventDownload && (t.setParam("preventDownload", 1),
                                              n = n || !t.getParam("preventDownload")),
                                              n ? e.location(t.toString()) : L.openDownloadUrl(y)
                                          } else
                                              L.openDownloadUrl(y)
                                      }
                                      ), t);
                                      L.onAppLaunched((function() {
                                          clearTimeout(o)
                                      }
                                      ))
                                  }
                              }
                              ,
                              b = 0,
                              S = r.length; b < S; b++)
                                  x = r[b],
                                  _ = x.getAttribute("mlink-handling"),
                                  x && "function" == typeof x.setAttribute && !_ && (x.setAttribute("mlink-handling", !0),
                                  J.ios && J.iosVersion[0] < 9 && z.qq || J.ios && J.iosVersion[0] >= 9 && z.qq && !f ? (x.setAttribute("href", this.passParamsToApp(m)),
                                  x.addEventListener("click", k)) : (x.setAttribute("href", "javascript:void(0)"),
                                  x.onclick = function() {}
                                  ,
                                  x.removeEventListener("click", k),
                                  x.addEventListener("click", k)));
                          else if (J.ios && z.qq)
                              for (j = 0,
                              P = r.length; j < P; j++)
                                  (T = r[j]).setAttribute("mlink-handling", !0),
                                  T.setAttribute("href", this.passParamsToApp(m)),
                                  T.onclick = function() {
                                      L.copyText(L.passParamsToApp(m)),
                                      "function" == typeof n.beforeClick && n.beforeClick(),
                                      L.byClick = !0,
                                      L.tracking(!0),
                                      L.sendEvent(m, !0);
                                      var t = setTimeout((function() {
                                          if (L.setClueType(E),
                                          L.sendEvent(m, !0),
                                          L.isLandingPage(h))
                                              e.location(h.landingPage);
                                          else if (z.isSupportedYYB(h, p))
                                              L.openDownloadUrl(h.yybDownloadUrl || y);
                                          else if ("open" === e.env.model && (z.wx || z.weibo)) {
                                              var t = new F(L.config.domainUrl)
                                                , n = !1;
                                              !0 === L.config.resetWeixinMenu && (t.setParam("resetWeixinMenu", 1),
                                              n = n || !t.getParam("resetWeixinMenu")),
                                              !0 === L.config.preventDownload && (t.setParam("preventDownload", 1),
                                              n = n || !t.getParam("preventDownload")),
                                              n ? e.location(t.toString()) : L.openDownloadUrl(y, !1)
                                          } else
                                              L.setClueType(E),
                                              L.openDownloadUrl(y, !1)
                                      }
                                      ), 2500);
                                      return L.onAppLaunched((function() {
                                          clearTimeout(t)
                                      }
                                      )),
                                      !0
                                  }
                                  ;
                          return t.abrupt("return", h);
                      case 25:
                      case "end":
                          return t.stop()
                      }
              }
              ), t, this)
          }
          ))),
          function(e, t) {
              return n.apply(this, arguments)
          }
          )
      }, {
          key: "redirect",
          value: function(t, n) {
              var o = this;
              this.byClick = n;
              var i = this.config.jmlink;
              J.desktop && !n || (C("redirect click: ", n),
              this.loadData().then((function(r) {
                  var a = J.android ? r.androidLink : r.iosLink
                    , s = o.getDownloadUrl(r)
                    , u = o.isAppInstalled()
                    , c = o.getUniversalLink(r);
                  c = o.passParamsToApp(c),
                  c = new F(c).toString(),
                  o.applink = c,
                  o.jsonData = r;
                  var l = z.isSupportedUniversalLink() && !c;
                  if (a || !l)
                      if (z.isSupportedUniversalLink() && (r.iosUniversalLinkEnabled || o.config.universal) && c && !0 !== o.config.inapp) {
                          C("click ul");
                          var h = JSON.parse(r.staticParams).landingPage
                            , p = o.config.universal ? o.passParamsToApp(o.config.universal, h) : c;
                          n ? (o.sendEvent(p, n),
                          o.location(p, (function() {}
                          ), 600)) : o.showUniversalLinkButton(p)
                      } else {
                          if (C("click scheme"),
                          n && o.sendEvent(a, n),
                          z.wx || z.qq || z.weibo || z.qqbrowser || J.android && z.qzone) {
                              if (C("wx | qq | qqbrowser | weibo."),
                              !z.isSupportedSchemeOnlyClick(r)) {
                                  if ("open" === e.env.model) {
                                      var d = new F(o.applyParamsToShorturl(t.domainUrl));
                                      return !0 === t.resetWeixinMenu && d.setParam("resetWeixinMenu", 1),
                                      !0 === t.preventDownload && d.setParam("preventDownload", 1),
                                      void (window.location.href = d.toString())
                                  }
                                  C("isSupportedSchemeOnlyClick else");
                                  new F(t.jmlink).getParam("DO_NOT_TRACKING");
                                  return o.setClueType(E),
                                  z.wx && z.isSupportedYYB(r, i) ? void o.sendEvent(a, !0, 1, "{}", !1).then((function() {
                                      C("Redirect to yybDownloadUrl.", r.yybDownloadUrl),
                                      o.location(r.yybDownloadUrl)
                                  }
                                  )) : void o.createOpenInBrowserModal(!0)
                              }
                              if (C("isSupportedSchemeOnlyClick"),
                              n)
                                  return void o.openScheme(a, (function() {
                                      z.weibo ? o.createOpenInBrowserModal() : o.openDownloadUrl(s)
                                  }
                                  ), 2e3)
                          }
                          if (z.isSupportedScheme(r))
                              return C("default for scheme: " + a),
                              n || "shorturl" !== e.env.model ? void setTimeout((function() {
                                  C("isAppInstalled:" + u),
                                  !0 === u ? o.openScheme(a, (function() {
                                      o.render.showSchemeNotSupport(s)
                                  }
                                  )) : !1 === u ? o.openDownloadUrl(s, !0, a) : o.openScheme(a, (function() {
                                      C("Redirect to Download URL.1"),
                                      o.openDownloadUrl(s, !1, a)
                                  }
                                  ))
                              }
                              ), 100) : (C("supported scheme by click"),
                              void o.renderSchemePage(s));
                          C("not support scheme."),
                          o.render.showSchemeNotSupport(s)
                      }
                  else if (C("!scheme && noUniversalLink:" + !a + "-" + l),
                  (!z.isSupportedSchemeOnlyClick(r) || n) && "LANDING_PAGE" === r.jumpType && r.landingPage)
                      C("goto landing page.."),
                      e.location(r.landingPage);
                  else {
                      C("setting error.");
                      var f = J.ios ? "iOS" : "Android"
                        , m = "很抱歉，" + r.productName + "暂未提供" + f + "对应的跳转服务。";
                      o.render.showPage(".setting-error", {
                          title: m,
                          text: " "
                      })
                  }
              }
              )))
          }
      }, {
          key: "loadData",
          value: function(t, n) {
              var o = this
                , i = (arguments.length > 2 && void 0 !== arguments[2] && arguments[2],
              6e3)
                , r = this
                , a = this.config
                , s = a.timeout || i
                , u = a.timeoutCallback || function() {
                  r.render.showPage(".server-error")
              }
              ;
              (isNaN(+s) || +s < 0) && (s = i,
              console.warn("timeout  must be number and >0")),
              "function" != typeof u && (u = function() {
                  r.render.showPage(".server-error")
              }
              ,
              console.warn("timeoutCallback must be function")),
              n = n || this.config.jmlink,
              n = this.applyParamsToShorturl(n);
              var c = this.getApiHost(n);
              return ee[n] && !0 !== t ? new Promise((function(e) {
                  var t = ee[n];
                  o.jsonData = t,
                  C("loadData: ", t),
                  e(t)
              }
              )) : Z[n] && !0 !== t ? Z[n].then((function(e) {
                  return o.jsonData = e,
                  e
              }
              )) : (Z[n] = new Promise((function(t, i) {
                  var r = new XMLHttpRequest
                    , l = setTimeout((function() {
                      console.warn("time out: ", c),
                      u(a),
                      i()
                  }
                  ), s);
                  r.onreadystatechange = function() {
                      if (4 == r.readyState) {
                          if (200 == r.status) {
                              clearTimeout(l);
                              var a = o.parseJson(r.responseText);
                              if (a && o.render.init(a),
                              o.jsonData = a,
                              C("loadData: ", a),
                              "open" === e.env.model && a.ssd) {
                                  var s = o.getShorturlKey(n);
                                  o.config.jmlink = e.env.protocol + a.ssd + "/" + s,
                                  o.config.domainUrl = o.config.jmlink
                              }
                              if (!0 === o.config.preventDownload && a.ios9Link) {
                                  var u = new F(a.ios9Link);
                                  a.ios9Link = u.setParam("preventDownload", "1").toString()
                              }
                              if (!0 === o.config.preventDownload && a.failLink) {
                                  var c = new F(a.failLink);
                                  a.failLink = c.setParam("preventDownload", "1").toString()
                              }
                              ee[o.config.jmlink] = a,
                              t(a)
                          } else
                              o.render.showPage(".server-error"),
                              i();
                          delete Z[n]
                      }
                  }
                  ,
                  r.open("GET", c, !0),
                  r.setRequestHeader("Content-Type", "application/json;charset=UTF-8"),
                  r.send(null)
              }
              )),
              Z[n])
          }
      }, {
          key: "sendEvent",
          value: (t = h()(c.a.mark((function e(t, n) {
              var o, i, r, a, s, u, l, h, p, d, f, m, g, y, w, k, b = arguments;
              return c.a.wrap((function(e) {
                  for (; ; )
                      switch (e.prev = e.next) {
                      case 0:
                          return o = b.length > 2 && void 0 !== b[2] ? b[2] : 0,
                          i = b.length > 3 && void 0 !== b[3] ? b[3] : "",
                          r = !(b.length > 4 && void 0 !== b[4]) || b[4],
                          a = ee[this.config.domainUrl] || this.jsonData,
                          s = a.appKey,
                          t || (t = J.ios ? a.iosDownloadUrl : a.androidDownloadUrl),
                          s || (C("ak is undefined"),
                          alert("appKey上报失败")),
                          u = this.getEventUrl(null),
                          l = J.ios ? 1 : (J.android,
                          0),
                          h = 1 === l ? Q.match(/iPhone OS ([\d_\.]*)/) : Q.match(/Android ([\d_\.]*)/),
                          p = this.getShorturlKey(),
                          d = v(),
                          f = a.resX || d.width,
                          m = a.resY || d.height,
                          g = i || a.staticParams || "{}",
                          y = (new Date).getTime(),
                          X++,
                          C("fp: ", re),
                          C("sendEvent " + X + ": ", this.clueType),
                          e.next = 21,
                          W();
                      case 21:
                          return w = e.sent,
                          k = {
                              ak: s,
                              os: l,
                              osv: h ? (h[1] || "").replace(/_/g, ".") : "",
                              sr: [f, m].join("x"),
                              dp: r ? this.passParamsToApp(t) : t,
                              k: p,
                              yyb: o,
                              dt: JSON.parse(g),
                              dpr: window.devicePixelRatio || 1,
                              t: y,
                              localIP: w,
                              memory: navigator.deviceMemory ? 1024 * navigator.deviceMemory : null,
                              fp: re,
                              ct: this.clueType,
                              wakeId: this.wakeId
                          },
                          C("send event end: ", k),
                          e.abrupt("return", this.httpReport("POST", u, k));
                      case 25:
                      case "end":
                          return e.stop()
                      }
              }
              ), e, this)
          }
          ))),
          function(e, n) {
              return t.apply(this, arguments)
          }
          )
      }, {
          key: "applyParamsToShorturl",
          value: function(t, n) {
              var o = []
                , i = new e.Uri(location.href).getParams();
              for (var r in i)
                  -1 === t.indexOf(r) && "mw_fp" !== r && o.push(r + "=" + encodeURIComponent(i[r]));
              -1 === t.indexOf("mw_fp") && o.push("mw_fp=" + re);
              var a = this.config.params || {};
              for (var s in a)
                  o.push("mw_cp_" + s + "=" + encodeURIComponent(a[s]));
              var u = this.config.invtparams || {};
              for (var c in u)
                  o.push("mw_dynp_" + c + "=" + encodeURIComponent(u[c]));
              var l = this.config.plhparams || {};
              for (var h in l)
                  o.push(h + "=" + encodeURIComponent(l[h]));
              return e.debug && o.push("mw_debug=1"),
              (o = o.sort()).length > 0 && (t += (-1 === t.indexOf("?") ? "?" : "&") + o.join("&")),
              t
          }
      }, {
          key: "passParamsToApp",
          value: function(t, n) {
              if (t && -1 == t.indexOf("mw_mk")) {
                  var o = this.jsonData ? this.jsonData : {}
                    , i = []
                    , r = new F(this.config.jmlink)
                    , a = r.getParam("mw_ck");
                  if (a && a.match(/(%\w{2})+/g) && (a = decodeURIComponent(a)),
                  n && "object" === s()(n))
                      for (var u in n)
                          i.push([u, n[u]].join("="));
                  !1 !== this.config.downloadWhenUniversalLinkFailed && i.push("downloadWhenFailed=1"),
                  !0 === this.config.resetWeixinMenu && i.push("resetWeixinMenu=1"),
                  i.push(["mw_ck", encodeURIComponent(a || o.channel || "")].join("=")),
                  i.push(["mw_mk", encodeURIComponent(o.mLinkKey || "")].join("=")),
                  i.push(["mw_slk", encodeURIComponent(this.getShorturlKey() || "")].join("=")),
                  i.push(["mw_tags", encodeURIComponent(o.tags || "")].join("=")),
                  i.push(["mw_wid", this.wakeId].join("=")),
                  o.mLinkKey || console.warn("mlinkKey is undefined"),
                  this.getShorturlKey() || console.warn("shortKey is undefined"),
                  o.channel || console.warn("channel is undefined");
                  var c = this.config.params;
                  for (var l in c || (c = r.getParamsStartWith("mw_cp_") || {}),
                  c) {
                      var h = "mw_cp_" + l;
                      -1 == t.indexOf(h) && i.push([h, encodeURIComponent(c[l])].join("="))
                  }
                  var p = new e.Uri(location.href).getParams();
                  for (var d in p) {
                      var f = d + "=" + p[d];
                      t.indexOf(f) < 0 && i.push(f)
                  }
                  var m = this.config.invtparams;
                  for (var v in m || (m = r.getParamsStartWith("mw_dynp_") || {}),
                  m) {
                      var g = "mw_dynp_" + v;
                      -1 == t.indexOf(g) && i.push([g, encodeURIComponent(m[v])].join("="))
                  }
                  if (t.match(/\?/g) && t.match(/\?/g).length > 1) {
                      var y = t.indexOf("?")
                        , w = t.slice(0, y + 1)
                        , k = t.slice(y + 1)
                        , b = k.indexOf("?")
                        , S = k.indexOf("&mw");
                      t = w + k.slice(0, b + 1) + encodeURIComponent(k.slice(b + 1, S)) + k.slice(S)
                  }
                  o.appKey || (console.error("ak is undefined"),
                  alert("appKey获取失败")),
                  i.push(["mw_ak", o.appKey].join("=")),
                  i = i.join("&"),
                  t = t + (-1 === t.indexOf("?") ? "?" : "&") + i
              }
              return t
          }
      }, {
          key: "getApiHost",
          value: function(t) {
              var n = "?" + new F(t).queryString
                , o = e.env.protocol + e.env.ulHost + "/jmlink-share/" + e.env.av + "/open/" + this.getShorturlKey(t) + n;
              return new F(o).toString()
          }
      }, {
          key: "getUniversalLink",
          value: function(e) {
              return !!e && (e.ios9Link || e.failLink)
          }
      }, {
          key: "createOpenInBrowserModal",
          value: function(e) {
              var t = this
                , n = {}
                , o = {}
                , i = J.ios ? ".openinbrowser.ios" : ".openinbrowser.android"
                , r = this.jsonData || {}
                , a = r.iconUrl
                , s = this.render.getCustomStyles()
                , u = J.ios ? s.iosTipsImg : s.androidTipsImg;
              J.iosVersion[0] >= 9 && z.wx && !e && (i = ".openinbrowser-ios9-wechat",
              s.ios_wechat_json && (u = s.ios_wechat_json.bgUrl,
              !1 === s.ios_wechat_json.appLogoIsShow && (a = !1),
              o.appLogoIsShow = s.ios_wechat_json.appLogoIsShow,
              !1 === s.ios_wechat_json.appNameIsShow ? o.appName = !1 : (o.appName = r.productName,
              s.ios_wechat_json.appNameColor && (o.appNameColor = s.ios_wechat_json.appNameColor)),
              n.innerHTML = s.ios_wechat_json.btnText || "立即下载",
              n.backgroundColor = s.ios_wechat_json.btnColor || "#69f",
              n.color = s.ios_wechat_json.btnTextColor || "#fff"),
              n.onclick = function() {
                  t.sendEvent(r.iosLink, !0, 1).then((function() {
                      t.location("LANDING_PAGE" === r.jumpType ? r.landingPage : r.iosDownloadUrl)
                  }
                  ))
              }
              );
              var c = this.render.showPage(i, K({
                  appIcon: a
              }, o, {
                  btn: K({}, n)
              }));
              c && this.render.setTipsImg(c, u)
          }
      }, {
          key: "parseJson",
          value: function(e) {
              try {
                  var t = JSON.parse(e);
                  return t = t && t || {}
              } catch (e) {
                  return ""
              }
          }
      }, {
          key: "getProtocol",
          value: function(e) {
              var t = e.match(/^(\w+):\/\//);
              if (t)
                  return t[1]
          }
      }, {
          key: "getDownloadUrl",
          value: function(e) {
              var t = ee[this.config.domainUrl] || this.jsonData;
              (t.iosDownloadUrl || t.androidDownloadUrl) && (e = t);
              var n = e.landingPage
                , o = J.ios ? e.iosDownloadUrl : e.androidDownloadUrl;
              z.isSupportedYYB(e) && e.yybDownloadUrl && (o = e.yybDownloadUrl);
              var i, r = "";
              n && (i = new F(this.config.jmlink).getParams(),
              r = new F(n).setParams(i).toString());
              return "LANDING_PAGE" === e.jumpType ? r : o
          }
      }, {
          key: "isLandingPage",
          value: function(e) {
              return "LANDING_PAGE" === e.jumpType && e.landingPage
          }
      }, {
          key: "getEventUrl",
          value: function(t) {
              return (t || (this.jsonData ? this.jsonData.server : "") || e.env.protocol + e.env.ulHost) + "/jmlink-share/" + e.env.av + "/share/event"
          }
      }, {
          key: "getShorturlKey",
          value: function(e) {
              var t = e || this.config.jmlink
                , n = new F(t).path.split("/");
              return n = n[n.length - 1]
          }
      }, {
          key: "httpReport",
          value: function(e, t, n) {
              return new Promise((function(o, i) {
                  var r;
                  (r = new XMLHttpRequest) && (r.open(e, t, !0),
                  r.withCredentials = !0,
                  r.setRequestHeader("Method", e + " " + t + " HTTP/1.1"),
                  r.setRequestHeader("Content-type", "application/json"),
                  "POST" == e ? r.send(JSON.stringify(n)) : r.send(),
                  r.onreadystatechange = function() {
                      4 === parseInt(r.readyState) && (200 === parseInt(r.status) ? o() : i())
                  }
                  ),
                  setTimeout((function() {
                      o()
                  }
                  ), 2e3)
              }
              ))
          }
      }, {
          key: "trackingH5",
          value: function(t, n) {
              C("track: ", t);
              var o = e.env.protocol + e.env.trackEvent + "/jmlink-tracking/" + e.env.av + "/tracking/i";
              o += "open" == e.env.model ? "?ch=cst" : "?ch=dft";
              var i = this.getShorturlKey();
              o += "&cid=" + i,
              o += 1 == n ? "&action=clk" : "&action=exp";
              var r = (new Date).getTime() + Math.floor(1e13 * Math.random());
              o += "&rdm=" + r;
              var a = new Image;
              a.onerror = function() {
                  (new Image).src = o
              }
              ,
              setTimeout((function() {
                  a.src = o
              }
              ), 0)
          }
      }, {
          key: "tracking",
          value: function() {
              var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
              this.btnClickedCounter = this.btnClickedCounter || 0,
              (this.btnClickedCounter > 0 || e) && this.trackingH5(this.config.jmlink, !0),
              this.btnClickedCounter += 1
          }
      }, {
          key: "trackingDownload",
          value: function() {}
      }, {
          key: "openDownloadUrl",
          value: function(t, n, o) {
              if (C("open downloadURL: ", t),
              t) {
                  var i = new F(document.location.href);
                  if (!0 !== this.config.preventDownload && !i.getParam("preventDownload")) {
                      var r = ie.test(t)
                        , a = ne.test(t)
                        , s = oe.test(t);
                      if ((r || a) && (z.wx || z.qq || z.qqbrowser) || s)
                          return this.setClueType(E),
                          void this.sendEvent(t, !0).then((function() {
                              e.location(t)
                          }
                          ));
                      this.applink && this.byClick && e.location(this.applyParamsToShorturl(this.applink, !0))
                  }
              } else {
                  var u = J.ios ? "当前服务无苹果版本" : "当前服务无安卓版本";
                  this.render.showPage(".server-error", {
                      title: u,
                      text: " "
                  })
              }
          }
      }, {
          key: "renderSchemePage",
          value: function(e) {
              var t = this
                , n = this.render.getCustomStyles().count_down_json || {}
                , o = this.jsonData.iconUrl
                , i = document.querySelector(".mw-page.scheme-redirecting");
              i && !i.classList.contains("show") && this.render.showPage(".scheme-redirecting", {
                  bg: n.bgUrl,
                  appIcon: o,
                  appLogoIsShow: n.schemeLogoShow,
                  appName: n.schemeAppNameShow ? this.jsonData.productName : "",
                  appNameColor: n.schemeAppNameColor,
                  btn: [{
                      btnName: ".mw-open-app",
                      backgroundColor: n.schemeOpenBtnColor,
                      color: n.schemeOpenTextColor,
                      innerHTML: n.schemeOpenBtnText || "打开app"
                  }, {
                      btnName: ".mw-open-download",
                      backgroundColor: n.schemeLoadBtnColor,
                      color: n.schemeLoadTextColor,
                      innerHTML: n.schemeLoadBtnText || "下载app",
                      onclick: function() {
                          t.copyText(e),
                          t.setClueType(E),
                          t.sendEvent(e, !0).then((function() {
                              C("downloadURL: ", e),
                              location.href = e
                          }
                          ))
                      }
                  }]
              })
          }
      }, {
          key: "isAppInstalled",
          value: function() {
              var e = window.location.href
                , t = new F(e)
                , n = "weixin" === t.getParam("platform")
                , o = "1" === t.getParam("isappinstalled");
              return n ? !!o : "unknow"
          }
      }, {
          key: "getParam",
          value: function(e) {
              var t = window.document.location.href.match(new RegExp("[?&]" + e + "=(\\w*)[&$]"));
              return t ? t[1] : ""
          }
      }, {
          key: "onAppLaunched",
          value: function(e) {
              e = "function" == typeof e ? e : function() {}
              ,
              C("onAppLaunched");
              window.addEventListener("beforeunload", (function(e) {
                  C(e.type)
              }
              ));
              document.addEventListener("visibilitychange", (function(t) {
                  var n = t.type;
                  C("event", n),
                  U() && e()
              }
              ))
          }
      }, {
          key: "openScheme",
          value: function(e, t, n) {
              C("system" + J.ios + ";" + J.ipad + J.iosVersion[0]);
              var o = t;
              J.ios && J.iosVersion[0] >= 9 ? (C("Scheme with location."),
              this.location(e, o, n)) : z.huawei && !z.chrome ? (C("Scheme with iframe."),
              this.openAppWithIframe(e, o, n)) : J.android && z.chrome ? (C("Scheme with location."),
              this.location(e, o, n)) : J.ios && z.chrome ? (C("Scheme with window.open."),
              this.openAppWithWindowOpen(e, o, n)) : this.location(e, o, n)
          }
      }, {
          key: "openAppWithWindowOpen",
          value: function(e, t, n) {
              var o;
              e = this.passParamsToApp(e);
              try {
                  o = window.open(e)
              } catch (e) {}
              o ? window.close() : t()
          }
      }, {
          key: "location",
          value: function(t) {
              var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : function() {}
                , o = arguments.length > 2 ? arguments[2] : void 0;
              t = this.passParamsToApp(t),
              e.location(t);
              var i = setTimeout((function() {
                  n()
              }
              ), o || (this.byClick ? 1500 : 20));
              this.onAppLaunched((function() {
                  clearTimeout(i)
              }
              ))
          }
      }, {
          key: "showSchemeButton",
          value: function(e) {
              this.showRedirectButton(e, 0)
          }
      }, {
          key: "showUniversalLinkButton",
          value: function(e) {
              this.showRedirectButton(e, 1)
          }
      }, {
          key: "showRedirectButton",
          value: function(e) {
              arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
              var t = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null
                , n = (t || this.config.button,
              ee[this.config.jmlink] || {})
                , o = n.productName || n.appName
                , i = n.iconUrl || n.appIcon
                , r = this.render.getCustomStyles()
                , a = r.open_app_json;
              null === t && this.render.showPage(".redirect-with-button", {
                  appIcon: i,
                  appName: o,
                  type: a.type,
                  bg: a.bgUrl,
                  btn: {
                      innerHTML: a.btnText || "打开App",
                      color: a.btnTextColor || "",
                      backgroundColor: a.btnColor || "",
                      btnDistanceBottom: a.btnDistanceBottom || "20%",
                      href: z.aliPay && e
                  }
              })
          }
      }, {
          key: "openAppWithIframe",
          value: function(e, t, n) {
              e = this.passParamsToApp(e);
              var o = document.createElement("iframe");
              o.style.width = "0px",
              o.style.height = "0px",
              o.style.overflow = "hidden",
              o.id = "mlinkIframeLauncher",
              o.src = e,
              document.body.appendChild(o);
              var i = setTimeout(t, n || 500);
              this.onAppLaunched((function() {
                  clearTimeout(i)
              }
              ))
          }
      }], [{
          key: "location",
          value: function(e) {
              e && (document.location.href = e)
          }
      }, {
          key: "isIosLE9",
          value: function() {
              return J.ios && J.iosVersion[0] >= 9
          }
      }, {
          key: "loadScript",
          value: function() {
              for (var e = [], t = document.querySelector("head"), n = arguments.length, o = new Array(n), i = 0; i < n; i++)
                  o[i] = arguments[i];
              return o.forEach((function(n) {
                  var o = document.createElement("script")
                    , i = new Promise((function(e, n) {
                      o.onload = function() {
                          e(),
                          t.removeChild(o)
                      }
                      ,
                      o.onerror = function() {
                          n(),
                          t.removeChild(o)
                      }
                  }
                  ));
                  o.src = n,
                  t.appendChild(o),
                  e.push(i)
              }
              )),
              Promise.all(e)
          }
      }, {
          key: "ready",
          value: function(e) {
              var t = document.readyState;
              "interactive" === t || "complete" === t ? e() : ($.push(e),
              document.addEventListener("readystatechange", (function() {
                  var e;
                  if ("interactive" === (t = document.readyState) || "complete" === t)
                      for (; $.length > 0; )
                          "function" == typeof (e = $.shift()) && e()
              }
              )))
          }
      }]),
      e
  }();
  ae.Cookie = Y,
  ae.os = J,
  ae.Uri = F,
  ae.browser = z,
  ae.device = o,
  ae.Render = V,
  ae.Version = "1.3.3",
  ae.env = {
      prefix: "jmlk.co",
      ulHost: "share-jmlink.jpush.cn",
      env: "prod",
      model: "open",
      av: "v1",
      trackEvent: "tracking-jmlink.jpush.cn",
      protocol: "https://"
  },
  window.JMLink = ae;
  var se, ue = t.default = ae;
  se = 1,
  "shorturl" !== ae.env.model && ae.ready((function() {
      setTimeout((function() {
          var e = document.querySelectorAll("a[data-jmlink]");
          if (0 !== e.length)
              return D(e, (function(e) {
                  var t = e.getAttribute("href")
                    , n = !!e.getAttribute("data-jmlink")
                    , o = e.getAttribute("data-jmlink-scan")
                    , i = "true" === e.getAttribute("data-auto")
                    , r = e.getAttribute("data-params");
                  t && (t = new F(t)),
                  r && (r = (r = r.slice(1, -1).split(",")).map((function(e) {
                      return e.split(":").map((function(e) {
                          return e.trim()
                      }
                      ))
                  }
                  )).reduce((function(e, t) {
                      return e[t[0]] = t[1],
                      e
                  }
                  ), {})),
                  n && t && t.host && !o && (e.setAttribute("data-jmlink-scan", se),
                  C("scanlink: ", se),
                  se += 1,
                  new ae({
                      jmlink: t.toString(),
                      button: e,
                      autoLaunchApp: i,
                      params: r
                  }))
              }
              )),
              se
      }
      ), 500)
  }
  ))
}
]);
