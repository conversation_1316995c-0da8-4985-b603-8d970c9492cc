//= require jquery
//= require jquery_ujs
//= require echarts.min
//= require cocoon
//= require hyper-config
//= require vendor.min
//= require ant-notice
//= require laydate
//= require ckeditor
//= require pagy
//= require img-preview
//= require app
//= require dragula.min
//= require clipboard
//= require ttoast
//= require vue
//= require naive-ui
//= require echarts-theme
//= require select2.full.min

$(document).on('change', '.file-input', function(){
    var _this = $(this)
    // 先获取用户上传的文件对象
    var fileObj = this.files[0];
    // 生成一个文件读取的内置对象
    var fileReader = new FileReader();
    // 将文件对象传递给内置对象
    fileReader.readAsDataURL(fileObj); //这是一个异步执行的过程，所以需要onload回调函数执行读取数据后的操作
    // 将读取出文件对象替换到img标签
    fileReader.onload = function(){  // 等待文件阅读器读取完毕再渲染图片
        _this.parent().find(".file-input-replace").attr("src", fileReader.result);
    }
});
