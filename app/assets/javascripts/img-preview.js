$(document).on('click', '.img-preview', function(){
  var img_url = $(this).attr("src")
  $("body").append('<div class="img-previewed">\
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">\
      <img src="'+img_url+'" class="animate__animated animate__zoomIn animate__faster" style="width: 400px; height: auto;">\
    </div>')
})

$(document).on('click', '.img-previewed', function(){
  $(this).remove()
});