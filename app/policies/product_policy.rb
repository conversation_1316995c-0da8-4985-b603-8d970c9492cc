# frozen_string_literal: true

class ProductPolicy < ApplicationPolicy
  def index?
    check_auth("products", "index")
  end

  def show?
    index?
  end

  def create?
    check_auth("products", "create")
  end

  def new?
    create?
  end

  def update?
    check_auth("products", "update")
  end

  def update_que?
    update?
  end

  def show_status?
    update?
  end

  def product_status?
    update?
  end

  def edit?
    update?
  end

  def destroy?
    check_auth("products", "destroy")
  end
end
