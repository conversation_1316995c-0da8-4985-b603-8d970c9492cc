# frozen_string_literal: true

class PushMessagePolicy < ApplicationPolicy
  def index?
    check_auth("push_messages", "index")
  end

  def new?
    create?
  end

  def create?
    check_auth("push_messages", "create")
  end

  def update?
    check_auth("push_messages", "update")
  end

  def edit?
    update?
  end

  def show?
    check_auth("push_messages", "index")
  end

  def destroy?
    check_auth("push_messages", "destroy")
  end

  def template_content?
    create?
  end

  def revoke?
    create?
  end
end
