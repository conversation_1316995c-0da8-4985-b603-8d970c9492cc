# frozen_string_literal: true

class ApplicationPolicy
  attr_reader :user, :record

  def initialize(user, record)
    @user = user
    @record = record
  end

  def index?
    false
  end

  def show?
    false
  end

  def create?
    false
  end

  def new?
    create?
  end

  def update?
    false
  end

  def edit?
    update?
  end

  def destroy?
    false
  end

  class Scope
    def initialize(user, scope)
      @user = user
      @scope = scope
    end

    def resolve
      raise NotImplementedError, "You must define #resolve in #{self.class}"
    end

    private

    attr_reader :user, :scope
  end

  private

  def pundit_user
    current_admin_user
  end

  def check_auth(visit_controller, visit_action, visit_user=user)
    return true if visit_user.is_admin?

    manage_action = ManageController.find_by(word: visit_controller)&.manage_actions&.find_by(word: visit_action)
    return false if manage_action.blank?

    if ManageRoleAction.where(manage_role_id: visit_user.manage_roles.ids, manage_action_id: manage_action.id).present?
      return true
    else
      return false
    end
  end
end
