class Jiguang
  # 每次推送最多 1000 个
  def self.send_message(registration_ids, message, title)
    url = "https://api.jpush.cn/v3/push"
    headers = {
      "Content-Type": "application/json; charset=utf-8",
      "Authorization": Settings.jiguang.authorization
    }

    body = {
      platform: ["android", "ios"],
      audience: {
        registration_id: registration_ids
      },
      notification: {
        alert: message,
        android: {
          title: title
        },
        ios: {
          alert: {
            "body": message,
            "title": title
          },
          sound: 'default'
        }
      },
      options: {
        apns_production: Rails.env.production?
      }
    }

    response = Faraday.post(url, body.to_json, headers)
    p JSON.parse(response.body)
    JSON.parse(response.body)
  end
end
