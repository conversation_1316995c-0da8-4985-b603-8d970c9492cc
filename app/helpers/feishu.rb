class Feishu
  def self.send_message(receive_id, phone, message)
    url = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=user_id"
    headers = {
      "Content-Type": "application/json; charset=utf-8",
      "Authorization": "Bearer #{get_access_token}"
    }

    body = {
      "receive_id": receive_id,
      "msg_type": "interactive",
      "content": {
        "header": {
          "title": {
              "tag": "plain_text",
              "content": "用户获取验证码异常告警"
          },
          "template": "red"
        },
        "elements": [
          {
            "tag": "div",
            "fields": [
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**手机号：**\n#{phone}"
                }
              },
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**发生时间：**\n#{Time.now.strftime("%Y-%m-%d %H:%M:%S")}"
                }
              },
              {
                "is_short": false,
                "text": {
                  "tag": "lark_md",
                  "content": ""
                }
              },
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**异常信息：**\n#{message}"
                }
              },
              {
                "is_short": false,
                "text": {
                  "tag": "lark_md",
                  "content": ""
                }
              }
            ]
          }
        ]
      }.to_json
    }
    Faraday.post(url, body.to_json, headers)
  end

  def self.send_product_is_sale_notice(receive_id, product, sale_time)
    product = product.reload
    url = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=user_id"
    headers = {
      "Content-Type": "application/json; charset=utf-8",
      "Authorization": "Bearer #{get_access_token}"
    }

    body = {
      "receive_id": receive_id,
      "msg_type": "interactive",
      "content": {
        "header": {
          "title": {
              "tag": "plain_text",
              "content": "#{product.is_sale? ? '商品定时开售提醒' : '商品定时开售异常提醒'}"
          },
          "template": "#{product.is_sale? ? 'green' : 'red'}"
        },
        "elements": [
          {
            "tag": "div",
            "fields": [
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**商品名称：**\n#{product.name}"
                }
              },
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**开售时间：**\n#{sale_time}"
                }
              },
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**当前状态：**\n#{product.is_sale? ? '已开售' : '未开售'}"
                }
              },
            ]
          }
        ]
      }.to_json
    }
    Faraday.post(url, body.to_json, headers)
  end

  private

  def self.get_access_token
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    headers = {
      "Content-Type": "application/json; charset=utf-8"
    }
    body = {
      "app_id": Settings.feishu.app_id,
      "app_secret": Settings.feishu.app_secret
    }
    Rails.cache.fetch("feishu_access_token", expires_in: 1.hours) do
      response = Faraday.post(url, body.to_json, headers)
      JSON.parse(response.body)["tenant_access_token"]
    end
  end
end
