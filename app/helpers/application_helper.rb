module ApplicationHelper
  extend Grape::API::Helpers

  # 用户授权
  def user_authenticate!
    user_token = headers["Usertoken"]
    return true if (@user = current_user || User.user_status.where(authentication_token: user_token).first)

    unauthorized!("未授权访问")
  end

  # 获取当前用户
  def current_user
    @user = User.find_by(phone: "12312345678") if params[:api_key] == "81720767-221c-c4a0-161a-4de6cba2016e"
    @user
  end

  # 错误处理
  def forbidden!(reason = nil)
    render_api_error_with_reason!(403, "403 Forbidden", reason)
  end

  def bad_request!(reason = nil)
    render_api_error_with_reason!(400, "400 Bad request", reason)
  end

  def bad_request_missing_attribute!(attribute)
    bad_request!("\"#{attribute}\" not given")
  end

  def not_found!(resource = nil)
    message = ["404"]
    message << resource if resource
    message << "Not Found"
    render_api_error!(message.join(" "), 404)
  end

  def unauthorized!(reason = nil)
    render_api_error_with_reason!(401, "401 Unauthorized", reason)
  end

  def not_allowed!(message = nil)
    render_api_error!(message || "405 Method Not Allowed", :method_not_allowed)
  end

  def not_acceptable!
    render_api_error!("406 Not Acceptable", 406)
  end

  def service_unavailable!(message = nil)
    render_api_error!(message || "503 Service Unavailable", 503)
  end

  def conflict!(message = nil)
    render_api_error!(message || "409 Conflict", 409)
  end

  def unprocessable_entity!(message = nil)
    render_api_error!(message || "422 Unprocessable Entity", :unprocessable_entity)
  end

  def file_too_large!
    render_api_error!("413 Request Entity Too Large", 413)
  end

  def not_modified!
    render_api_error!("304 Not Modified", 304)
  end

  def no_content!
    render_api_error!("204 No Content", 204)
  end

  def created!
    render_api_error!("201 Created", 201)
  end

  def accepted!
    render_api_error!("202 Accepted", 202)
  end

  def render_validation_error!(model, status = 400)
    if model.errors.any?
      render_api_error!(model_errors(model).messages || "400 Bad Request", status)
    end
  end

  def model_errors(model)
    model.errors
  end

  def render_api_error_with_reason!(status, message, reason)
    message = [message]
    message << "- #{reason}" if reason
    render_api_error!(message.join(" "), status)
  end

  def render_api_error!(message, status)
    render_structured_api_error!({ "message" => message }, status)
  end

  def render_structured_api_error!(hash, status)
    # Use this method instead of `render_api_error!` when you have additional top-level
    # hash entries in addition to 'message' which need to be passed to `#error!`
    error!(hash, status, header)
  end

  def mini_program_token
    Rails.cache.fetch("mini_program_token", expires_in: 1.hour) do
      response = Faraday.get("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=#{Settings.mini_program.appid}&secret=#{Settings.mini_program.app_secret}")
      result = JSON.parse(response.body)
      Rails.logger.info '----------wechat_mini_program----access_token--------'
      Rails.logger.info result['access_token']
      result['access_token']
    end
  end
end
