module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def tradeStatus(status)
    case status
    when 0
      'JH_01'
    when 1
      'JH_02'
    when 2
      'JH_03'
    when 3, 4, 200
      'JH_04'
    when 5, 6
      'JH_05'
    else
      'JH_98'
    end
  end

  def refundStatus(status, repair)
    result = ''
    if status.to_i == 11 # 申请退款
      result = 'JH_01'
    elsif status.to_i == 6 # 同意退款
      result = 'JH_06'
    elsif status.to_i == 21 # 同意退款
      result = 'JH_03'
    elsif status.to_i == 1 || status.to_i == 2 || status.to_i == 3 || status.to_i == 4 || status.to_i == 200
      result = 'JH_07'
    else
      result = 'JH_99'
    end
    result
  end

  def goodsStatus(status)
    result = ''
    if !status.present?
      result = 'JH_98'
    elsif status == 2
      result = 'JH_02'
    elsif status == 1
      result = 'JH_01'
    else
      result = 'JH_98'
    end
    result
  end
end
