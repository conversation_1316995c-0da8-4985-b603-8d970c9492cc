# == Schema Information
#
# Table name: manage_actions
#
#  id                   :bigint           not null, primary key
#  name                 :string(255)
#  que                  :integer
#  word                 :string(255)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  manage_controller_id :integer
#
# Indexes
#
#  index_manage_actions_on_manage_controller_id  (manage_controller_id)
#
class ManageAction < ApplicationRecord
  belongs_to :manage_controller
  has_many :manage_role_actions, dependent: :destroy
  has_many :manage_roles, through: :manage_role_actions

  validates :word, presence: true, uniqueness: { scope: :manage_controller_id }
end
