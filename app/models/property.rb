# == Schema Information
#
# Table name: properties
#
#  id         :bigint           not null, primary key
#  level      :integer          default(0)
#  name       :string(255)
#  que        :integer          default(0)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  parent_id  :integer
#
class Property < ApplicationRecord
  has_many :skus, :through => :sku_properties, dependent: :destroy
  belongs_to :parent, class_name: "Property", foreign_key: "parent_id", optional: true
end
