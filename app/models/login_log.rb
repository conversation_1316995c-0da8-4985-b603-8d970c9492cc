# == Schema Information
#
# Table name: login_logs
#
#  id            :bigint           not null, primary key
#  address       :string(255)
#  ip            :string(255)
#  is_forbidden  :boolean
#  is_lastest    :boolean
#  login_at      :datetime
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_user_id :integer
#
# Indexes
#
#  index_login_logs_on_admin_user_id  (admin_user_id)
#
class LoginLog < ApplicationRecord
  belongs_to :admin_user
end
