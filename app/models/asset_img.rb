# == Schema Information
#
# Table name: asset_imgs
#
#  id           :bigint           not null, primary key
#  image        :string(255)
#  resoure_type :string(255)
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  resoure_id   :integer
#
class AssetImg < ApplicationRecord
  default_scope { order(created_at: "desc") }

  mount_uploader :image, ImageUploader
  belongs_to :resoure, polymorphic: true, optional: true, counter_cache: true
end
