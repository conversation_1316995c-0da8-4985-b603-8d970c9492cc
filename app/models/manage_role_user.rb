# == Schema Information
#
# Table name: manage_role_users
#
#  id             :bigint           not null, primary key
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  admin_user_id  :integer
#  manage_role_id :integer
#
# Indexes
#
#  index_manage_role_users_on_admin_user_id   (admin_user_id)
#  index_manage_role_users_on_manage_role_id  (manage_role_id)
#
class ManageRoleUser < ApplicationRecord
  belongs_to :manage_role
  belongs_to :admin_user
end
