# == Schema Information
#
# Table name: comments
#
#  id               :bigint           not null, primary key
#  asset_imgs_count :integer          default(0)
#  comments_count   :integer          default(0)
#  content          :string(255)
#  is_buy           :boolean          default(TRUE)
#  is_owner         :boolean          default(FALSE)
#  que              :integer
#  status           :boolean          default(TRUE)
#  user_nickname    :string(255)
#  userlikes_count  :integer          default(0)
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  parent_id        :integer
#  product_id       :integer
#  sku_id           :integer
#  user_id          :integer
#
class Comment < ApplicationRecord
  default_scope { order(created_at: "desc") }

  belongs_to :user
  belongs_to :product
  has_many :asset_imgs, as: :resoure, counter_cache: :asset_imgs_count
  belongs_to :parent, class_name: "Comment", optional: true, foreign_key: "parent_id", counter_cache: true
  has_many :comments, class_name: "Comment", foreign_key: "parent_id", counter_cache: true
  has_many :userlikes, as: :resoure, counter_cache: true
  has_many :reports, as: :resoure, counter_cache: true

  # attr_accessor :asset_imgs_count

  scope :comment_status, -> { where(status: true) }

  before_save :change_product_is_buy

  def change_product_is_buy
    if new_record?
      order_ids = user.orders.order_status_pay
      flag = false
      order_ids.each do |order|
        if order.order_details.where(product_id: product_id).first
          flag = true
          break
        end
      end
      self.is_buy = flag
    end
  end
end
