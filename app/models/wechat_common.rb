class WechatCommon
  #计算签名
  def self.computational_signature(current_hash)
    stringA = current_hash.sort.map{|k,v| "#{k}=#{v}" }.join("&")
    api_key = Settings.mini_program.api_key
    Rails.logger.info "--------#{stringA}_start-------"
    stringSignTemp="#{stringA}&key=#{api_key}"
    sign = Digest::MD5.hexdigest(stringSignTemp).upcase
    Rails.logger.info "--------#{sign}_start-------"
    current_hash.merge(sign: sign)
  end

  #请求url
  def self.request_url(current_url, current_hash, flag)
    current_body = current_hash.to_xml(root: 'xml', dasherize: false)
    rpost = HTTParty.post(current_url, body: current_body, headers: {'Content-Type' => 'application/xml'})

    request_result = Hash.from_xml(rpost)["xml"]

    Rails.logger.info "--------#{flag}_start-------"
    Rails.logger.info request_result
    Rails.logger.info "--------#{flag}_end---------"
    request_result
  end

  #带证书请求url(current_url, current_hash, flag)
  def self.certificate_request_url(current_url, current_hash, flag)
    current_body = current_hash.to_xml(root: 'xml', dasherize: false)

    uri = URI.parse(current_url)
    http = Net::HTTP.new(uri.host, uri.port)
    request = Net::HTTP::Post.new(uri.request_uri)
    request.body = current_body
    request["Content-Type"] = "text/xml"
    http.use_ssl = true
    cert_file = File.read("#{Rails.root}/config/initializers/wxapp_rsa_cert.pem")
    key_file = File.read("#{Rails.root}/config/initializers/wxapp_rsa_private.pem")

    http.cert = OpenSSL::X509::Certificate.new(cert_file)
    http.key = OpenSSL::PKey::RSA.new(key_file)
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    result = http.request(request)
    request_result = Hash.from_xml(result.body)['xml']
    Rails.logger.info "--------#{flag}_start-------"
    Rails.logger.info request_result
    Rails.logger.info "--------#{flag}_end---------"
    request_result
  end

  # 小程序支付信息构建
  def self.wxapp_pay_info(prepay_id)
    appid = Settings.mini_program.appid
    api_key = Settings.mini_program.api_key
    nonce_str = SecureRandom.hex(16).to_s
    timestamp = Time.now.to_i.to_s
    package = "prepay_id=#{prepay_id}"
    sign_type = "MD5"
    pay_sign = Digest::MD5.hexdigest("appId=#{appid}&nonceStr=#{nonce_str}&package=#{package}&signType=#{sign_type}&timeStamp=#{timestamp}&key=#{api_key}")
    { timestamp: timestamp, nonce_str: nonce_str, package: package, sign_type: sign_type, pay_sign: pay_sign }
  end

  def self.rsa_encrypt(data)
    key_file = File.read("#{Rails.root}/config/initializers/wxapp_rsa_cert.pem")
    rsa = OpenSSL::PKey::RSA.new(key_file)
    rsa_data = rsa.public_encrypt(data, OpenSSL::PKey::RSA::PKCS1_OAEP_PADDING)
    Base64.strict_encode64(rsa_data)
  end

  # 退款解密
  def self.decrypt_aes_256_ecb(encrypted_text)
    # 步骤1：对加密串A做base64解码，得到加密串B
    decoded_text = Base64.decode64(encrypted_text)

    # 步骤2：对商户key做md5，得到32位小写key*
    md5_key = Digest::MD5.hexdigest(Settings.mini_program.api_key)

    # 步骤3：用key*对加密串B做AES-256-ECB解密（PKCS7Padding）
    decipher = OpenSSL::Cipher.new('AES-256-ECB')
    decipher.decrypt
    decipher.key = md5_key

    decrypted_text = decipher.update(decoded_text) + decipher.final

    return decrypted_text
  end
end
