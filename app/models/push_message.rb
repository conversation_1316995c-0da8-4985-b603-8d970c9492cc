# == Schema Information
#
# Table name: push_messages
#
#  id                  :bigint           not null, primary key
#  actual_push_count   :integer
#  crowd               :json
#  expected_push_count :integer
#  include_jpush       :boolean
#  include_sms_message :boolean
#  pushed_at           :datetime
#  status              :integer
#  template_content    :text(65535)
#  timing              :integer
#  title               :string(255)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  msg_id              :string(255)
#  product_id          :integer
#  template_id         :integer
#
# Indexes
#
#  index_push_messages_on_status  (status)
#
class PushMessage < ApplicationRecord

  # 目标人群
  SUBSCRIPTION_PEOPLE = '1'.freeze
  COLLECT_PEOPLE = '2'.freeze
  ALL_PEOPLE = '3'.freeze

  belongs_to :product

  enum status: { pending: 1, pushing: 2, pushed: 3, revoked: 4 }
  enum timing: { definite_time: 1, immediately: 2 }
  enum template_id: { product_start_sale_notify: 1, product_pre_sale_notify: 2, custom_notify: 3 }

  before_save :check_push_message
  before_create :set_init_status
  after_save :update_push_count
  after_create :push_message_content

  def self.push_template(select_product_name, select_template_id, push_message=nil)
    {
      product_start_sale_notify: {
        "title": "#{select_product_name}已开始发售",
        "content": '立即购买'
      },
      product_pre_sale_notify: {
        "title": "#{select_product_name}已开始预售",
        "content": '立即预订'
      },
      custom_notify: {
        "title": push_message&.title || '',
        "content": push_message&.template_content || ''
      }
    }[select_template_id.to_sym]
  end

  def registration_ids
    User.where(id: select_user_ids[:all_user_ids]).where.not(register_id: ["", nil]).pluck(:register_id)
  end

  def phones
    old_users_arr = CSV.open(Rails.root.to_s + '/db/old_users.csv').to_a.flatten
    all_user_phones = User.where.not(phone: ["", nil]).pluck(:phone)
    all_user_phones = (all_user_phones + old_users_arr).uniq
    sub_user_phones = User.where(id: select_user_ids[:sub_user_ids]).where.not(phone: ["", nil]).pluck(:phone)
    collect_user_phones = User.where(id: select_user_ids[:collect_user_ids]).where.not(phone: ["", nil]).pluck(:phone)
    collect_user_phones = collect_user_phones - (collect_user_phones & sub_user_phones)
    {all_user_phones: all_user_phones, sub_user_phones: sub_user_phones, collect_user_phones: collect_user_phones}
  end

  def select_user_ids
    user_ids = []
    sub_user_ids = []
    collect_user_ids = []
    if crowd.include?(ALL_PEOPLE)
      user_ids = User.ids
      return { all_user_ids: user_ids, sub_user_ids: sub_user_ids, collect_user_ids: collect_user_ids }
    else
      if crowd.include?(SUBSCRIPTION_PEOPLE)
        sub_user_ids = Subscription.where(product_id: product_id, is_subscription: true).pluck(:user_id)
      end

      if crowd.include?(COLLECT_PEOPLE)
        collect_user_ids = Assemble.where(product_id: product_id, is_like: true).pluck(:user_id)
      end
    end
    { all_user_ids: sub_user_ids + collect_user_ids, sub_user_ids: sub_user_ids, collect_user_ids: collect_user_ids }
  end

  def crowd_zh
    crowd_arr = []
    crowd_arr << '订阅商品的用户' if crowd.include?(SUBSCRIPTION_PEOPLE)
    crowd_arr << '收藏商品的用户' if crowd.include?(COLLECT_PEOPLE)
    crowd_arr << '系统所有用户' if crowd.include?(ALL_PEOPLE)
    crowd_arr.join('、')
  end

  private

  def check_push_message
    if status.in?([nil, 'pending']) && timing == 'immediately'
      self.pushed_at = Time.current + 1.minute
    end

    if self.custom_notify?
      self.include_jpush = true
      self.include_sms_message = false
    end

    if id && pushed_at_changed?
      PushMessageJob.perform_at(self.pushed_at, id, self.pushed_at.to_s)
    end
  end

  def set_init_status
    self.status = :pending
  end

  def update_push_count
    self.update_columns(expected_push_count: select_user_ids[:all_user_ids].count)
  end

  def push_message_content
    PushMessageJob.perform_at(self.pushed_at, id, self.pushed_at.to_s)
  end
end
