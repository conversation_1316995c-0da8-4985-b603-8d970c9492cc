# == Schema Information
#
# Table name: keycap_configs
#
#  id                    :bigint           not null, primary key
#  enable_rt_mode        :boolean
#  press_trigger_point   :float(24)
#  release_trigger_point :float(24)
#  trigger_point         :float(24)
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  career_preset_id      :integer
#  keycap_id             :integer
#
# Indexes
#
#  index_keycap_configs_on_career_preset_id  (career_preset_id)
#  index_keycap_configs_on_keycap_id         (keycap_id)
#
class KeycapConfig < ApplicationRecord
  belongs_to :keycap
  belongs_to :career_preset
end
