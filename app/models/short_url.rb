# == Schema Information
#
# Table name: short_urls
#
#  id          :bigint           not null, primary key
#  click_count :integer          default(0)
#  redirect_to :string(255)
#  uniq_key    :string(255)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
# Indexes
#
#  index_short_urls_on_uniq_key  (uniq_key) UNIQUE
#
class ShortUrl < ApplicationRecord
  has_many :short_url_statistics

  before_create :generate_uniq_key

  def generate_uniq_key
    self.uniq_key = SecureRandom.hex(2)
  end
end
