# == Schema Information
#
# Table name: crowd_statuses
#
#  id         :bigint           not null, primary key
#  icon       :string(255)
#  name       :string(255)
#  que        :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class CrowdStatus < ApplicationRecord
  mount_uploader :icon, IconUploader
  validates :name, presence: true, uniqueness: true
  validates :icon, presence: true
  acts_as_list column: :que

  def self.except_current_list(name)
    list = CrowdStatus.order("que asc").pluck(:name)

    list.reject { |elem| elem == name }
  end
end
