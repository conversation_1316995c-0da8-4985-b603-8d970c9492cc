# == Schema Information
#
# Table name: after_sales
#
#  id               :bigint           not null, primary key
#  amout            :float(24)
#  asset_imgs_count :integer          default(0)
#  detail           :string(255)
#  not_at           :datetime
#  order_status     :integer
#  reason           :string(255)
#  return_at        :datetime
#  status           :integer
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  order_id         :integer
#
class AfterSale < ApplicationRecord
  default_scope { order(created_at: 'desc') }
  has_many :asset_imgs, as: :resoure #,:counter_cache => true

  belongs_to :order
  # belongs_to :user

  ReasonCode = { "1" => '仅退款', "11" => '未收到货', '111' => '发货时间太长', "112" => '不喜欢/不想要', "12" => '已收到货', "121" => '产品与描述不符', "122" => '产品质量问题', "123" => '产品有微小瑕疵', "124" => '少件/漏发', "125" => '包装/商品破损', "126" => '发错货', "2" => '退货退款', "21" => '无理由退款货', "22" => '产品与描述不符', "23" => '产品质量问题', "24" => '产品有微小瑕疵', "25" => '少件/漏发', "26" => '包装/商品破损', "27" => '发错货', "3" => '换货', "31" => '无理由退款货', "32" => '产品与描述不符', "33" => '产品质量问题', "34" => '产品有微小瑕疵', "35" => '少件/漏发', "36" => '包装/商品破损', "37" => '发错货' }

  # status = { "0" => "用户申请" }

  #,"11"=>'未收到货','111'=>'发货时间太长'，"112"=>'不喜欢/不想要'

  #,"12"=>'已收到货',"121"=>'产品与描述不符'
  #,"122"=>'产品质量问题',"123"=>'产品有微小瑕疵',"124"=>'少件/漏发',"125"=>'包装/商品破损'
  #,"126"=>'发错货',"2"=>'退货退款',"21"=>'无理由退款货',"22"=>'产品与描述不符'
  # "23"=>'产品质量问题',"24"=>'产品有微小瑕疵',"25"=>'少件/漏发',"26"=>'包装/商品破损',"27"=>'发错货',"3"=>'换货',"31"=>'无理由退款货',"32"=>'产品与描述不符',"33"=>'产品质量问题',"34"=>'产品有微小瑕疵',"35"=>'少件/漏发',"36"=>'包装/商品破损',"37"=>'发错货'

  # ReasonCode = { "1"=>'仅退款', "11"=>'未收到货 111发货时间太长', "112": '不喜欢/不想要', "12": '已收到货'，"121": '产品与描述不符', "122": '产品质量问题', "123": '产品有微小瑕疵', "124": '少件/漏发', "125": '包装/商品破损', "126": '发错货', "2": '退货退款', "21": '无理由退款货', "22": '产品与描述不符', "23": '产品质量问题', "24": '产品有微小瑕疵', "25": '少件/漏发', "26": '包装/商品破损', "27": '发错货', "3": '换货', "31": '无理由退款货', "32": '产品与描述不符', "33": '产品质量问题', "34": '产品有微小瑕疵', "35": '少件/漏发' ,"36": '包装/商品破损',"37": '发错货'}

end
