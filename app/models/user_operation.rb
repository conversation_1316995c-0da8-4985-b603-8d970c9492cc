# == Schema Information
#
# Table name: user_operations
#
#  id             :bigint           not null, primary key
#  created_on     :date
#  operation_type :integer
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  user_id        :integer
#
# Indexes
#
#  index_user_operations_on_user_id  (user_id)
#
class UserOperation < ApplicationRecord
  enum operation_type: { active: 1 }
end
