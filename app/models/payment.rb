# == Schema Information
#
# Table name: payments
#
#  id         :bigint           not null, primary key
#  amount     :float(24)
#  pay_at     :datetime
#  pay_method :integer
#  refund_at  :datetime
#  status     :integer
#  trade_no   :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  order_id   :integer
#
# Indexes
#
#  index_payments_on_order_id  (order_id)
#
class Payment < ApplicationRecord
  belongs_to :order
  enum pay_method: {alipay: 0, wxpay: 1}
  enum status: {unpaid: 0, paid: 1, refund: 2}

  def pay_charge(ip_)
    Pingpp::Charge.create(
      order_no: trade_no,
      app: {id: "app_v50SS0XTS0WDDGGG"},
      channel: Order::ChannelPAY[pay_type],
      amount: amount * 100,
      client_ip: ip_,
      currency: "cny",
      subject: "支付订单",
      body: "#{order.order_details.collect(&:id)}"
    )
  end

  # 微信小程序退款
  def refund_order
    current_url = 'https://api.mch.weixin.qq.com/secapi/pay/refund'

    c_total_fee = "#{(self.amount*100).to_i}"
    c_refund_fee = "#{(self.amount*100).to_i}"
    appid = Settings.mini_program.appid
    mch_id = Settings.mini_program.mch_id

    batch_no = self.order.payments.ids.index(self.id) + 2
    refund_hash = {
      appid: "#{appid}",
      mch_id: "#{mch_id}",
      nonce_str: SecureRandom.hex(16).to_s,
      transaction_id: "#{self.trade_no}",
      out_refund_no: "#{self.order.number}#{batch_no}",
      total_fee: c_total_fee,
      refund_fee: c_refund_fee,
      notify_url: "https://api.iqunix.com/orders/wechat_refund_notify",
      op_user_id: "1", #操作员id
    }

    result_hash = WechatCommon.computational_signature(refund_hash)

    WechatCommon.certificate_request_url(current_url, result_hash, 'refund_order')
  end

  def self.ransackable_attributes(auth_object = nil)
    ["amount", "created_at", "id", "order_id", "pay_at", "pay_method", "refund_at", "status", "trade_no", "updated_at"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["order"]
  end
end
