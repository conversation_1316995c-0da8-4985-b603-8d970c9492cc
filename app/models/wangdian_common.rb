class WangdianCommon
  #计算签名
  def self.computational_signature(current_hash={}, pager_params={})
    sid = Settings.wangdian.sid
    appkey = Settings.wangdian.appkey
    salt = Settings.wangdian.salt
    version = Settings.wangdian.version
    timestamp = Time.now.to_i - 1325347200
    current_hash[:sid] = sid
    current_hash[:key] = appkey
    current_hash[:salt] = salt
    current_hash[:timestamp] = timestamp
    current_hash[:v] = version
    current_hash = current_hash.merge(pager_params) if pager_params.present?
    stringA = current_hash.sort.map{|k,v| "#{k}#{v}" }.join("")
    stringSignTemp = "#{Settings.wangdian.appsecret}#{stringA}#{Settings.wangdian.appsecret}"
    p "--------拼接字符串结果-----------"
    p stringSignTemp
    sign = Digest::MD5.hexdigest(stringSignTemp)
    p "--------签名结果-----------"
    p sign
    current_hash.merge(sign: sign)
  end

  #请求url
  def self.request_url(current_url, current_body, flag)
    p "--------请求url-----------"
    p current_url
    p "--------请求body-----------"
    p current_body
    response = HTTParty.post(current_url, body: current_body, headers: {'Content-Type' => 'application/json'})
    request_result = JSON.parse(response.body)
    request_result
  end

  def self.status_map(order_status)
    # 10未确认 20待尾款 30待发货 40部分发货 50已发货 60已签收 70已完成 80已退款 90已关闭(付款前取消)
    # STATUS = {"0" => "待支付", "1" => "已支付待发货", "2" => "已发货", "3" => "已完成待评价", "4" => "已评价", "5" => "订单取消", "6" => "已退款", "11" => "发货前退款", "21" => "发货后退款", "200" => "交易完成", "2000" => "等待支付尾款"}
    {
      0 => 10,
      1 => 30,
      2 => 50,
      3 => 70,
      5 => 90,
      6 => 80,
      11 => 10,
      21 => 10,
      2000 => 20
    }[order_status]
  end

  # 旺店通同步订单
  def self.sync_order(order)
    base_url = Settings.wangdian.url
    refund_status = if order.status == 6
                    5 # 退款成功
                  elsif order.is_after_sale?
                    2 # 已申请退款
                  else
                    0 # 无退款
                  end
    order_info = {}
    order_info[:shop_no] = Settings.wangdian.shop_no
    order_info[:rawTradeList] = [
        {
          "tid" => order.number,
          "process_status" => 10, # 10: 待递交, 20: 已递交， 30: 部分发货， 40: 已发货， 60: 已完成， 70: 已取消
          "trade_status" => WangdianCommon.status_map(order.status), # 10未确认 20待尾款 30待发货 40部分发货 50已发货 60已签收 70已完成 80已退款 90已关闭(付款前取消)
          "refund_status" => refund_status,
          "pay_status" => order.payments.first.paid? ? 2 : 0,
          "order_count" => order.order_details.count,
          "goods_count" => order.order_details.sum(:sku_count),
          "pay_method" => order.pay_type == "wxpay" ? 8 : 7,
          "trade_time" => order.created_at.strftime("%Y-%m-%d %H:%M:%S"),
          "end_time" => "",
          "buyer_nick" => order.user.username,
          "buyer_message" => order.user_node,
          "receiver_name" => order.receive_name,
          "receiver_area" => order.get_area,
          "receiver_address" => order.receive.address.to_s,
          "receiver_mobile" => order.receive_phone,
          "post_amount" => 0,
          "discount" => 0,
          "receivable" => order.amount,
          "delivery_term" => 1,
          "is_auto_wms" => false,
          "warehouse_no" => Settings.wangdian.warehouse_no,
        }
      ]
    order_info[:rawTradeOrderList] = []
    order.order_details.each do |detail|
      order_info[:rawTradeOrderList] << {
        "tid" => order.number,
        "oid" => "IQUNIXAPP#{detail.id}",
        "status" => WangdianCommon.status_map(order.status), # 10未确认 20待尾款 30待发货 40部分发货 50已发货 60已签收 70已完成 80已退款 90已关闭
        "refund_status" => refund_status,
        "goods_id" => detail.sku_number,
        "spec_id" => detail.sku_number,
        "goods_no" => detail.sku_number,
        "spec_no" => detail.sku_number,
        "goods_name" => detail.product_name,
        "spec_name" => JSON.parse(detail.try(:property_name)).join("-"),
        "num" => detail.sku_count,
        "price" => detail.skuprice,
        "discount" => 0,
        "share_discount" => 0,
        "total_amount" => detail.sku_count * detail.skuprice,
        "adjust_amount" => 0,
        "refund_amount" => order.status == 6 ? detail.sku_count * detail.skuprice : 0,
        "remark" => order.user_node.to_s,
        "json" => ""
      }
    end
    tmp_order_info = order_info.dup
    final_hash = computational_signature({method: "sales.RawTrade.pushSelf2", body: order_info.values.to_json})
    extra_url = final_hash.except(:body).sort.map{|k,v| "#{k}=#{v}" }.join("&")
    WangdianCommon.request_url(base_url + "?#{extra_url}", tmp_order_info.values.to_json, "test")
  end

  # 旺店通同步物流
  def self.sync_logistics
    base_url = Settings.wangdian.url
    pager_params = {
      page_size: 100,
      page_no: 0,
      calc_total: 1
    }
    query_params = {
      "shop_no" => Settings.wangdian.shop_no,
      "is_own_platform" => true,
    }
    final_hash = computational_signature({method: "sales.LogisticsSync.getSyncListExt", body: [query_params].to_json}, pager_params)
    extra_url = final_hash.except(:body).merge(pager_params).sort.map{|k,v| "#{k}=#{v}" }.join("&")
    WangdianCommon.request_url(base_url + "?#{extra_url}", [query_params].to_json, "test")
  end

  # 物流同步状态回传
  def self.sync_logistics_status(success_sync_ids)
    base_url = Settings.wangdian.url
    sync_list = []
    success_sync_ids.each do |sync_id|
      sync_list << {
        "sync_id" => sync_id,
        "status" => 0,
      }
    end
    final_hash = computational_signature({method: "sales.LogisticsSync.update", body: [sync_list].to_json})
    extra_url = final_hash.except(:body).sort.map{|k,v| "#{k}=#{v}" }.join("&")
    WangdianCommon.request_url(base_url + "?#{extra_url}", [sync_list].to_json, "test")
  end
end
