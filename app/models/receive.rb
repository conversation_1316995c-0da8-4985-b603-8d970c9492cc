# == Schema Information
#
# Table name: receives
#
#  id         :bigint           not null, primary key
#  address    :string(255)
#  is_default :boolean          default(FALSE)
#  name       :string(255)
#  phone      :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  region_id  :integer
#  user_id    :integer
#
class Receive < ApplicationRecord
  default_scope { order(created_at: 'desc') }

  belongs_to :user
  belongs_to :region
  has_many :orders

  def self.ransackable_attributes(auth_object = nil)
    ["address", "created_at", "id", "is_default", "name", "phone", "region_id", "updated_at", "user_id"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["orders", "region", "user"]
  end

end
