# == Schema Information
#
# Table name: manage_controllers
#
#  id         :bigint           not null, primary key
#  name       :string(255)
#  que        :integer
#  word       :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class ManageController < ApplicationRecord
  has_many :manage_actions, dependent: :destroy

  validates :word, presence: true, uniqueness: true
end
