# == Schema Information
#
# Table name: manage_roles
#
#  id         :bigint           not null, primary key
#  name       :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class ManageRole < ApplicationRecord
  has_many :manage_role_users, dependent: :destroy
  has_many :admin_users, through: :manage_role_users

  has_many :manage_role_actions, dependent: :destroy
  has_many :manage_actions, through: :manage_role_actions
  validates :name, presence: true, uniqueness: true
end
