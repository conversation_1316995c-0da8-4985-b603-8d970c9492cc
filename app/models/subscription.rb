# == Schema Information
#
# Table name: subscriptions
#
#  id              :bigint           not null, primary key
#  is_subscription :boolean          default(TRUE)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  product_id      :integer
#  sku_id          :integer
#  user_id         :integer
#
class Subscription < ApplicationRecord
  belongs_to :user, :counter_cache => true
  belongs_to :product, :counter_cache => true

  scope :valid, -> { where(is_subscription: true) }
end
