# == Schema Information
#
# Table name: profiles
#
#  id         :bigint           not null, primary key
#  data       :json
#  share_code :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  device_id  :integer
#
# Indexes
#
#  index_profiles_on_device_id  (device_id)
#
class Profile < ApplicationRecord
  belongs_to :device, class_name: "<PERSON><PERSON>", foreign_key: :device_id
  validates :share_code, uniqueness: true, presence: true
end
