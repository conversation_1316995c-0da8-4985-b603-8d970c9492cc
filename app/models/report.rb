# == Schema Information
#
# Table name: reports
#
#  id           :bigint           not null, primary key
#  report_code  :integer
#  report_desc  :string(255)
#  resoure_type :string(255)
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  resoure_id   :integer
#  user_id      :integer
#
class Report < ApplicationRecord
  default_scope { order(created_at: 'desc') }

  belongs_to :user
  belongs_to :resoure, :polymorphic => true

  report_hash = {
    "1" => "垃圾营销",
    "2" => "涉黄信息",
    "3" => "不实信息",
    "4" => "人身攻击",
    "5" => "有害信息",
    "6" => "内容抄袭",
    "7" => "违法信息",
    "8" => "诈骗信息",
    "9" => "其他"
  }
end
