# == Schema Information
#
# Table name: banners
#
#  id          :bigint           not null, primary key
#  banner_type :integer
#  click_count :integer          default(0)
#  content     :text(65535)
#  image       :string(255)
#  is_closing  :boolean          default(FALSE)
#  picture_url :string(255)
#  que         :integer          default(0)
#  title       :string(255)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  crowd_id    :integer
#  product_id  :integer
#
class Banner < ApplicationRecord
  acts_as_list column: :que
  mount_uploader :image, BannerImageUploader
  belongs_to :product, optional: true
  belongs_to :crowd, optional: true

  enum banner_type: { is_crowd: 1, is_product: 2, is_url: 3, is_content: 4 }
  before_save :check_validates
  after_save :update_other_info, if: -> { saved_change_to_banner_type? }

  def self.ransackable_attributes(auth_object = nil)
    ["click_count", "created_at", "crowd_id", "id", "image", "is_closing", "picture_url", "product_id", "que", "title", "updated_at"]
  end

  private

  def update_other_info
    if self.is_crowd?
      self.update(product_id: nil, picture_url: nil, content: nil)
    elsif self.is_product?
      self.update(crowd_id: nil, picture_url: nil, content: nil)
    elsif self.is_url?
      self.update(crowd_id: nil, product_id: nil, content: nil)
    elsif self.is_content?
      self.update(crowd_id: nil, product_id: nil, picture_url: nil)
    end
  end

  def check_validates
    if self.is_crowd?
      if self.crowd_id.blank?
        errors.add(:crowd_id, "众筹不能为空！")
        raise ::ActiveRecord::RecordInvalid.new(self)
      end
    elsif self.is_product?
      if self.product_id.blank?
        errors.add(:product_id, "商品不能为空！")
        raise ::ActiveRecord::RecordInvalid.new(self)
      end
    elsif self.is_url?
      if self.picture_url.blank?
        errors.add(:picture_url, "跳转链接不能为空！")
        raise ::ActiveRecord::RecordInvalid.new(self)
      end
    elsif self.is_content?
      if self.content.blank?
        errors.add(:content, "图文内容不能为空！")
        raise ::ActiveRecord::RecordInvalid.new(self)
      end
    end
  end
end
