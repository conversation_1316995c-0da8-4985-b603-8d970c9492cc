# == Schema Information
#
# Table name: devices
#
#  id         :bigint           not null, primary key
#  dfu_pid    :string(255)
#  name       :string(255)
#  pid        :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class Device < ApplicationRecord
  validates :name, presence: true, uniqueness: true
  validates :pid, presence: true, uniqueness: true
  validates :dfu_pid, uniqueness: true, allow_blank: true
end
