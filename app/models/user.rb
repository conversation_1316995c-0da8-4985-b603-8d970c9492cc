# == Schema Information
#
# Table name: users
#
#  id                   :bigint           not null, primary key
#  amounts_count        :float(24)        default(0.0)
#  assembles_count      :integer          default(0)
#  authentication_token :string(255)
#  avatar               :string(255)
#  avatar_file          :string(255)
#  comments_count       :integer          default(0)
#  login_time           :datetime
#  mini_program_openid  :string(255)
#  newup_name           :string(255)
#  openid               :string(255)
#  option_count         :integer          default(0)
#  orders_count         :integer          default(0)
#  phone                :string(255)
#  source_from          :integer
#  status               :boolean          default(FALSE)
#  subscriptions_count  :integer          default(0)
#  unionid              :string(255)
#  user_login_time      :integer          default(0)
#  user_nikename        :string(255)
#  user_system          :string(255)      default(""), not null
#  user_version         :string(255)      default(""), not null
#  username             :string(255)
#  wx_name              :string(255)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  register_id          :string(255)
#
# Indexes
#
#  index_users_on_openid    (openid) UNIQUE
#  index_users_on_phone     (phone) UNIQUE
#  index_users_on_username  (username) UNIQUE
#
class User < ApplicationRecord
  has_many :receives, class_name: "Receive"
  has_many :orders
  has_many :subscriptions, :counter_cache => true
  has_many :userlikes, :counter_cache => true
  has_many :assembles
  has_many :notifications
  has_many :reports, as: :resoure, :counter_cache => true
  has_many :opinions

  before_save :ensure_authentication_token
  before_save :ensure_user_nikename
  before_save :ensure_username
  after_create :set_source_from

  mount_uploader :avatar_file, AvatarUploader

  enum source_from: { app: 1, mini_program: 2 }

  scope :user_status, -> { where(status: false) }
  scope :yesterday, -> { where("created_at >= ? AND created_at <= ?", Date.yesterday.beginning_of_day, Date.yesterday.end_of_day) }
  scope :today, -> { where(created_at: Time.current.all_day) }

  after_save :change_username_synchro_others

  validates :openid, :phone, uniqueness: true, allow_blank: true

  Error_hash = {
    "isp.RAM_PERMISSION_DENY" => "RAM权限DENY",
    "isv.OUT_OF_SERVICE" => "业务停机",
    "isv.PRODUCT_UN_SUBSCRIPT" => "未开通云通信产品的阿里云客户",
    "isv.PRODUCT_UNSUBSCRIBE" => "产品未开通",
    "isv.ACCOUNT_NOT_EXISTS" => "账户不存在",
    "isv.ACCOUNT_ABNORMAL" => "账户异常",
    "isv.SMS_TEMPLATE_ILLEGAL" => "短信模板不合法",
    "isv.SMS_SIGNATURE_ILLEGAL" => "短信签名不合法",
    "isv.INVALID_PARAMETERS" => "参数异常",
    "isp.SYSTEM_ERROR" => "系统错误",
    "isv.MOBILE_NUMBER_ILLEGAL" => "非法手机号",
    "isv.MOBILE_COUNT_OVER_LIMIT" => "手机号码数量超过限制",
    "isv.TEMPLATE_MISSING_PARAMETERS" => "模板缺少变量",
    "isv.BUSINESS_LIMIT_CONTROL" => "业务限流",
    "isv.INVALID_JSON_PARAM" => "JSON参数不合法，只接受字符串值",
    "isv.BLACK_KEY_CONTROL_LIMIT" => "黑名单管控",
    "isv.PARAM_LENGTH_LIMIT" => "参数超出长度限制",
    "isv.PARAM_NOT_SUPPORT_URL" => "不支持URL",
    "isv.AMOUNT_NOT_ENOUGH" => "账户余额不足"
  }

  def status_cn
    if !self.status
      "正常"
    else
      if self.phone.blank?
        "已注销"
      else
        "禁用"
      end
    end
  end

  def ensure_authentication_token
    if authentication_token.blank?
      self.authentication_token = generate_authentication_token
    end
  end

  def ensure_user_nikename
    # 要进行多个判断
    if self.new_record?
      self.user_nikename = user_uniq_key if self.user_nikename.blank?
      self.avatar = "https://iqunix.oss-cn-shenzhen.aliyuncs.com/uploads/user/default_user.jpg" if avatar.blank?
    end
  end

  def change_username_synchro_others
    if self.username_changed?
      (self.orders || []).each do |order|
        order.update_columns(user_nickname: self.username)
      end
    end
  end

  def user_name
    self.username
  end

  def user_uniq_key
    values = [*'0'..'9'].sample(4).join
    cvalues = [*'a'..'z',*'A'..'Z'].sample(4).join
    'IQ' + values + cvalues
  end

  def is_official_user?
    id == Settings.official_user_id
  end

  private

  def ensure_username
    if username_changed? && username.present? && User.find_by(username: username).present?
      self.username = username + SecureRandom.hex(4)
    elsif wx_name_changed? && wx_name.present?
      if User.find_by(username: wx_name).present?
        self.username = wx_name + SecureRandom.hex(4)
      else
        self.username = wx_name
      end
    elsif user_nikename_changed? && user_nikename.present?
      if User.find_by(username: user_nikename).present?
        self.username = user_nikename + SecureRandom.hex(4)
      else
        self.username = user_nikename
      end
    end
  end

  def generate_authentication_token
    loop do
      token = Devise.friendly_token
      break token unless User.where(authentication_token: token).first
    end
  end

  def set_source_from
    if self.source_from.blank?
      if self.mini_program_openid.present?
        self.update_columns(source_from: 'mini_program')
      else
        self.update_columns(source_from: 'app')
      end
    end
  end

  private

  def self.ransackable_attributes(auth_object = nil)
    ["amounts_count", "assembles_count", "source_from", "authentication_token", "avatar", "avatar_file", "comments_count", "created_at", "id", "login_time", "name", "username", "openid", "option_count", "orders_count", "phone", "register_id", "status", "subscriptions_count", "updated_at", "user_login_time", "user_nikename", "user_system", "user_version", "wx_name"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["assembles", "notifications", "opinions", "orders", "receives", "reports", "subscriptions", "userlikes"]
  end
end
