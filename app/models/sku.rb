# == Schema Information
#
# Table name: skus
#
#  id              :bigint           not null, primary key
#  properties_ids  :string(255)
#  que             :integer          default(0)
#  sku_markedprice :float(24)
#  sku_name        :string(255)
#  sku_number      :string(255)
#  sku_outerid     :string(255)
#  sku_pictureurl  :string(255)
#  sku_price       :float(24)
#  sku_property    :string(255)
#  sku_quantity    :integer          default(0)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  product_id      :integer
#
class Sku < ApplicationRecord
  attr_accessor :sku_properties_arrs

  mount_uploader :sku_pictureurl, SkuPictureurlUploader

  belongs_to :product
  has_many :order_details
  has_many :sku_properties, dependent: :destroy
  has_many :properties, :through => :sku_properties
  accepts_nested_attributes_for :sku_properties, allow_destroy: true

  before_save :save_sku_properties!
  skip_callback :commit, :after, :remove_sku_pictureurl!, if: :related_orders?

  validates :sku_quantity, presence: true
  validates :sku_price, :sku_markedprice, presence: true, if: -> { !product.is_pre_sale? || (product.is_pre_sale? && product.pay_final?) }

  def save_sku_properties!
    return unless sku_properties_arrs

    sku_properties_arrs.split(',').each do |sku_property_name|
      propertie_name = sku_property_name.split('_')
      first_property = Property.find_or_create_by!(name: propertie_name.first, level: 0, parent_id: nil)
      second_property = Property.find_or_create_by!(name: propertie_name.last, level: 1, parent_id: first_property.id)
      # 处理区分中英文符号的问题
      if second_property.name != propertie_name.last
        second_property.update!(name: propertie_name.last)
      end
      sku_properties.find_or_initialize_by(property_id: second_property.id)
    end
  end

  def full_property_name
    all_properties = properties.map { |property| "#{property.parent.name}_#{property.name}" }
    all_properties.join(',')
  end

  private

  def related_orders?
    order_details.exists?
  end

end
