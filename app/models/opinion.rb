# == Schema Information
#
# Table name: opinions
#
#  id               :bigint           not null, primary key
#  asset_imgs_count :integer          default(0)
#  code             :string(255)
#  detail           :text(65535)
#  is_mark          :boolean
#  mobile           :string(255)
#  option_type      :integer
#  status           :integer          default("pending")
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  parent_id        :integer
#  user_id          :integer
#
# Indexes
#
#  index_opinions_on_parent_id  (parent_id)
#
class Opinion < ApplicationRecord
  has_many :asset_imgs, as: :resoure
  has_many :child_opinions, class_name: "Opinion", foreign_key: "parent_id"

  belongs_to :user, optional: true
  belongs_to :parent, class_name: "Opinion", optional: true, foreign_key: "parent_id"

  validates :option_type, :detail, presence: true

  scope :only_parent, -> { where(parent_id: nil) }

  enum status: {pending: 0, processing: 1, completed: 2, canceled: 3}
  enum option_type: {software: 0, logistics: 1, product: 2, returns: 3, other: 4, instruction: 5, cancel_instruction: 6}

  after_create :set_code
  after_create :check_parent_status

  def self.ransackable_attributes(auth_object = nil)
    ["status", "is_mark"]
  end

  def self.ransackable_associations(auth_object = nil)
    []
  end

  private

  def set_code
    return if code.present?

    self.code = "IQ#{Time.now.to_i}#{rand(1000..9999)}"
    save
  end

  def check_parent_status
    return if option_type != "cancel_instruction"

    parent&.update(status: :canceled)
  end
end
