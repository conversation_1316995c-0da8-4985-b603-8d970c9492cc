# == Schema Information
#
# Table name: orders
#
#  id               :bigint           not null, primary key
#  admin_node       :string(255)
#  amount           :float(24)
#  charge_number    :string(255)
#  charge_time      :datetime
#  count            :integer
#  delivery_time    :datetime
#  logistics_com    :string(255)
#  logistics_desp   :string(255)
#  logistics_number :string(255)
#  number           :string(255)
#  order_type       :integer
#  pay_time         :datetime
#  pay_type         :string(255)
#  phone            :string(255)
#  receive_address  :string(255)
#  receive_name     :string(255)
#  receive_phone    :string(255)
#  refund_time      :datetime
#  send_time        :datetime
#  status           :integer
#  text_conment     :text(65535)
#  user_nickname    :string(255)
#  user_node        :string(255)
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  crowd_id         :integer
#  receive_id       :integer
#  refund_id        :string(255)
#  user_id          :integer
#
class Order < ApplicationRecord
  audited only: :status

  default_scope { order(created_at: "desc") }

  STATUS = {"0" => "待支付", "1" => "已支付待发货", "2" => "已发货", "3" => "已完成待评价", "4" => "已评价", "5" => "订单取消", "6" => "已退款", "11" => "发货前退款", "21" => "发货后退款", "200" => "交易完成", "2000" => "等待支付尾款"}
  OPERATION_STATUS = {"0" => "等待买家付款", "1" => "等待发货", "2" => "已发货", "11" => "发货前退款", "21" => "发货后退款", "6" => "已退款", "2000" => "已付定金"}
  PAYTYPE = {"alipay" => "支付宝", "wxpay" => "微信"}

  ChannelPAY = {"alipay" => "alipay", "wxpay" => "wx"}
  enum order_type: {pre_sale: 2}

  has_many :order_details
  has_many :payments

  has_one :after_sale

  before_save :synchro_others
  belongs_to :receive
  belongs_to :user

  before_save :save_amount
  before_save :save_number

  before_save :change_product_sale_count

  after_save :save_text_conment

  # 表示已经够买过的状态
  scope :order_status_pay, -> { where(status: [1, 2, 3, 4, 200]) }
  scope :paid, -> { where(status: [1, 2, 3, 4, 11, 21, 200, 2000]) }
  scope :refunding, -> { where(status: [11, 21]) }

  def pay!
    return if status != 0
    ActiveRecord::Base.transaction do
      self.pay_time = Time.zone.now
      payments.last.update!(pay_at: Time.zone.now, status: :paid)
      order_related_product = order_details.first.product
      if order_related_product.is_pre_sale? && order_related_product.pay_deposit?
        PresaleOrderNotificationJob.perform_async(id, 'paid_deposit')
        self.status = 2000
        save!
      elsif order_related_product.is_pre_sale? && order_related_product.pay_final?
        # 订单合并处理
        deposit_order = user.orders.joins(:order_details).where("order_details.product_id = #{order_related_product.id}").where(status: 2000).first
        if deposit_order.present?
          # 合并主订单
          deposit_order.status = 1
          deposit_order.amount += self.amount
          deposit_order.pay_time = Time.zone.now
          deposit_order.receive_id = self.receive_id
          deposit_order.receive_name = self.receive_name
          deposit_order.receive_phone = self.receive_phone
          deposit_order.receive_address = self.receive_address
          deposit_order.user_node = self.user_node
          # 合并订单详情
          current_order_detail = self.order_details.first
          deposit_order_detail = deposit_order.order_details.first
          deposit_order_detail.sku_count = current_order_detail.sku_count
          deposit_order_detail.sku_id = current_order_detail.sku_id
          deposit_order_detail.skuprice += current_order_detail.skuprice
          deposit_order_detail.sku_number = current_order_detail.sku_number
          deposit_order_detail.skupictureurl += current_order_detail.skupictureurl
          deposit_order_detail.product_name = current_order_detail.product_name
          deposit_order_detail.property_name = current_order_detail.property_name
          deposit_order_detail.sku_id = current_order_detail.sku_id
          deposit_order_detail.save!
          deposit_order.save!
          deposit_order.update_columns(created_at: Time.zone.now)
          self.payments.update!(order_id: deposit_order.id)
          self.destroy
        end
      else
        self.status = 1
        save!
      end
    end
  end

  # 发起退款请求
  def refund
    return { success: true } if Rails.env.development?

    if payments.paid.present?
      payments.paid.each do |payment|
        begin
          if payment.trade_no.size == 28
            # 微信小程序退款
            payment.refund_order
          else
            Pingpp::Charge.retrieve(payment.trade_no).refunds.create(description: "APP订单退款")
          end
        rescue => err
          Rails.logger.error "退款失败: #{err}"
          self.refund_id = err
          self.save
          return { success: false, reason: err }
        end
      end
    else
      begin
        Pingpp::Charge.retrieve(charge_number).refunds.create(description: "APP订单退款")
      rescue => err
        self.refund_id = err
        self.save
        return { success: false, reason: err }
      end
    end

    { success: true }
  end

  # 退款成功
  def self.refund_succeeded(charge_number)
    success = false
    ActiveRecord::Base.transaction do
      payment = Payment.find_by(trade_no: charge_number)
      if payment.present?
        payment.update!(refund_at: Time.current, status: :refund)
        order = payment.order
        begin
          PresaleOrderNotificationJob.perform_async(order.id, 'deposit_refunded') if order.audits.last.audited_changes["status"].first == 2000
        rescue => err
        end
        order.update!(refund_time: Time.current, status: 6) if order.payments.paid.blank?
        begin
          if order.reload.audits.last.audited_changes["status"] == [2000, 6]
            PresaleOrderNotificationJob.perform_async(order.id, 'initiative_deposit_refunded')
          end
        rescue => err
        end
        success = true
      elsif (order = Order.find_by(charge_number: charge_number))
        order.update!(refund_time: Time.current, status: 6)
        success = true
      end
    end
    success
  end

  # 退款失败
  def self.refund_failed(charge_number, failure_msg)
    payment = Payment.find_by(trade_no: charge_number)
    if payment.present?
      payment.order.update(refund_id: failure_msg)
    elsif (order = Order.find_by(charge_number: charge_number))
      order.update(refund_id: failure_msg)
    end
  end

  def status_cn
    Order::STATUS[try(:status).to_s]
  end

  def is_after_sale?
    status.in?([11, 21])
  end

  def order_status_rate
    if pre_sale?
      case status
      when 0
        0
      when 1
        1.5
      when 2
        2.25
      when 2000
        0.75
      when 6
        4
      when 11
        1.5
      when 21
        2.25
      when 200
        4
      else
        0
      end
    else
      case status
      when 0, 1, 2, 3, 4, 5
        status.to_i
      when 6
        3
      when 11
        1.5
      when 21
        1.5
      when 200
        4
      else
        0
      end
    end
  end

  def save_amount
    if new_record? && !order_details.blank?
      self.amount = 0
      order_details.each do |det|
        self.amount += det.skuprice.to_f * det.sku_count.to_i
      end
    end
  end

  def save_number
    if new_record? || number.blank?
      self.number = Time.now.to_i.to_s + rand(10000).to_s
    end
  end

  def pay_charge(ip_)
    Pingpp::Charge.create(
      order_no: number,
      app: {id: "app_v50SS0XTS0WDDGGG"},
      channel: Order::ChannelPAY[pay_type],
      amount: self.amount * 100,
      client_ip: ip_,
      currency: "cny",
      subject: "支付订单",
      body: "#{order_details.collect(&:id)}"
    )
  end

  # 生成记录之后,预支付交易标识 -订单
  def unified_order
    current_url = 'https://api.mch.weixin.qq.com/pay/unifiedorder'
    c_url = "https://api.iqunix.com/orders/wechat_notify"
    c_fee = "#{(self.amount*100).to_i}"
    appid = Settings.mini_program.appid
    mch_id = Settings.mini_program.mch_id

    unified_hash = {
      appid: "#{appid}",
      mch_id: "#{mch_id}",
      nonce_str: SecureRandom.hex(16).to_s,
      body: "小程序支付订单",
      out_trade_no: number,
      total_fee: c_fee,
      spbill_create_ip: '127.0.0.1',
      notify_url: c_url,
      trade_type: 'JSAPI',
      openid: user.mini_program_openid
    }

    result_hash = WechatCommon.computational_signature(unified_hash)
    request_result = WechatCommon.request_url(current_url, result_hash, 'unified_order')
    request_result["prepay_id"]
  end

  def synchro_others
    self.user_nickname = user.username
    self.phone = user.phone
    self.receive_name = receive.name
    self.receive_phone = receive.phone
    # 省市区地址
    pro = receive.region.try(:parent).try(:parent).try(:name)
    city = receive.region.try(:parent).try(:name)
    district = receive.region.try(:name)
    self.receive_address = pro.to_s + " " + city.to_s + " " + district.to_s + " " + receive.address.to_s
  end

  def get_area
    pro = receive.region.try(:parent).try(:parent).try(:name)
    city = receive.region.try(:parent).try(:name)
    district = receive.region.try(:name)
    pro.to_s + " " + city.to_s + " " + district.to_s
  end

  def save_text_conment
    text_conment = number.to_s + receive_name.to_s + receive_phone.to_s + user_nickname.to_s + phone
    update_columns(text_conment: text_conment)
  end

  def change_product_sale_count
    if status_changed? && status == 1
      order_details.each do |dd|
        product = dd.product
        count = product.sale_count.to_i + dd.sku_count.to_i
        product.update_columns(sale_count: count)
        sku = dd.sku
        sku.update(sku_quantity: sku.sku_quantity.to_i - dd.sku_count.to_i)
        if !crowd_id.blank? && crowd = Crowd.find(crowd_id)
          crowd.update_columns(sale_count: count)
        end
      end
    end

    if status_changed? && status == 6
      return if audits.last.audited_changes["status"].first == 2000

      order_details.each do |dd|
        product = dd.product
        count = product.sale_count.to_i - dd.sku_count.to_i
        product.update_columns(sale_count: count)
        sku = dd.sku
        next if sku&.sku_quantity.blank?

        sku.update(sku_quantity: sku.sku_quantity.to_i + dd.sku_count.to_i)
        if !crowd_id.blank? && crowd = Crowd.find(crowd_id)
          crowd.update_columns(sale_count: count)
        end
      end
    end

    # 旺店通同步订单
    if status_changed? && status.in?([1, 2, 3, 5, 6, 11, 21])
      sync_wangdian
    end
  end

  def sync_wangdian
    WangdianCommon.sync_order(self)
  end

  def self.ransackable_attributes(auth_object = nil)
    ["admin_node", "amount", "charge_number", "charge_time", "count", "created_at", "crowd_id", "delivery_time", "id", "logistics_com", "logistics_desp", "logistics_number", "number", "pay_time", "pay_type", "phone", "receive_address", "receive_id", "receive_name", "receive_phone", "refund_id", "refund_time", "send_time", "status", "text_conment", "updated_at", "user_id", "user_nickname", "user_node"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["after_sale", "order_details", "receive", "user", "payments"]
  end
end
