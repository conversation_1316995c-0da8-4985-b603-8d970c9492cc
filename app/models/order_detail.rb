# == Schema Information
#
# Table name: order_details
#
#  id            :bigint           not null, primary key
#  product_name  :string(255)
#  property_name :string(255)
#  sku_count     :integer
#  sku_number    :string(255)
#  skupictureurl :string(255)
#  skuprice      :float(24)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  order_id      :integer
#  product_id    :integer
#  sku_id        :integer
#
class OrderDetail < ApplicationRecord
  default_scope { order(created_at: "desc") }

  belongs_to :order

  belongs_to :product

  belongs_to :sku

  before_save :synchro_others

  def synchro_others
    self.product_name = product.name
    self.skupictureurl = sku.sku_pictureurl.present? ? sku.sku_pictureurl : product.list_img_url
    self.property_name = sku.properties.collect(&:name)
  end

  def property_name_format
    JSON.parse(self[:property_name]).join("-")
  end

  def self.ransackable_attributes(auth_object = nil)
    ["created_at", "id", "order_id", "product_id", "product_name", "property_name", "sku_count", "sku_id", "sku_number", "skupictureurl", "skuprice", "updated_at"]
  end
end
