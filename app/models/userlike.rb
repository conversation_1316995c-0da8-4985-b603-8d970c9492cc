# == Schema Information
#
# Table name: userlikes
#
#  id           :bigint           not null, primary key
#  resoure_type :string(255)
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  resoure_id   :integer
#  user_id      :integer
#
class Userlike < ApplicationRecord
  default_scope { order(created_at: 'desc') }

  belongs_to :user
  belongs_to :resoure, :polymorphic => true, :counter_cache => true
end
