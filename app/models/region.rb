# == Schema Information
#
# Table name: regions
#
#  id         :bigint           not null, primary key
#  level      :integer
#  name       :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  parent_id  :integer
#
class Region < ApplicationRecord
  default_scope { order(created_at: 'desc') }

  belongs_to :parent, class_name: "Region", optional: true, foreign_key: "parent_id"
end
