# == Schema Information
#
# Table name: crowds
#
#  id                  :bigint           not null, primary key
#  color_value         :string(255)      default("#fff")
#  count               :integer          default(0)
#  end_status          :integer          default(0)
#  end_time            :datetime
#  image               :string(255)
#  is_time_come        :boolean          default(FALSE)
#  name                :string(255)
#  que                 :integer          default(0)
#  sale_count          :integer          default(0)
#  start_time          :datetime
#  status              :integer          default(0)
#  subscriptions_count :integer          default(0)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  crowd_status_id     :integer
#  product_id          :integer
#  sku_id              :integer
#
# Indexes
#
#  index_crowds_on_crowd_status_id  (crowd_status_id)
#
class Crowd < ApplicationRecord
  default_scope { order(created_at: 'desc') }

  mount_uploader :image, ImageUploader
  belongs_to :product
  belongs_to :crowd_status
  has_many :subscriptions, counter_cache: true
  has_many :banners, dependent: :destroy

  def status_icon
    crowd_status&.icon_url || CrowdStatus.order(:que).first&.icon_url
  end

  def status_name
    crowd_status&.name || CrowdStatus.order(:que).first.name
  end
end
