# == Schema Information
#
# Table name: products
#
#  id                  :bigint           not null, primary key
#  aftermarket         :string(255)
#  assembles_count     :integer
#  before_image        :string(255)
#  button_txt          :string(255)
#  deposit             :float(24)
#  detail              :text(65535)
#  is_comment          :boolean          default(FALSE)
#  is_open_comment     :boolean          default(FALSE)
#  is_pre_sale         :boolean
#  is_sale             :boolean          default(FALSE)
#  is_sale_at          :datetime
#  is_sale_at_button   :boolean
#  list_img_url        :string(255)
#  name                :string(255)      default(""), not null
#  on_sale             :boolean          default(FALSE)
#  on_shelf            :boolean          default(FALSE)
#  pre_sale_stage      :integer
#  qrcode              :string(255)
#  que                 :integer          default(0)
#  sale_count          :integer          default(0)
#  share_img_url       :string(255)
#  subscriptions_count :integer          default(0)
#  title               :string(255)
#  total_inventory     :integer          default(0)
#  url_link            :string(255)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  tag_id              :integer
#
class Product < ApplicationRecord
  acts_as_list column: :que
  mount_uploader :before_image, BeforeImageUploader
  # mount_uploader :share_img_url, ShareImgUrlUploader
  mount_uploader :list_img_url, ListImgUrlUploader
  mount_uploader :aftermarket, AftermarketUploader

  has_many :comments, dependent: :destroy
  has_many :banners, dependent: :destroy
  has_many :crowds, dependent: :destroy
  has_many :assembles, counter_cache: :assembles_count, dependent: :destroy
  has_many :subscriptions, counter_cache: :subscriptions_count, dependent: :destroy

  has_many :skus, dependent: :destroy
  accepts_nested_attributes_for :skus, allow_destroy: true

  has_many :product_images, dependent: :destroy
  accepts_nested_attributes_for :product_images, allow_destroy: true

  has_many :order_details

  belongs_to :tag

  enum pre_sale_stage: { pay_deposit: 1, pay_final: 2 }

  scope :product_on_shelf, -> { where(on_shelf: true) }
  scope :product_is_sale, -> { where(is_sale: true) }

  before_save :save_total_inventory
  # 必填项及约束
  validates :name, :list_img_url, :before_image, :aftermarket, :detail, :que, presence: true
  validates :deposit, presence: true, if: -> { is_pre_sale? }
  validate :check_pay_final_sku_price, if: -> { is_pre_sale? && pay_final? }
  before_save :send_notification
  before_save :check_is_sale_at_change
  after_create :create_mini_program_info

  def parent_comments
    where(parent_id: nil)
  end

  def g_first_last
    skus = self.skus.order("sku_price desc")
    [skus.last.try(:sku_price), skus.first.try(:sku_price)]
  end

  def total_sku_quantity
    skus.sum(&:sku_quantity)
  end

  def aftermarket_image
    if aftermarket.blank?
      aftermarket_url
    else
      aftermarket
    end
  end

  def save_total_inventory
    if new_record? && !skus.blank?
      self.total_inventory = skus.sum(&:sku_quantity)
    end
  end

  def order_status_1_count
    OrderDetail.where(order_id: Order.where(status: 1).ids).where(product_id: id).sum(&:sku_count)
  end

  def real_time_sale_count
    OrderDetail.where(order_id: Order.paid.ids).where(product_id: id).sum(&:sku_count)
  end

  def skus_arr
    tmp_arr = []
    skus.each do |sku|
      tmp_hash = {}
      tmp_hash[:sku_id] = sku.id
      tmp_hash[:sku_name] = sku.full_property_name
      tmp_hash[:sku_price] = sku.sku_price
      tmp_hash[:sku_markedprice] = sku.sku_markedprice
      tmp_hash[:sku_quantity] = sku.sku_quantity
      tmp_hash[:sku_number] = sku.sku_number
      tmp_hash[:sku_pictureurl] = sku.sku_pictureurl_url
      tmp_arr << tmp_hash
    end
    tmp_arr
  end

  def share_id
    Base64.encode64("iqunix-#{id}").chomp!
  end

  def origin_sale_price(sku)
    if is_pre_sale?
      if pay_deposit?
        deposit
      else
        sku.sku_markedprice
      end
    else
      sku.sku_markedprice
    end
  end

  def sale_price(sku)
    if is_pre_sale?
      if pay_deposit?
        deposit
      else
        sku.sku_price - deposit
      end
    else
      sku.sku_price
    end
  end

  def outside_sale_price(sku)
    if is_pre_sale?
      if pay_deposit?
        deposit
      else
        sku.sku_price
      end
    else
      sku.sku_price
    end
  end

  def can_pay?(user)
    return false if is_sale.blank?

    return true unless is_pre_sale?

    if self.pay_final?
      user.orders.joins(:order_details).where("order_details.product_id = #{id}").where(status: 2000).first.present?
    else
      true
    end
  end

  def sale_quantity_arr(user)
    user.orders.joins(:order_details).where("order_details.product_id = #{id}").where(status: 2000).map(&:order_details).flatten.map(&:sku_id)
  end

  def send_notification
    if self.pre_sale_stage_changed? && pre_sale_stage == "pay_final"
      # 遍历所有预售订单
      orders = Order.joins(:order_details).where("order_details.product_id = #{id}").where(status: 2000)
      orders.each do |order|
        PresaleOrderNotificationJob.perform_async(order.id, "change_pay_all")
        PresaleOrderNotificationJob.perform_at(72.hours.from_now, order.id, "no_pay_all_over_72_hours")
      end
    end
  end

  def check_is_sale_at_change
    if is_sale_at_button_changed? && is_sale_at_button.blank?
      self.is_sale_at = nil
    else
      if is_sale_at_changed? && is_sale_at.present?
        if is_sale_at >= Time.now
          ProductIsSaleJob.perform_at(is_sale_at, id, is_sale_at.to_s)
        end
      end
    end
  end

  def create_mini_program_info
    CreateQrcodeJob.perform_async(id)
    CreateUrlLinkJob.perform_async(id)
  end

  # 搜索
  def self.ransackable_attributes(auth_object = nil)
    ["tag_id", "name", "on_shelf", "is_sale", "que", "id"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["tag"]
  end

  private

  def check_pay_final_sku_price
    skus.each do |sku|
      if sku.sku_price.blank? || sku.sku_markedprice.blank?
        errors.add(:base, '请填写 sku 的价格')
        break
      end
    end
  end

end
