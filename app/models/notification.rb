# == Schema Information
#
# Table name: notifications
#
#  id           :bigint           not null, primary key
#  content      :text(65535)
#  is_read      :boolean          default(FALSE)
#  notify_type  :integer
#  target_type  :string(255)
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  send_user_id :integer
#  target_id    :integer
#  user_id      :integer
#
# Indexes
#
#  index_notifications_on_send_user_id               (send_user_id)
#  index_notifications_on_target_id_and_target_type  (target_id,target_type)
#
class Notification < ApplicationRecord
  belongs_to :target, polymorphic: true
  belongs_to :send_user, class_name: "User", foreign_key: "send_user_id"
  belongs_to :user

  enum notify_type: {reply_comment: 1, comment_star: 2, reply_opinion: 3}
end
