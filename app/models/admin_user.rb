# == Schema Information
#
# Table name: admin_users
#
#  id                     :bigint           not null, primary key
#  account                :string(255)
#  email                  :string(255)
#  encrypted_password     :string(255)      default(""), not null
#  is_admin               :boolean
#  name                   :string(255)
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string(255)
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_admin_users_on_account               (account) UNIQUE
#  index_admin_users_on_email                 (email) UNIQUE
#  index_admin_users_on_reset_password_token  (reset_password_token) UNIQUE
#
class AdminUser < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable
  has_many :manage_role_users, dependent: :destroy
  has_many :manage_roles, through: :manage_role_users
  has_many :login_logs, dependent: :destroy
  # validates :account, presence: true, uniqueness: true

  def email_required?
    false
  end

  def self.calc_hello
    current_time = Time.current
    if current_time.hour < 9
      "早上好"
    elsif current_time.hour < 11
      "上午好"
    elsif current_time.hour < 13
      "中午好"
    elsif current_time.hour < 20
      "下午好"
    else
      "晚上好"
    end
  end
end
