# == Schema Information
#
# Table name: app_versions
#
#  id             :bigint           not null, primary key
#  download_count :integer
#  file_load      :string(255)
#  node           :string(255)
#  title          :string(255)
#  ver_type       :integer          default(0)
#  version        :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#
class AppVersion < ApplicationRecord
  default_scope { order(created_at: 'desc') }
  mount_uploader :file_load, VersionUploader
  VerType = { "0" => "暂无", "1" => "安卓", "2" => "IOS" }
end
