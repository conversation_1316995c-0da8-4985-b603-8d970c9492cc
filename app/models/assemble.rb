# == Schema Information
#
# Table name: assembles
#
#  id         :bigint           not null, primary key
#  is_like    :boolean          default(TRUE)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  product_id :integer
#  user_id    :integer
#
class Assemble < ApplicationRecord
  default_scope { order(created_at: 'desc') }

  belongs_to :user, :counter_cache => true
  belongs_to :product, :counter_cache => true
end
