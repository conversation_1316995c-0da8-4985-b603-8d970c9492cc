<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <title>运营管理平台</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;" name="viewport" />
    <meta content="Coderthemes" name="author"/>
    <link rel="shortcut icon" href="/assets/images/favicon.ico">
  </head>

  <body class="authentication-bg">

  <div class="account-pages">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-5">
          <div class="card">
            <!-- Logo -->
            <div class="card-header pt-3 pb-4 text-center bg-dark">
              <a href="index.html">
                <%= image_tag "logo.png", height: 32 %>
              </a>
            </div>

            <div class="card-body p-4">
              <div class="text-center w-75 m-auto">
                <h4 class="text-dark-50 text-center mt-0 font-weight-bold">登录</h4>
                <%# <p class="text-muted mb-4">输入邮箱和密码进行登录运营平台.</p> %>
              </div>

              <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>

                <div class="form-group mb-3">
                  <%= f.label :account, "用户名" %>
                  <%= f.text_field :account, class: "form-control" %>
                </div>

                <div class="form-group mb-3">
                  <label for="password">密码</label>
                  <%= f.password_field :password, autocomplete: "off", class: "form-control" %>
                </div>

                <div class="form-group mb-3">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="checkbox-signin" checked>
                    <label class="custom-control-label" for="checkbox-signin">记住我</label>
                  </div>
                </div>

                <div class="form-group mb-0 text-center d-grid">
                  <%= f.submit "登录", class: 'btn btn-xs btn-dark' %>
                </div>

              <% end %>
            </div> <!-- end card-body -->
          </div>
        </div> <!-- end col -->
      </div>
      <!-- end row -->
    </div>
    <!-- end container -->
  </div>
  <!-- end page -->

  <footer class="footer footer-alt">
    2023 © IQUNIX - iQunix.com
  </footer>
  </body>
</html>
