json.code 10000
json.message 'SUCCESS'
json.totalcount @result['totalcount']
json.refunds @result['refunds'] do |refund|
  json.refundno refund['id']
  json.platorderno refund['order_number']
  json.totalamount refund['totalamount']
  json.payamount refund['payamount']
  json.buyernick refund['buyernick']
  json.sellernick 'iQunix'
  json.createtime refund['created_at']
  json.updatetime refund['updated_at']
  json.orderstatus tradeStatus(refund['status'])
  json.refundstatus refundStatus(refund['status'], refund)
  json.goodsstatus goodsStatus(refund['cargostatus_code'])
  json.hasgoodsreturn refund['cargostatus_code'] == 1 ? false : true
  json.reason refund['reason']
  json.desc refund['detail']
  json.productnum 1
  json.logisticname '暂时没有'
  json.logisticno   '暂时没有'
  json.goodinfos refund['goodinfos'] do |good|
    json.ProductId       good['product_id']
    json.OuterID         good['code']
    json.Sku             good['Sku']
    json.ProductName     good['ProductName']
    json.RefundAmount    good['RefundAmount']
    json.Reason          refund['repair_desc']
    json.ProductNum      good['ProductNum']
  end
end