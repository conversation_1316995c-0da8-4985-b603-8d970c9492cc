json.code 10000
json.message 'SUCCESS'
json.numtotalorder @result['order_size']
json.orders @result['orders'] do |order|

  json.PlatOrderNo order['number']
  json.tradeStatus tradeStatus(order['status'])
  json.tradetime Time.parse(order['created_at']).strftime("%Y-%m-%d %H:%M:%S")
  json.country '中国'
  json.province order['receive_address'].split(" ")[0]
  json.city     order['receive_address'].split(" ")[1]
  json.area     order['receive_address'].split(" ")[2]
  json.address  order['receive_address'].split(" ")[3]
  json.phone    order['phone']
  json.mobile   order['phone']
  json.customerremark order['user_node']
  json.goodsfee         order['amount']
  json.totalmoney       order['amount']
  json.favourablemoney  0
  json.sendstyle        order['logistics_com']
  json.receivername     order['receive_name']
  json.nick             order['user_nickname']
  json.ShouldPayType    '担保交易'

  json.goodinfos order['goodinfos'] do |good|
      json.ProductId       good['product_id']
      json.suborderno      good['suborderno']
      json.tradegoodsno    good['tradegoodsno']
      json.tradegoodsname  good['tradegoodsname']
      json.tradegoodsspec  good['tradegoodsspec']
      json.goodscount      good['goodscount']
      json.price           good['price']
    end

end