<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">众筹管理</li>
          </ol>
        </div>
        <h4 class="page-title">众筹列表</h4>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <%= link_to "新增众筹", "#", class: "btn btn-primary mb-2", "data-bs-toggle": "modal", "data-bs-target": "#create-modal" %>
          <table class="table table-centered w-100 dt-responsive nowrap" id="products-datatable">
            <thead class="table-light">
              <tr>
                <th>ID</th>
                <th style="width: 400px;">商品信息</th>
                <th>销量/目标</th>
                <th style="width: 200px;">众筹状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <% @crowds.each_with_index do |crowd,index|%>
                <tr>
                  <td>
                    <%= crowd.id %>
                  </td>
                  <td>
                    <% g_ood=crowd.try(:product)%>
                      <img src="<%=g_ood.try(:list_img_url)%>" alt="contact-img" title="contact-img"
                        class="rounded me-1" height="48" />
                      <p class="m-0 d-inline-block align-middle font-12">
                        <a href="#" class="text-body">
                          <%= g_ood.name %>
                        </a>
                        <br />
                        <span class="text-warning">商品ID:<%= g_ood.id %></span>
                      </p>
                  </td>
                  <td class="text-center">
                    <div class="progress progress-sm">
                      <% if crowd.count > 0 %>
                        <div class="progress-bar" role="progressbar" style="width: <%= (crowd.sale_count / crowd.count.to_f).round(2) * 100 %>%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                      <% end %>
                    </div>
                    <%= crowd.sale_count %> / <%= crowd.count %>
                  </td>
                  <td>
                    <div id="crowd-edit-<%= crowd.id %>">
                      <div class="dropdown" crowd-id="<%= crowd.id %>">
                        <% if policy(crowd).update? %>
                          <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <%= crowd.status_name %>
                          </button>
                        <% else %>
                          <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" disabled>
                            <%= crowd.status_name %>
                          </button>
                        <% end %>
                        <div class="dropdown-menu dropdown-menu-animated">
                          <% CrowdStatus.except_current_list(crowd.status_name).each do |status_name| %>
                            <a class="dropdown-item" data-controller="crowd" data-action="click->crowd#update_status" status-id="<%= CrowdStatus.find_by_name(status_name)&.id %>"><%= status_name %></a>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <%= link_to "#", class: "action-icon", "data-bs-toggle": "modal", "data-bs-target": "#edit-modal-#{crowd.id}" do %>
                      <i class="mdi mdi-square-edit-outline" title="编辑"></i>
                    <% end %>
                    <%= link_to operation_crowd_path(crowd), method: :delete, data: { confirm: "确定删除吗？" }, class: "action-icon" do %>
                      <i class="uil-trash-alt" title="删除"></i>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @pagy.count <= 10 %>
                共 <b><%= @pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@pagy) %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%= render "form", crowd: Crowd.new, title: "新增众筹", id: "create-modal" %>

<% @crowds.each do |crowd| %>
  <div id="parent-modal-<%= crowd.id %>">
    <%= render "form", crowd: crowd, title: "编辑众筹", id: "edit-modal-#{crowd.id}" %>
  </div>
<% end %>