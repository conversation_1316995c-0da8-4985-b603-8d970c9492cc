<div id="<%= id %>" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="<%= id %>Label" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="<%= id %>Label"><%= title %></h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
      </div>
      <%= form_with model: [:operation, crowd], data: { turbo: false } do |form| %>
        <div class="modal-body">
          <% unless crowd.persisted? %>
            <div class="row">
              <label for="plat" class="col-3 col-form-label"><span class="validate-error">*</span>选择关联商品</label>
              <div class="col-9">
                <%= form.select :product_id, options_for_select(Product.all.collect{|o| [o.name,o.id]}, crowd.product_id), {}, required: true, class: "form-select" %>
              </div>
            </div>
          <% end %>

          <div class="row <%= crowd.persisted? ? '' : 'mt-2' %>">
            <label for="info" class="col-3 col-form-label">目标销量</label>
            <div class="col-9">
              <%= form.number_field :count, class: 'form-control', placeholder: '目标销量' %>
            </div>
          </div>
          <div class="row mt-2">
            <label for="crowd_is_time_come" class="col-3 col-form-label">启用进度条</label>
            <div class="col-9">
              <%= form.check_box(:is_time_come, { "data-switch": "success", id: "is-time-come#{form.object&.id}" }, 1, 0) %>
              <label for="is-time-come<%= form.object&.id %>" data-on-label="是" data-off-label="否" class="mb-0" style="position: absolute;margin-top: 8px;"></label>
            </div>
          </div>
          <div class="row mt-2">
            <label for="info" class="col-3 col-form-label">进度条颜色</label>
            <div class="col-9">
              <%= form.text_field :color_value, type: "color", class: 'form-control' %>
            </div>
          </div>
          <div class="row mt-2">
            <label for="crowd_status_id" class="col-3 col-form-label">众筹状态</label>
            <div class="col-9">
              <%= form.select :crowd_status_id, options_for_select(CrowdStatus.order(:que).pluck(:name, :id), crowd&.crowd_status_id), {}, class: "form-select" %>
            </div>
          </div>

        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">保存</button>
        </div>
      <% end %>
    </div>
  </div>
</div>
