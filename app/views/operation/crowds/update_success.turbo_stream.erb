<turbo-stream action="update" target="crowd-edit-<%= @crowd.id %>">
  <template>
    <div class="dropdown" crowd-id="<%= @crowd.id %>">
      <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <%= @crowd.status_name %>
      </button>
      <div class="dropdown-menu dropdown-menu-animated">
        <% CrowdStatus.except_current_list(@crowd.status_name).each do |status_name| %>
          <a class="dropdown-item" data-controller="crowd" data-action="click->crowd#update_status" status-id="<%= CrowdStatus.find_by_name(status_name)&.id %>"><%= status_name %></a>
        <% end %>
      </div>
    </div>
  </template>
</turbo-stream>

<turbo-stream action="update" target="parent-modal-<%= @crowd.id %>">
  <template>
    <%= render partial: 'form', formats: :html, locals: { crowd: @crowd, title: "编辑众筹", id: "edit-modal-#{@crowd.id}" } %>
  </template>
</turbo-stream>