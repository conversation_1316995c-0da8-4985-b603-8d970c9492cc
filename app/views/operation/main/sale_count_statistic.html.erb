<turbo-frame id="sale_count_chart_frame">
  <div id="sale_count_chart" style="width: 100%;height:300px;"></div>
  <script>
    // 销售额
    var myChart = echarts.init(document.getElementById('sale_count_chart'), 'macarons');
    option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      toolbox: {
        // feature: {
        //   dataView: { show: true, readOnly: true, title: '数据视图' }
        // }
      },
      legend: {
        data: ['新增用户', '销售额', '平均用户价值']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: <%= raw @date_times %>
      },
      yAxis: [
        {
          type: 'value',
          name: '人数',
          position: 'left',
          // offset: 30,
          alignTicks: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[0]
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '销售额',
          position: 'right',
          // offset: 30,
          alignTicks: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[1]
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '金额',
          position: 'right',
          alignTicks: true,
          offset: 50,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[2]
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
      ],
      series: [
        {
          name: '新增用户',
          type: 'line',
          data: <%= raw @users_count %>,
          lineStyle: {
            color: colors[0]
          }
        },
        {
          name: '销售额',
          type: 'bar',
          yAxisIndex: 1,
          data: <%= raw @users_sale_amount %>,
          lineStyle: {
            color: colors[1]
          }
        },
        {
          name: '平均用户价值',
          type: 'bar',
          yAxisIndex: 2,
          data: <%= raw @average_user_sale %>,
          lineStyle: {
            color: colors[1]
          }
        }
      ]
    };
    myChart.setOption(option);
    window.addEventListener('resize', function() {
      myChart.resize();
    });
  </script>
</turbo-frame>