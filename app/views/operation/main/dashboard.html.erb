<style>
  .header-title {
      font-size: 16px !important;
      font-weight: 600;
  }

  .list-item {
      color: #000000;
      padding: 6px 0 6px 15px;
      -webkit-transition: all .5s;
      -moz-transition: all .5s;
      -ms-transition: all .5s;
      -o-transition: all .5s;
      transition: all .5s;
  }

  .list-item:hover {
      background-color: #f5f5f5;
      -webkit-transition: all .5s;
      -moz-transition: all .5s;
      -ms-transition: all .5s;
      -o-transition: all .5s;
      transition: all .5s;
  }

  .list-item:hover .list-item-a {
      color: #155bd4;
      -webkit-transition: all .5s;
      -moz-transition: all .5s;
      -ms-transition: all .5s;
      -o-transition: all .5s;
      transition: all .5s;
  }

  .statistic-text {
      font-size: 28px;
      font-weight: 400;
      color: black;
  }

  .statistic-hint {
      color: #8d8d8d;
  }

  .ri-arrow-up-fill{
      font-size: 12px;
      color: green;
  }

  .ri-arrow-down-fill {
      font-size: 12px;
      color: red;
  }

  .ri-subtract-fill {
      font-size: 12px;
      color: red;
  }

  .right-icon {
      float: right;
      top: -69px;
      right: 20px;
      position: relative;
      color: #b7b7b7;
      font-size: 40px;
      -webkit-transition: all .5s;
      -moz-transition: all .5s;
      -ms-transition: all .5s;
      -o-transition: all .5s;
      transition: all .5s;
  }

  .table > :not(caption) > * > * {
    padding: 0.65rem 0.95rem;
  }
</style>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">数据面板</h4>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-xl-4" style="padding-left: 20px">
      <div class="card" style="height: 206px;margin-left: -7px">
        <div class="card-body">
          <%= link_to operation_users_path(q: {created_at_gteq: Time.current.at_beginning_of_day.strftime("%F %T"), created_at_lteq: Time.current.at_end_of_day.strftime("%F %T")}) do %>
            <div class="list-item" style="padding: 15px 0 5px 15px;font-weight: 700;">
              <div class="font-16">今日新增用户数</div>
              <div class="statistic-text list-item-a">
                <%= @today_users_count %>
                <i class="<%= @users_count_status %>"></i>
              </div>
              <div class="right-icon"><i class="uil-users-alt"></i></div>
            </div>
          <% end %>
          <%= link_to operation_users_path(q: {created_at_gteq: (Time.current - 1.day).at_beginning_of_day.strftime("%F %T"), created_at_lteq: (Time.current - 1.day).at_end_of_day.strftime("%F %T")}) do %>
            <div class="list-item font-16">昨日：<span class="list-item-a"><%= @yesterday_users_count %></span></div>
          <% end %>
        </div>
      </div>
    </div>
    <div class="col-xl-4" style="padding-left: 20px">
      <div class="card" style="height: 206px;margin-left: -7px">
        <div class="card-body">
          <div class="list-item" style="padding: 15px 0 5px 15px;font-weight: 700;">
            <div class="font-16">今日总销售额</div>
            <div class="statistic-text">
              ¥ <%= @today_sale_amount %>
              <i class="<%= @sale_amount_status %>"></i>
            </div>
            <div class="right-icon"><i class="uil-yen-circle"></i></div>
          </div>
          <div class="list-item statistic-hint font-16">
            昨日总销售额：¥ <%= @yesterday_sale_amount %>
          </div>
          <div class="list-item statistic-hint font-16">
            今日订单退款中：¥ <%= @today_sale_refunding_amount %>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4" style="padding-left: 12px">
      <div class="card">
        <div class="card-body">
          <%= link_to operation_orders_path(q: {status_in: [11,21]}) do %>
            <div class="list-item" style="padding: 15px 0 5px 15px;font-weight: 700;">
              <div class="font-16 statistic-title">待处理退款订单</div>
              <div class="statistic-text list-item-a"><%= Order.refunding.count %></div>
              <div class="right-icon"><i class="mdi mdi-clipboard-text-outline"></i></div>
            </div>
          <% end %>
          <%= link_to operation_orders_path(q: {status_eq: 11}) do %>
            <div class="list-item font-16">
              发货前退款：
              <span class="list-item-a">
                <%= Order.where(status: 11).count %>
              </span>
            </div>
          <% end %>
          <%= link_to operation_orders_path(q: {status_eq: 21}) do %>
            <div class="list-item font-16">发货后退款：
              <span class="list-item-a">
                <%= Order.where(status: 21).count %>
              </span>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <div class="col-xl-8">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title">用户总数(<%= User.where.not(phone: nil).count %>) | 订阅总数(<%= Subscription.valid.count %>)</h4>
          <h4 class="header-title"></h4>
          <div dir="ltr">
            <div id="user_statistic" style="width: 100%;height:300px;"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4">
      <div class="card" style="height: 374px;">
        <div class="card-body">
          <div class="row">
            <div class="col-md-12" data-controller="dashboard">
              <span class="header-title float-start">订阅数</span>
              <div id="app" v-cloak>
                <n-config-provider :theme-overrides="themeOverrides">
                  <n-popselect v-model:value="value" :options="options" @update:value="handleSelect" :show-checkmark="false">
                    <n-button
                      text
                      type="primary"
                      style="margin-bottom: 20px;margin-top:4px;font-weight:bold;float:right;"
                    >
                      {{ value }}
                      <i class="uil-angle-down" style="font-size: 20px"></i>
                    </n-button>
                  </n-popselect>
                </n-config-provider>
                </div>
              </div>
              <div style="height: 284px;overflow: scroll;width: 100%;" id="subscription_content">
                <turbo-frame id="subscription_statistic" src="/operation/main/subscription_statistic">
                  <div class="d-flex justify-content-center">
                    <div class="spinner-border" role="status"></div>
                  </div>
                </turbo-frame>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-12">
      <div class="card">
        <div class="card-body">
          <span class="header-title float-start">交易转化概况 <i class="mdi mdi-help-circle-outline" data-bs-toggle="tooltip" data-bs-html="true" data-bs-title="<p>销售额：当日注册的用户在平台的总交易额</p><p>平均用户价值：当日注册用户的成交总额 / 当日用户注册数</p>"></i></span>
          <div id="sale_count_statistic" v-cloak>
            <n-config-provider :theme-overrides="themeOverrides">
              <n-popselect v-model:value="value" :options="options" @update:value="handleSelect" :show-checkmark="false">
                <n-button
                  text
                  type="primary"
                  style="margin-bottom: 20px;margin-top:4px;font-weight:bold;float:right;"
                >
                  {{ value }}
                  <i class="uil-angle-down" style="font-size: 20px"></i>
                </n-button>
              </n-popselect>
            </n-config-provider>
          </div>
          <div style="height: 320px;overflow: scroll;width: 100%;" id="sale_count_statistic_content">
            <turbo-frame id="sale_count_chart_frame" src="/operation/main/sale_count_statistic" loading="lazy">
              <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status"></div>
              </div>
            </turbo-frame>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
    // 用户分析
    var myChart = echarts.init(document.getElementById('user_statistic'), 'macarons');
    const colors = ["#5ab1ef", "#2ec7c9", "#b6a2de", "#ffb980"]
    option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增用户', '活跃用户', '新增订阅', '新增预定']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: <%= raw @date_times %>
      },
      yAxis: [
        {
          type: 'value',
          name: '人数',
          position: 'left',
          alignTicks: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[0]
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '订阅数',
          position: 'right',
          alignTicks: true,
          offset: 0,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[2]
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
      ],
      series: [
        {
          name: '新增用户',
          type: 'line',
          data: <%= raw @users_count %>,
          lineStyle: {
            color: colors[0]
          }
        },
        {
          name: '活跃用户',
          type: 'line',
          data: <%= raw @active_users_count %>,
          lineStyle: {
            color: colors[1]
          }
        },
        {
          name: '新增订阅',
          type: 'line',
          yAxisIndex: 1,
          data: <%= raw @subscriptions_count %>,
          lineStyle: {
            color: colors[2]
          }
        },
        {
          name: '新增预定',
          type: 'line',
          yAxisIndex: 1,
          data: <%= raw @pre_sales_count %>,
          lineStyle: {
            color: colors[3]
          }
        }
      ]
    };
    myChart.setOption(option);

    window.addEventListener('resize', function() {
      myChart.resize();
    });
</script>
