<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item active">职业预设管理</li>
          </ol>
        </div>
        <h4 class="page-title">职业预设管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-2">
              <%= link_to "新增职业预设", new_operation_career_preset_path, class: "btn btn-primary" %>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table table-centered table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>背景图</th>
                  <th>标题</th>
                  <th>描述</th>
                  <th>发布环境</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @career_presets.each do |career_preset| %>
                  <tr>
                    <td><%= image_tag career_preset.background.url, style: "width: 90px; height: 50px;" %></td>
                    <td><%= career_preset.title_zh %></td>
                    <td><%= career_preset.description_zh %></td>
                    <td><%= career_preset.deploy_env_i18n %></td>
                    <td><%#= link_to 'Show', [:operation, career_preset] %> <%= link_to '编辑', edit_operation_career_preset_path(career_preset) %> <%= link_to '删除', [:operation, career_preset], method: :delete, data: { confirm: '确定删除吗？' } %></td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
