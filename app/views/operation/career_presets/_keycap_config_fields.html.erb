<div class="nested-fields">
  <table class="table" style="margin-bottom: 0;">
    <tbody>
      <tr>
        <td style="width: 150px;" class="text-center"><%= f.select :keycap_id, Keycap.all.map { |keycap| [keycap.name, keycap.id] }, { include_blank: "请选择按键" }, class: "form-select" %></td>
        <td style="width: 100px;padding-top: 23px;" class="text-center"><%= f.check_box :enable_rt_mode, class: "form-check-input rt-mode-toggle" %></td>
        <td style="width: 150px;" class="text-center"><%= f.number_field :trigger_point, class: "form-control", in: 0.01..4.00, step: 0.01 %></td>
        <td style="width: 150px;" class="text-center"><%= f.number_field :press_trigger_point, class: "form-control rt-mode-field", in: 0.01..4.00, step: 0.01, disabled: !f.object.enable_rt_mode, required: f.object.enable_rt_mode %></td>
        <td style="width: 150px;" class="text-center"><%= f.number_field :release_trigger_point, class: "form-control rt-mode-field", in: 0.01..4.00, step: 0.01, disabled: !f.object.enable_rt_mode, required: f.object.enable_rt_mode %></td>
        <td style="width: 100px;" ><%= link_to_remove_association "删除", f, class: "btn btn-danger btn-sm" %></td>
      </tr>
    </tbody>
  </table>
</div>

<script>
$(document).ready(function() {
  $('.rt-mode-toggle').on('change', function() {
    var enabled = $(this).is(':checked');
    var rtModeFields = $(this).closest('tr').find('.rt-mode-field');
    rtModeFields.prop('disabled', !enabled);
    rtModeFields.prop('required', enabled);
  });
});
</script>
