<%= form_for([:operation, @career_preset]) do |f| %>
<div class="row">
  <div class="col-md-6">
    <div class="field">
      <%= f.label :background, "背景图（1080 × 624像素）" %><br>
      <%= f.file_field :background %>
    </div>
    <div class="field">
      <%= f.label :title_zh, "标题(中文)" %><br>
      <%= f.text_field :title_zh, class: 'form-control', required: true %>
    </div>
    <div class="field">
      <%= f.label :description_zh, "描述(中文)" %><br>
      <%= f.text_area :description_zh, class: 'form-control', required: true %>
    </div>
    <div class="field">
      <%= f.label :title_tw, "标题(繁体)" %><br>
      <%= f.text_field :title_tw, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_tw, "描述(繁体)" %><br>
      <%= f.text_area :description_tw, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :title_en, "标题(英文)" %><br>
      <%= f.text_field :title_en, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_en, "描述(英文)" %><br>
      <%= f.text_area :description_en, class: 'form-control' %>
    </div>

    <div class="field">
      <%= f.label :title_ja, "标题(日文)" %><br>
      <%= f.text_field :title_ja, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_ja, "描述(日文)" %><br>
      <%= f.text_area :description_ja, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :title_ko, "标题(韩文)" %><br>
      <%= f.text_field :title_ko, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_ko, "描述(韩文)" %><br>
      <%= f.text_area :description_ko, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :deploy_env, "发布环境" %><br>
      <%= f.select :deploy_env, CareerPreset.deploy_envs.keys.map { |key| [key.humanize, key] }, {}, class: 'form-control' %>
    </div>
    <div class="actions mt-2">
      <%= f.submit "保存", class: "btn btn-primary", style: "margin-right: 6px;" %>
      <%= link_to '取消', operation_career_presets_path, class: "btn btn-secondary" %>
    </div>
  </div>
  <div class="col-md-6">
    <h3>按键配置</h3>
    <div class="alert alert-warning" role="alert">
      注：Others 为其他按键，必须添加。
    </div>
    <table class="table" style="margin-bottom: 0;">
      <thead>
        <tr>
          <th style="width: 150px;"><span style="color:red">*</span> 按键ID</th>
          <th style="width: 100px;">快速触发模式</th>
          <th style="width: 150px;">触发点(mm)</th>
          <th style="width: 150px;">按下（触发）行程(mm)</th>
          <th style="width: 150px;">松开（重置）行程(mm)</th>
          <th style="width: 100px">操作</th>
        </tr>
      </thead>
    </table>
    <%= f.fields_for :keycap_configs do |keycap_config| %>
      <%= render 'keycap_config_fields', f: keycap_config %>
    <% end %>
    <div class="links">
      <%= link_to_add_association '添加按键配置', f, :keycap_configs, class: "btn btn-primary btn-sm", style: "margin-top: 10px;" %>
    </div>
  </div>
</div>

<% end %>
