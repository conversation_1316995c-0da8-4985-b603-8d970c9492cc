<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item active">固件管理</li>
          </ol>
        </div>
        <h4 class="page-title">固件管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-2">
              <%= link_to "新增固件", new_operation_firmware_path, class: "btn btn-primary" %>
            </div>
          </div>
          <% Device.all.pluck(:name, :pid).each do |name, pid| %>
            <% firmwares = @firmwares.where(device_id: pid).order(id: :desc).limit(5) %>
            <% if firmwares.present? %>
              <h4 class="mt-4 mb-3"><%= name.upcase %></h4>
              <div class="table-responsive">
                <table class="table table-centered table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>版本</th>
                      <th>发布环境</th>
                      <th>强制更新</th>
                      <th>固件下载</th>
                      <th style="width: 800px;">描述</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% firmwares.each do |firmware| %>
                      <tr>
                        <td><%= firmware.version %></td>
                        <td><%= firmware.deploy_env.reject!(&:empty?).map { |key| key.humanize if key.present? }.join('、') rescue '未设置' %></td>
                        <td><%= firmware.force_update? ? '是' : '否' %></td>
                        <td><%= link_to '下载', firmware.file_url, target: '_blank' rescue '未上传固件' %></td>
                        <td><%= firmware.description_zh %></td>
                        <td><%= link_to '编辑', edit_operation_firmware_path(firmware) %> <%= link_to '删除', [:operation, firmware], method: :delete, data: { confirm: '确定删除吗？' } %></td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
