<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">用户管理</li>
          </ol>
        </div>
        <h4 class="page-title">用户列表</h4>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-lg-8">
              <%= search_form_for [:operation, @q], html: {class: "row gx-2 align-items-center"} do |f| %>
                <div class="col-auto mb-2">
                  <div class="input-group flex-nowrap">
                    <input type="search" name="seach_name" class="form-control" id="inputPassword2" value="<%= params[:seach_name] %>" placeholder="全文搜索...">
                  </div>
                </div>

                <div class="col-auto mb-2">
                  <div class="input-group flex-nowrap">
                    <span class="input-group-text">注册时间</span>
                    <%= f.text_field :created_at_gteq, class: "form-control datetime", type: 'search', placeholder: "起始时间", autocomplete: 'off', value: params[:q] ? params[:q][:created_at_gteq].to_s : nil %>
                    <span style="margin: 8px 5px 0px 5px;">-</span>
                    <%= f.text_field :created_at_lteq, class: "form-control datetime", type: 'search', placeholder: "结束时间", autocomplete: 'off', value: params[:q] ? params[:q][:created_at_lteq].to_s : nil %>
                  </div>
                </div>
                <div class="col-auto mb-2">
                  <div class="input-group flex-nowrap">
                    <span class="input-group-text">来源</span>
                    <%= f.select :source_from_eq, options_for_select([['全部', nil], ['APP', 1], ['小程序', 2]], params[:q]&.dig(:source_from_eq)), {},
                        type: "search", class: "form-select" %>
                  </div>
                </div>
                <div class="col-auto mb-2">
                  <div class="me-sm-2">
                    <button type="submit" class="btn btn-primary">搜索</button>
                  </div>
                </div>
              <% end %>
            </div>
            <!-- <div class="col-sm-4">
                <a href="/operation/goods/new" class="btn btn-danger mb-2"><i class="mdi mdi-plus-circle me-2"></i> 添加商品</a>
            </div> -->
            <div class="col-sm-8">
              <!-- <div class="text-sm-end">
                  <button type="button" class="btn btn-success mb-2 me-1"><i class="mdi mdi-cog-outline"></i></button>
                  <button type="button" class="btn btn-light mb-2 me-1">Import</button>
                  <button type="button" class="btn btn-light mb-2">Export</button>
              </div> -->
            </div><!-- end col-->
          </div>

          <div class="table-responsive">
            <table class="table table-centered w-100 dt-responsive nowrap" id="products-datatable">
              <thead class="table-light">
              <tr>
                <th class="all" style="width: 10px;">
                  <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="customCheck1">
                    <label class="form-check-label" for="customCheck1">&nbsp;</label>
                  </div>
                </th>
                <!-- <th class="all">用户ID</th>
                <th>用户头像</th> -->
                <th>昵称</th>
                <!-- <th>微信昵称</th> -->
                <th>注册号码</th>
                <th>绑定平台</th>
                <th>状态</th>
                <!-- <th>发布</th> -->
                <th>注册时间</th>
                <th>已购订单</th>
                <th>最后上线时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
              </thead>
              <tbody>
              <% @users.each do |user| %>
                <tr>
                  <td>
                    <div class="form-check">
                      <input type="checkbox" class="form-check-input" id="customCheck2">
                      <label class="form-check-label" for="customCheck2">&nbsp;</label>
                    </div>
                  </td>
                  <td>
                    <img src="<%= user.avatar %>" alt="contact-img" title="contact-img" class="rounded me-1" height="48"/>
                    <p class="m-0 d-inline-block align-middle font-12">
                      <a href="#" class="text-body"><%= user.user_name %></a>
                      <br/>
                      <span class="text-warning">用户ID:<%= user.id %></span>
                    </p>
                  </td>
                  <td>
                    <%= user.phone %>
                  </td>
                  <td>
                    <% if user.openid.present? %>
                      <i class="ri-wechat-fill" style="font-size: 18px;color: #2ac52a"></i>
                    <% else %>
                      无
                    <% end %>
                  </td>
                  <td>
                    <%= user.try(:status_cn) %>
                  </td>
                  <td>
                    <%= user.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
                  </td>
                  <td>
                    <% if user.orders.where.not(status: [0,6]).present? %>
                      <% user.orders.where.not(status: [0,6]).each do |order| %>
                        <%= link_to order.number, operation_order_path(order), target: '_blank' %><br>
                      <% end %>
                    <% else %>
                      无
                    <% end %>
                  </td>
                  <td>
                    <%= user.try(:login_time).strftime("%Y-%m-%d %H:%M:%S") if user.try(:login_time) %>
                  </td>
                  <td>
                    <div style="margin-top: 12px;">
                      <% if policy(user).users_status? %>
                        <input type="checkbox" class="users_status"  <%= !user.try(:status) ? "checked=''" : "" %>  id="<%= 'users_status' + user.id.to_s %>" data-switch="success">
                        <label for="<%= 'users_status' + user.id.to_s %>" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;"></label>
                      <% else %>
                        <input type="checkbox" class="users_status"  <%= !user.try(:status) ? "checked=''" : "" %>  id="<%= 'users_status' + user.id.to_s %>" data-switch="success" disabled>
                        <label for="<%= 'users_status' + user.id.to_s %>" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;" title="无权限"></label>
                      <% end %>
                    </div>
                  </td>

                  <td class="table-action">
                    <a href="<%#=operation_good_path(g_ood.goods_id)%>" class="action-icon">
                      <!-- <i class="mdi mdi-eye"></i> -->
                      <p class="m-0 d-inline-block align-middle font-12">查看</p>
                    </a>
                  </td>
                </tr>
              <% end %>
              </tbody>
            </table>
          </div>
          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @pagy.count <= 10 %>
                共 <b><%= @pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@pagy) %>
              <% end %>
            </div>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>

</div>


<script type="text/javascript">

    lay('.datetime').each(function(){
      laydate.render({
        elem: this,
        type: 'datetime',
        trigger: 'click',
        theme: '#1677ff'
      });
    });

    $(".users_status").click(function () {
        var id = $(this).attr("id");
        var id_number = id.split("status")
        change_user_status(id_number[1]);
    });

    function change_user_status(id) {
        $.ajax({
            url: "/operation/users/" + id + "/users_status",
            method: 'post',
            success: function (msg) {
                if (msg["status"] == 500) {
                    alert("操作不成功");
                }
                if (msg["status"] == 200) {
                    alert("操作成功");
                }

            }
        })
    };

</script>
