
        <style>
            .pagination li{
                    display: inline-block;
                    margin: 0;
                    padding: 0 4px;
                    border: 1px solid #d1dbe5;
                    border-right: 0;
                    background: #fff;
                    font-size: 13px;
                    min-width: 28px;
                    height: 28px;
                    line-height: 28px;
                    cursor: pointer;
                    box-sizing: border-box;
                    text-align: center;
            }
            .special_que{
                width: 100%;
            }
        </style>
                   <div class="container-fluid">

                        <!-- start page title -->
                        <div class="row">
                            <div class="col-12">
                                <div class="page-title-box">
                                    <div class="page-title-right">
                                        <ol class="breadcrumb m-0">
                                            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
                                            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
                                            <li class="breadcrumb-item active">分类管理</li>
                                        </ol>
                                    </div>
                                    <h4 class="page-title">分类管理</h4>
                                </div>
                            </div>
                        </div>
                        <!-- end page title -->

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row mb-2">
                                            <div class="col-sm-4">
                                                <a href="/operation/tags/new" class="btn btn-danger mb-2"><i class="mdi mdi-plus-circle me-2"></i> 添加分类</a>
                                            </div>
                                            <div class="col-sm-8">
                                                <!-- <div class="text-sm-end">
                                                    <button type="button" class="btn btn-success mb-2 me-1"><i class="mdi mdi-cog-outline"></i></button>
                                                    <button type="button" class="btn btn-light mb-2 me-1">Import</button>
                                                    <button type="button" class="btn btn-light mb-2">Export</button>
                                                </div> -->
                                            </div><!-- end col-->
                                        </div>

                                        <div class="table-responsive">
                                            <table class="table table-centered w-100 dt-responsive nowrap" id="products-datatable">
                                                <thead class="table-light">
                                                    <tr>
                                                        <!-- <th class="all" style="width: 10px;">
                                                            <div class="form-check">
                                                                <input type="checkbox" class="form-check-input" id="customCheck1">
                                                                <label class="form-check-label" for="customCheck1">&nbsp;</label>
                                                            </div>
                                                        </th> -->
                                                        <th style="width: 10%;">排序</th>
                                                        <th>名称</th>
                                                        <th style="width: 20%;">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <%(@tags||[]).each do |tag|%>
                                                    <tr>
                                                        <!-- <td>
                                                            <div class="form-check">
                                                                <input type="checkbox" class="form-check-input" id="customCheck2">
                                                                <label class="form-check-label" for="customCheck2">&nbsp;</label>
                                                            </div>
                                                        </td> -->
                                                        <td>
                                                            <%=text_field_tag 'que'+ tag.id.to_s, tag.que , class: 'special_que' %>
                                                        </td>
                                                        <td>
                                                            <%=tag.try(:name)%>
                                                        </td>
                                                        <td class="table-action">
                                                            <a href="" class="action-icon">
                                                                <!-- <i class="mdi mdi-eye"></i> -->
                                                                <p class="m-0 d-inline-block align-middle font-12">查看</p>
                                                            </a>

                                                            <a href="<%=edit_operation_tag_path(tag.id)%>" class="action-icon">
                                                                <!-- <i class="mdi mdi-square-edit-outline"></i> -->
                                                                <p class="m-0 d-inline-block align-middle font-12">编辑</p>

                                                            </a>
                                                            <a href="javascript:void(0);" class="action-icon"> <p class="m-0 d-inline-block align-middle font-12">删除</p></a>
                                                        </td>
                                                    </tr>
                                                    <%end%>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div style="display: flex;">
                                          <div>
                                            <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
                                          </div>
                                          <div style="flex: 1;text-align: right;">
                                            <% if @pagy.count <= 10 %>
                                              共 <b><%= @pagy.count %></b> 项
                                            <% else %>
                                              <%== pagy_info(@pagy) %>
                                            <% end %>
                                          </div>
                                        </div>

                                    </div> <!-- end card-body-->
                                </div> <!-- end card-->
                            </div> <!-- end col -->
                        </div>
                        <!-- end row -->

                    </div>
