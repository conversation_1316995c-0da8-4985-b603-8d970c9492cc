<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">设置</a></li>
            <li class="breadcrumb-item active">推送通知列表</li>
          </ol>
        </div>
        <h4 class="page-title">推送通知列表</h4>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-body">
      <%= link_to '创建推送', new_operation_push_message_path, class: 'btn btn-primary' %>
      <table class="table table-centered w-100 dt-responsive nowrap">
        <thead >
          <tr>
            <!-- <th>标题</th>
            <th>推送平台</th> -->
            <th>通知模板</th>
            <th>关联商品</th>
            <!-- <th>模板内容</th> -->
            <!-- <th>消息ID</th> -->
            <!-- <th>推送人群</th> -->
            <th>预计推送人数</th>
            <!-- <th>实际推送人数</th> -->
            <!-- <th>推送阶段</th> -->
            <th>推送时间</th>
            <th>创建时间</th>
            <th>推送状态</th>
            <th>操作</th>
          </tr>
        </thead>

        <tbody>
          <% @push_messages.each do |push_message| %>
            <tr>
              <!-- <td><%= push_message.title %></td> -->
              <!-- <td><%= push_message.include_sms_message %></td> -->
              <td><%= push_message.template_id_i18n %></td>
              <td><%= push_message.product.name %></td>
              <!-- <td><%= push_message.template_content %></td> -->
              <!-- <td><%= push_message.msg_id %></td> -->
              <!-- <td><%= push_message.crowd %></td> -->
              <td><%= push_message.expected_push_count %></td>
              <!-- <td><%= push_message.actual_push_count %></td> -->
              <!-- <td><%= push_message.timing %></td> -->
              <td><%= push_message.pushed_at.strftime("%F %T") %></td>
              <td><%= push_message.created_at.strftime("%F %T") %></td>
              <td><%= push_message.status_i18n %></td>
              <td>
                <%= link_to '详情', [:operation, push_message] %>
                <% if push_message.pending? %>
                  <%= link_to '编辑', edit_operation_push_message_path(push_message) %>
                  <%= link_to '撤销', revoke_operation_push_message_path(push_message), data: { confirm: '确定撤销吗？' }, method: :put %>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
</div>
