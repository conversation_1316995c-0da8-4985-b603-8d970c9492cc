<style>
  .form-control {
    width: 300px;
  }

  .form-select {
    width: 300px;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
  }
</style>
<div data-controller="push-message" push_message_id="<%= params[:id] %>" class="push-message-form">
  <%= form_for([:operation, @push_message]) do |f| %>
    <% if @push_message.errors.any? %>
      <div id="error_explanation">
        <h2><%= pluralize(@push_message.errors.count, "error") %> prohibited this push_message from being saved:</h2>

        <ul>
        <% @push_message.errors.full_messages.each do |msg| %>
          <li><%= msg %></li>
        <% end %>
        </ul>
      </div>
    <% end %>

    <div class="mt-2">
      <div class="title">选择推送类型</div>
      <div class="form-check form-check-inline">
        <%= f.check_box :include_sms_message, class: "form-check-input" %>
        <%= f.label :include_sms_message, "短信", class: "form-check-label" %>
      </div>

      <div class="form-check form-check-inline">
        <%= f.check_box :include_jpush, class: "form-check-input" %>
        <%= f.label :include_jpush, "极光", class: "form-check-label" %>
      </div>
    </div>

    <div class="mt-2">
      <%= f.label :product_id, "商品选择", class: "title" %><br>
      <%= f.select :product_id, Product.all.collect {|p| [ p.name, p.id ] }, { include_blank: true }, class: "form-select", required: true %>
    </div>

    <div class="mt-2">
      <%= f.label :template_id, "通知模板", class: "title" %><br>
      <%= f.select :template_id, [["商品开售通知", 'product_start_sale_notify'], ["商品预售通知", 'product_pre_sale_notify'], ["自定义内容", 'custom_notify']], { include_blank: true }, class: "form-select", required: true %>
    </div>

    <div class="mt-2">
      <%= f.label :title, "极光通知标题", class: "title" %><br>
      <%= f.text_field :title, class: "form-control", required: true %>
    </div>

    <div class="mt-2">
      <%= f.label :template_content, "极光通知内容", class: "title" %><br>
      <%= f.text_area :template_content, class: "form-control", required: true %>
    </div>

    <div class="mt-2">
      <%= f.label :crowd, "目标人群", class: "title" %><br>
      <div class="form-check form-check-inline">
        <input type="checkbox" class="form-check-input" id="push_message_crowd_1" name="push_message[crowd][]" value="1" <%= 'checked' if f.object.crowd&.include?("1") %>>
        <label class="form-check-label subscription_count" for="push_message_crowd_1">订阅商品的用户(0)</label>
      </div>
      <div class="form-check form-check-inline">
        <input type="checkbox" class="form-check-input" id="push_message_crowd_2" name="push_message[crowd][]" value="2" <%= 'checked' if f.object.crowd&.include?("2") %>>
        <label class="form-check-label collect_count" for="push_message_crowd_2">收藏商品的用户(0)</label>
      </div>
      <div class="form-check form-check-inline">
        <% old_users_arr = CSV.open(Rails.root.to_s + '/db/old_users.csv').to_a.flatten %>
        <input type="checkbox" class="form-check-input" id="push_message_crowd_3" name="push_message[crowd][]" value="3" <%= 'checked' if f.object.crowd&.include?("3") %>>
        <label class="form-check-label all_count" for="push_message_crowd_3">系统所有用户(<%= (User.where.not(phone: ["", nil]).pluck(:phone) + old_users_arr).uniq.size %>)</label>
      </div>
    </div>

    <div class="mt-2">
      <%= f.label :timing, "推送时机", class: "title" %><br>
      <div class="form-check form-check-inline">
        <input type="radio" id="push_message_timing_1" name="push_message[timing]" class="form-check-input push_message_timing" value="definite_time" <%= 'checked' if f.object.definite_time? || f.object.timing.blank? %>>
        <label class="form-check-label" for="push_message_timing_1">定时推送</label>
      </div>
      <div class="form-check form-check-inline">
        <input type="radio" id="push_message_timing_2" name="push_message[timing]" class="form-check-input push_message_timing" value="immediately" <%= 'checked' if f.object.immediately? %>>
        <label class="form-check-label" for="push_message_timing_2">立即推送</label>
      </div>
    </div>

    <div class="mt-1">
      <%= f.datetime_field :pushed_at, class: "form-control", style: "display: #{f.object.immediately? ? 'none' : 'block'}" %>
    </div>

    <div class="mt-2">
      <%= f.submit "保存", class: "btn btn-primary" %>
    </div>
  <% end %>
</div>