<style>
  .form-disable {
    pointer-events: none;
    opacity: 0.6;
  }
</style>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">设置</a></li>
            <li class="breadcrumb-item active">系统配置</li>
          </ol>
        </div>
        <h4 class="page-title">系统配置</h4>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-body">
      <%= form_with model: @system_config, url: operation_system_config_path(@system_config), html: { class: "#{ 'form-disable' unless policy(@system_config).update?}" } do |f| %>
        <div class="form-group row">
          <div class="form-group col-xl-12">
          <label for="system_config_auto_refund">订单自动退款</label>
          <%= f.check_box("auto_refund", { "data-switch": "success" }, 1, 0) %>
          <label for="system_config_auto_refund" data-on-label="开启" data-off-label="关闭" class="mb-0" style="position: absolute;"></label>
        </div>

        <div class="mt-2">
          <%= f.submit "保存", class: "btn btn-primary" %>
      <% end %>
    </div>
  </div>
</div>
