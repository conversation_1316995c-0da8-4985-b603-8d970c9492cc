<style>
  .table-responsive {
    height: 450px;
    overflow: scroll;
  }

  thead tr:first-child {
      background-color: #CCC;
      position:sticky;
      top:0px;
      padding-top:10px;
  }

  hr {
    margin: 0;
    border-top: 1px solid #adadad;
  }

  .item_user_time {
    color: #b2b2b2;
    font-size: 12px;
  }
  .item_user_name {
    color: #ed7b2f;
    font-size: 15px;
  }

  .timeline {
    border-left: 1px solid hsl(0, 0%, 90%);
    position: relative;
    list-style: none;
  }

  .timeline .timeline-item {
    position: relative;
  }

  .timeline .timeline-item:after {
    position: absolute;
    display: block;
    top: 0;
  }

  .timeline .timeline-item:after {
    background-color: hsl(0, 0%, 90%);
    left: -38px;
    border-radius: 50%;
    height: 11px;
    width: 11px;
    content: "";
  }
  .timeline:before {
    background-color: #ffffff;
    bottom: 0;
    content: "";
    left: 50%;
    position: absolute;
    top: 30px;
    width: 2px;
    z-index: 0;
  }

  .timeline .timeline-item-first {
    position: relative;
  }

  .timeline .timeline-item-first:after {
    background-color: hsl(0, 0%, 90%);
    left: -40px;
    border-radius: 50%;
    height: 15px;
    width: 15px;
    content: "";
    position: absolute;
    display: block;
    top: 0;
  }
  .dropdown-menu-start {
    position: absolute;
    inset: 0px auto auto 0px !important;
    margin: 0px;
    transform: translate3d(0px, 34px, 0px) !important;
  }

  .opinion-detail-show:hover .mark-opinion {
    display: initial;
  }

  .mark-opinion {
    display: none;
    cursor: pointer;
  }
</style>
<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">反馈建议</li>
          </ol>
        </div>
        <h4 class="page-title">反馈建议</h4>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <div class="card" style="height: 613px;">
        <div class="card-body">
          <h4 class="page-title">处理中 <span class="badge badge-warning-lighten"><%= @processing_pagy.count %></span></h4>
          <div class="table-responsive">
            <table class="table table-centered mb-0">
              <thead class="table-light">
                <tr>
                  <th>反馈详情</th>
                  <th style="width: 80px">反馈人</th>
                  <th style="width: 140px;">最后回复人</th>
                  <th style="width: 65px;">操作</th>
                </tr>
              </thead>
              <tbody>
                <% if @processing_opinions.blank? %>
                  <tr>
                    <td colspan="5" style="text-align: center;">
                      暂无数据
                    </td>
                  </tr>
                <% end %>
                <% @processing_opinions.each do |opinion| %>
                  <tr>
                    <!-- <td>
                      <%= opinion.code %>
                    </td>
                    <td style="width: 200px;">
                      <%= opinion.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
                    </td> -->
                    <td class="opinion-detail-show">
                      <span data-bs-toggle="modal" style="cursor: pointer;" data-controller="opinion" data-action="click->opinion#show" opinion-id="<%= opinion.id %>" data-bs-target="#detail-right-modal-<%= opinion.id %>"><%= opinion.detail %></span>
                      <svg t="1694503112191" title="标记" class="icon mark-opinion" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5385" width="18" height="18"><path d="M122.88 0c-22.528 0-40.96 18.432-40.96 40.96v942.08c0 11.264 4.608 21.504 11.776 28.672 7.168 7.168 17.408 11.776 29.184 11.776 22.528 0 40.96-18.432 40.96-40.96v-321.024c34.816-9.216 117.76-43.52 163.84-51.2 43.008-7.168 80.384-10.24 113.152-10.24 146.944 0 211.456 57.856 303.616 61.952 7.68 0.512 14.848 0.512 22.016 0.512 61.44 0 97.792-12.8 129.536-16.896 23.04-3.072 45.568-25.088 45.568-48.64l0.512-529.408c0-26.112-21.504-46.592-46.592-46.592-3.072 0-6.656 0.512-10.24 1.024-55.296 12.288-137.728 41.984-188.416 45.056-5.632 0.512-11.264 0.512-17.408 0.512-99.328 0-250.88-47.104-388.608-47.104H276.48c-45.568 1.024-83.456 10.24-113.664 17.408-0.512-10.24-5.12-19.456-11.776-26.112C144.384 4.608 134.144 0 122.88 0z" p-id="5386" fill="<%= opinion.is_mark? ? '#1677ff' : '#dbdbdb' %>"></path></svg>
                    </td>
                    <td>
                      <%= link_to opinion.user&.user_name, operation_users_path(seach_id: opinion.user_id), target: "_blank" %>
                    </td>
                    <td>
                      <% if opinion.child_opinions.last&.user.present? %>
                        <%= link_to opinion.child_opinions.last.user.username, operation_users_path(seach_id: opinion.child_opinions.last.user_id), target: "_blank" %>
                      <% else %>
                        无
                      <% end %>
                    </td>
                    <td>
                      <a href="javascript:void(0);" class="action-icon" data-bs-toggle="modal" data-controller="opinion" data-action="click->opinion#show" opinion-id="<%= opinion.id %>" data-bs-target="#detail-right-modal-<%= opinion.id %>">
                        <p class="m-0 d-inline-block align-middle font-12">回复</p>
                      </a>
                    </td>
                  </tr>
                  <div id="detail-right-modal-<%= opinion.id %>" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
                    <%= render "right_reply", opinion: opinion %>
                  </div>
                <% end %>
              </tbody>
            </table>
          </div>
          <br>
          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@processing_pagy) if @processing_pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @processing_pagy.count <= 10 %>
                共 <b><%= @processing_pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@processing_pagy) %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="card" style="height: 613px;">
        <div class="card-body">
          <h4 class="page-title">待处理 <span class="badge badge-primary-lighten"><%= @pending_pagy.count %></span></h4>
          <div class="table-responsive">
            <table class="table table-centered mb-0">
              <thead class="table-light">
                <tr>
                  <!-- <th style="width: 170px;">工单号</th>
                  <th style="width: 190px;">反馈时间</th> -->
                  <th>反馈详情</th>
                  <th style="width: 140px;">反馈人</th>
                  <th style="width: 75px;">操作</th>
                </tr>
              </thead>
              <tbody>
                <% if @pending_opinions.blank? %>
                  <tr>
                    <td colspan="5" style="text-align: center;">
                      暂无数据
                    </td>
                  </tr>
                <% end %>
                <% @pending_opinions.each do |opinion| %>
                  <tr>
                    <!-- <td>
                      <%= opinion.code %>
                    </td>
                    <td style="width: 200px;">
                      <%= opinion.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
                    </td> -->
                    <td class="opinion-detail-show">
                      <span data-bs-toggle="modal" style="cursor: pointer;" data-controller="opinion" data-action="click->opinion#show" opinion-id="<%= opinion.id %>" data-bs-target="#detail-right-modal-<%= opinion.id %>"><%= opinion.detail %></span>
                      <svg t="1694503112191" title="标记" class="icon mark-opinion" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5385" width="18" height="18"><path d="M122.88 0c-22.528 0-40.96 18.432-40.96 40.96v942.08c0 11.264 4.608 21.504 11.776 28.672 7.168 7.168 17.408 11.776 29.184 11.776 22.528 0 40.96-18.432 40.96-40.96v-321.024c34.816-9.216 117.76-43.52 163.84-51.2 43.008-7.168 80.384-10.24 113.152-10.24 146.944 0 211.456 57.856 303.616 61.952 7.68 0.512 14.848 0.512 22.016 0.512 61.44 0 97.792-12.8 129.536-16.896 23.04-3.072 45.568-25.088 45.568-48.64l0.512-529.408c0-26.112-21.504-46.592-46.592-46.592-3.072 0-6.656 0.512-10.24 1.024-55.296 12.288-137.728 41.984-188.416 45.056-5.632 0.512-11.264 0.512-17.408 0.512-99.328 0-250.88-47.104-388.608-47.104H276.48c-45.568 1.024-83.456 10.24-113.664 17.408-0.512-10.24-5.12-19.456-11.776-26.112C144.384 4.608 134.144 0 122.88 0z" p-id="5386" fill="<%= opinion.is_mark? ? '#1677ff' : '#dbdbdb' %>"></path></svg>
                    </td>
                    <td>
                      <%= link_to opinion.user&.user_name, operation_users_path(seach_id: opinion.user_id), target: "_blank" %>
                    </td>
                    <td style="width: 100px;">
                      <a href="javascript:void(0);" class="action-icon" data-bs-toggle="modal" data-bs-target="#detail-right-modal-<%= opinion.id %>">
                        <p class="m-0 d-inline-block align-middle font-12">回复</p>
                      </a>
                    </td>
                  </tr>
                  <div id="detail-right-modal-<%= opinion.id %>" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
                    <%= render "right_reply", opinion: opinion %>
                  </div>
                <% end %>
              </tbody>
            </table>
          </div>
          <br>
          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@pending_pagy) if @pending_pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @pending_pagy.count <= 10 %>
                共 <b><%= @pending_pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@pending_pagy) %>
              <% end %>
            </div>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="col-12 mt-2">
            <ul class="nav nav-tabs nav-bordered mb-3">
              <% ([["全部", nil], ["已完成", 2], ["已取消", 3]]).each do |category, value| %>
                <li class="nav-item">
                  <%= link_to operation_opinions_path(q: {status_eq: value}), "aria-expanded": false, class: "nav-link #{(params[:q]&.dig(:status_eq).to_s == value.to_s && params[:q]&.dig(:is_mark_eq).blank?) ? 'active' : '' }" do %>
                    <span class="d-none d-md-block"><%= category %></span>
                  <% end %>
                </li>
              <% end %>
              <li class="nav-item">
                <%= link_to operation_opinions_path(q: {is_mark_eq: true}), "aria-expanded": false, class: "nav-link #{params[:q]&.dig(:is_mark_eq) == "true" ? 'active' : '' }" do %>
                  <span class="d-none d-md-block">已标记</span>
                <% end %>
              </li>
            </ul>
          </div>

          <div class="table-responsive">
            <table class="table table-centered mb-0">
              <thead class="table-light">
                <tr>
                  <th>反馈详情</th>
                  <th style="width: 140px;">反馈人</th>
                  <th style="width: 185px">反馈时间</th>
                  <th style="width: 170px">工单号</th>
                  <th style="width: 90px">问题类型</th>
                  <th style="width: 75px">状态</th>
                  <th style="width: 90px">填写号码</th>
                  <th style="width: 75px;">操作</th>
                </tr>
              </thead>
              <tbody>
                <% if @opinions.blank? %>
                  <tr>
                    <td colspan="8" style="text-align: center;">
                      暂无数据
                    </td>
                  </tr>
                <% end %>
                <% @opinions.each do |opinion| %>
                  <tr>
                    <td class="opinion-detail-show">
                      <span data-bs-toggle="modal" style="cursor: pointer;" data-controller="opinion" data-action="click->opinion#show" opinion-id="<%= opinion.id %>" data-bs-target="#detail-right-modal-<%= opinion.id %>"><%= opinion.detail %></span>
                      <svg t="1694503112191" title="标记" class="icon mark-opinion" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5385" width="18" height="18"><path d="M122.88 0c-22.528 0-40.96 18.432-40.96 40.96v942.08c0 11.264 4.608 21.504 11.776 28.672 7.168 7.168 17.408 11.776 29.184 11.776 22.528 0 40.96-18.432 40.96-40.96v-321.024c34.816-9.216 117.76-43.52 163.84-51.2 43.008-7.168 80.384-10.24 113.152-10.24 146.944 0 211.456 57.856 303.616 61.952 7.68 0.512 14.848 0.512 22.016 0.512 61.44 0 97.792-12.8 129.536-16.896 23.04-3.072 45.568-25.088 45.568-48.64l0.512-529.408c0-26.112-21.504-46.592-46.592-46.592-3.072 0-6.656 0.512-10.24 1.024-55.296 12.288-137.728 41.984-188.416 45.056-5.632 0.512-11.264 0.512-17.408 0.512-99.328 0-250.88-47.104-388.608-47.104H276.48c-45.568 1.024-83.456 10.24-113.664 17.408-0.512-10.24-5.12-19.456-11.776-26.112C144.384 4.608 134.144 0 122.88 0z" p-id="5386" fill="<%= opinion.is_mark? ? '#1677ff' : '#dbdbdb' %>"></path></svg>
                    </td>
                    <td>
                      <%= link_to opinion.user&.user_name, operation_users_path(seach_id: opinion.user_id), target: "_blank" %>
                    </td>
                    <td>
                      <%= opinion.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
                    </td>
                    <td>
                      <%= opinion.code %>
                    </td>
                    <td>
                      <%= opinion.option_type_i18n %>
                    </td>
                    <td>
                      <%= opinion.status_i18n %>
                    </td>
                    <td>
                      <%= opinion.mobile.present? ? opinion.mobile : "无" %>
                    </td>
                    <td style="width: 100px;">
                      <a href="javascript:void(0);" class="action-icon" data-bs-toggle="modal" data-bs-target="#detail-right-modal-<%= opinion.id %>">
                        <p class="m-0 d-inline-block align-middle font-12">回复</p>
                      </a>
                    </td>
                  </tr>
                  <div id="detail-right-modal-<%= opinion.id %>" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
                    <%= render "right_reply", opinion: opinion %>
                  </div>
                <% end %>
              </tbody>
            </table>
          </div>
          <br>
          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @pagy.count <= 10 %>
                共 <b><%= @pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@pagy) %>
              <% end %>
            </div>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>
</div>

<script>
    $('.modal').on('hidden.bs.modal', function (e) {
      Turbo.visit(location.toString())
    })

    $('.mark-opinion').on('click', function() {
      _this = $(this)
      current_is_mark = _this.find("path").attr("fill") == "#dbdbdb" ? false : true
      console.log("current_is_mark")
      console.log(_this.find("path").attr("fill"))
      $.ajax({
        url: "/operation/opinions/" + $(this).parent().find('span').attr('opinion-id') + "/mark",
        type: "POST",
        dataType: "json",
        data: {
          is_mark: current_is_mark
        },
        success: function(data) {
          if (data.status == 200) {
            if (data.is_mark == true){
              _this.find("path").attr("fill", "#1677ff")
              message("标记成功");
            } else {
              _this.find("path").attr("fill", "#dbdbdb")
              message("已取消标记");
            }
          }
        }
      })
    })
</script>
