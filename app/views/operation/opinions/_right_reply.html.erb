<% btn_color = case opinion.status
  when "pending"
    "btn-soft-primary"
  when "processing"
    "btn-soft-warning"
  when "completed"
    "btn-soft-success"
  when "canceled"
    "btn-soft-danger"
  else
    "btn-soft-primary"
  end %>

<div class="modal-dialog modal-right" style="width: 550px;max-width: 550px;flex-flow: unset;">
  <div class="modal-content">
    <div class="modal-header border-0" style="padding-left: 34px;padding-right: 34px;background: #f4f5f9;">
      <span style="font-size: 16px">反馈详情</span>
      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
    </div>
    <div class="modal-body" style="padding-top: 0px;overflow: scroll;overflow-x:hidden;scrollbar-width: 1px">
      <div class="row" style="padding: 0px 10px;">
        <div style="margin-top: 20px;">&nbsp&nbsp&nbsp工单号：<%= opinion.code %></div>
        <div style="margin-top: 12px;">问题类型：<%= opinion.option_type_i18n %></div>
        <div style="margin-top: 12px;">填写号码：<%= opinion.mobile.present? ? opinion.mobile : "无" %></div>
        <div style="margin-top: 10px;" class="d-flex">
          <div style="margin-top: 5px">
            处理进度：
          </div>
          <div class="dropdown">
            <button class="btn <%= btn_color %> btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" data-bs-display="dynamic" aria-haspopup="true" aria-expanded="false">
              <%= opinion.status_i18n %>
            </button>
            <ul class="dropdown-menu dropdown-menu-start" opinion-id="<%= opinion.id %>">
              <li><a class="dropdown-item" status="pending" data-controller="opinion" data-action="click->opinion#update_status" href="javascript:;">待处理</a></li>
              <li><a class="dropdown-item" status="processing" data-controller="opinion" data-action="click->opinion#update_status" href="javascript:;">处理中</a></li>
              <li><a class="dropdown-item" status="completed" data-controller="opinion" data-action="click->opinion#update_status" href="javascript:;">已完成</a></li>
              <li><a class="dropdown-item" status="canceled" data-controller="opinion" data-action="click->opinion#update_status" href="javascript:;">已取消</a></li>
            </ul>
          </div>
        </div>
        <div class="row mt-2 mb-3">
          <div class="col-md-1">
            <%= image_tag opinion.user.avatar, class: "rounded-circle", size: "40x40" %>
          </div>
          <div class="col-md-11" style="padding-left: 20px;">
            <span class="item_user_name"><%= opinion.user.user_name %></span> <span class="item_user_time"><%= time_ago_in_words opinion.created_at %></span>
            <div class="mt-2">
              <%= opinion.detail %>
              <% if !opinion.asset_imgs.blank? %>
                <br>
                <% opinion.asset_imgs.each do |a_i| %>
                  <img src="<%= a_i.try(:image) %>" class="rounded me-1 img-preview mb-2" height="42" width="42"/>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 10px;background: #F2F2F2;margin-top: 20px;width: 550px;margin-left: -20px;"></div>
      <div class="row" style="padding: 20px 10px;">
        <div style="margin-bottom: 16px;">
          <h5>评论 <%= opinion.child_opinions.count %></h5>
        </div>
        <div class="col-md-1">
          <%= image_tag "logo_black.jpg", class: "rounded-circle", size: "40x40" %>
        </div>
        <div class="col-md-11" style="padding-left: 20px;" data-controller="opinion">
          <% if opinion.status.in?(["pending", "processing"]) %>
            <textarea class="form-control detail" data-opinion-target="detail" rows="5" placeholder="请输入回复内容..."></textarea>
            <button class="btn btn-primary btn-sm mt-1 reply" data-action="click->opinion#greet" style="float:right;width: 74px;margin-bottom: 10px;" opinion-id="<%= opinion.id %>">回复</button>
          <% else %>
            <textarea class="form-control detail" data-opinion-target="detail" rows="5" placeholder="当前状态不支持回复" disabled></textarea>
            <button class="btn btn-primary btn-sm mt-1 reply" data-action="click->opinion#greet" style="float:right;width: 74px;margin-bottom: 10px;" opinion-id="<%= opinion.id %>" disabled>回复</button>
          <% end %>
        </div>
      </div>
      <div style="padding: 0px 10px;">
        <ul class="timeline">
          <% opinion.child_opinions.order(id: :desc).each_with_index do |child_opinion, index| %>
            <hr>
            <li class="<%= index == 0 ? 'timeline-item-first' : 'timeline-item' %>">
              <div class="row" style="margin-top: 20px;">
                <div class="col-md-1">
                  <%= image_tag child_opinion.user.avatar, class: "rounded-circle", size: "40x40" %>
                </div>
                <div class="col-md-11" style="padding-left: 20px;">
                  <span class="item_user_name"><%= child_opinion.user.user_name %></span>
                  <% if child_opinion.user.is_official_user? %>
                    <span class="badge bg-warning" style="width: 36px;border-radius: 2px;background: linear-gradient(90deg, #FAD888 0%, #BC9263 100%);">官方</span>
                  <% end %>
                  <span class="item_user_time"><%= time_ago_in_words child_opinion.created_at %></span>

                  <div class="mt-2" style="margin-bottom: 20px;">
                    <%= child_opinion.detail %>
                    <% if !child_opinion.asset_imgs.blank? %>
                      <br>
                      <% child_opinion.asset_imgs.each do |a_i| %>
                        <img src="<%= a_i.try(:image) %>" class="rounded me-1 img-preview mb-2" height="42" width="42"/>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
  </div><!-- /.modal-content -->
</div><!-- /.modal-dialog -->
