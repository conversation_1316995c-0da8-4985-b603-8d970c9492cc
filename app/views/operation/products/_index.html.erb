<div class="table-responsive">
  <table class="table table-centered w-100 dt-responsive nowrap" id="products-datatable">
    <thead class="table-light">
    <tr>
      <th style="width: 250px;">商品信息</th>
      <th>售价</th>
      <th>总库存</th>
      <th>预售量</th>
      <th>总销量</th>
      <th>待发货</th>
      <th>订阅数</th>
      <th>位置</th>
      <th>是否显示</th>
      <th>是否在售</th>
      <th>讨论</th>
      <th>小程序码</th>
      <th style="width: 60px;">操作</th>
    </tr>
    </thead>
    <tbody>
      <% @products.each do |product| %>
        <tr>
          <td>
            <img src="<%= product.try(:list_img_url) %>" alt="contact-img" title="contact-img" class="rounded me-1" height="48"/>
            <p class="m-0 d-inline-block align-middle font-12">
              <span class="text-body"><%= product.name %></span>
              <br/>
              <span class="text-muted">商品ID:<%= product.id %></span>
              <% uniq_key = ShortUrl.find_or_create_by(redirect_to: Settings.mobile_host + '/web/products/' + product.share_id).uniq_key %>
              <a class="text-primary share-link" href="javascript:;" data-clipboard-text="<%= Settings.mobile_host + '/' + uniq_key %>" data-controller="product" data-action="click->product#copy_success"><i class="mdi mdi-share-outline"></i>推广</a>
            </p>
          </td>
          <td>
            <% g_first, g_last = product.g_first_last %>
            <p class="m-0 d-inline-block align-middle font-12">
              <span>￥<%= g_first %></span><br/>
              <span style="padding-left: 50%">~</span><br/>
              <span>￥<%= g_last %></span><br/>
            </p>
          </td>
          <td>
            <%= product.skus.sum(:sku_quantity) %>
          </td>
          <td>
            <% order_ids = Order.joins(:order_details).where("order_details.product_id = #{product.id}").where(status: 2000) %>
            <%= link_to order_ids.size, "#", "data-bs-toggle": "modal", "data-bs-target": "#pre-sale-modal-#{product.id}" %>
            <div id="pre-sale-modal-<%= product.id %>" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="standard-modalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title" id="standard-modalLabel">预售详情</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                          <% pre_sale_skus = OrderDetail.unscope(:order).where(order_id: order_ids).group(:property_name).count.to_a %>
                          <% pre_sale_skus = pre_sale_skus.sort_by { |sub_array| -sub_array[1] } %>
                          <ul class="list-group">
                            <% pre_sale_skus.each do |property_name, order_count| %>
                              <li class="list-group-item d-flex justify-content-between align-items-center">
                                  <%= JSON.parse(property_name).join("-") %>
                                  <span class="badge bg-primary rounded-pill"><%= order_count %></span>
                              </li>
                            <% end %>
                          </ul>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-light" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->
          </td>
          <td>
            <%= product.try(:sale_count).to_i %>
          </td>
          <td>
            <%= product.order_status_1_count %>
          </td>
          <td>
            <%= product.subscriptions.where(is_subscription: true).count %>
          </td>
          <td>
            <%= product.que %><span class="edit" data-bs-toggle="modal" data-bs-target="#edit-modal<%= product.id %>"><i class="ri-edit-2-line"></i></span>
          </td>
          <td>
            <div style="margin-top: 12px;">
              <% if policy(product).update? %>
                <input type="checkbox" class="goods_show_status"  <%= product.try(:on_shelf) ? "checked=''" : "" %>  id="<%= 'show_status' + product.id.to_s %>" data-switch="success">
                <label for="<%= 'show_status' + product.id.to_s %>" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;"></label>
              <% else %>
                <input type="checkbox" class="goods_show_status" <%= product.try(:on_shelf) ? "checked=''" : "" %>  id="<%= 'show_status' + product.id.to_s %>" data-switch="success" disabled>
                <label for="<%= 'show_status' + product.id.to_s %>" title="无权限" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;"></label>
              <% end %>
            </div>
          </td>
          <td>
            <div style="margin-top: 12px;">
              <% if policy(product).update? %>
                <input type="checkbox" class="goods_status" <%= product.try(:is_sale) ? "checked=''" : "" %>  id="<%= 'status' + product.id.to_s %>" data-switch="success">
                <label for="<%= 'status' + product.id.to_s %>" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;"></label>
              <% else %>
                <input type="checkbox" class="goods_status" <%= product.try(:is_sale) ? "checked=''" : "" %>  id="<%= 'status' + product.id.to_s %>" data-switch="success" disabled>
                <label for="<%= 'status' + product.id.to_s %>" title="无权限" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;"></label>
              <% end %>
            </div>
          </td>
          <td>
            <%= link_to product.try(:comments).try(:count), operation_product_comments_path(product) %>
          </td>
          <td>
            <%= image_tag product.try(:qrcode), height: 50, width: 50, class: "img-preview" %>
          </td>
          <td>
            <a href="<%= edit_operation_product_path(product.id) %>" class="action-icon">
              <i class="mdi mdi-square-edit-outline" title="编辑"></i>
            </a>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <% @products.each do |product| %>
    <div id="edit-modal<%= product.id %>" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="standard-modalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title" id="standard-modalLabel"><%= product.name %></h4>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
          </div>
          <%= form_with(model: [:operation, product], url: update_que_operation_product_path(product)) do |f| %>
            <div class="modal-body">
              <label for="que">位置</label>
              <%= f.number_field :que, class: 'form-control col-xl-3', placeholder: "输入商品权重", required: true %>
              <input type="hidden" name="origin_url" value="<%= request.url %>">
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
              <button type="submit" class="btn btn-primary">保存</button>
            </div>
          <% end %>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
  <% end %>
</div>
<div style="display: flex;">
  <div>
    <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
  </div>
  <div style="flex: 1;text-align: right;">
    <% if @pagy.count <= 10 %>
      共 <b><%= @pagy.count %></b> 项
    <% else %>
      <%== pagy_info(@pagy) %>
    <% end %>
  </div>
</div>

<script>
  new ClipboardJS('.share-link');
</script>