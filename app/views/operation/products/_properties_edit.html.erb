<div class="card container shadow-lg mb-3">
  <div class="card-body">
    <h4>商品规格</h4>
    <div class="row">
      <div class="control-group">
        <div class="controls">
          <a id="add_lv1" class="btn btn-light btn-sm" href="javascript:;">添加属性</a>

          <% property_ids = SkuProperty.where(sku_id: @product.sku_ids).pluck(:property_id) %>
          <% properties = Property.where(id: property_ids) %>
          <% parent_ids = properties.pluck(:parent_id).uniq %>
          <% parent_ids.each do |parent_id| %>
            <!-- 规格编辑 -->
            <div class="control-group lv1">
              <div class="row">
                <label class="col-1 property-label">属性名：</label>
                <div class="col-9 controls">
                  <input type="text" name="lv1" placeholder="属性名" class="form-control lv-width" value="<%= Property.find(parent_id).name %>" required="required">
                  <a class="remove_lv1" href="javascript:;">移除</a>
                </div>
              </div>
              <div class="row mt-2">
                <label class="col-1 property-value-label">属性值：</label>
                <div class="controls col-11 lv2s">
                  <% properties.where(parent_id: parent_id).pluck(:name).each do |name| %>
                    <div class="property-value-list">
                      <input type="text" name="lv2" class="form-control property-value-input" placeholder="属性值" value="<%= name %>" required="required">
                      <a class="remove_lv2" href="javascript:;"><i class="mdi mdi-close-circle"></i></a>
                    </div>
                  <% end %>
                  <a class="add_lv2" href="javascript:;">添加属性值</a>
                </div>
              </div>
            </div>
          <% end %>

        </div>
      </div>
    </div>
  </div>
</div>

<div class="card container shadow-lg mb-3">
  <div class="card-body">
    <h4>销售规格</h4>
    <div class="row mt-3" id="old_attru">
      <div class="row">
        <div class="form-group col-xl-12 mb-12">
          <div id="lv_table_con" class="control-group" style="display: none;">
            <div class="controls">
              <div id="lv_table"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  var lv1HTML = '<div class="control-group lv1">' +
      '<div class="row">' +
      '<label class="col-1 property-label">属性名：</label>' +
      '<div class="col-9 controls">' +
      '<input type="text" name="lv1" class="form-control lv-width" placeholder="属性名" required="required" style="margin-right: 4px;">' +
      '<a class="remove_lv1" href="javascript:;" style="margin-left: 14px;">移除</a>' +
      '</div>' +
      '</div>' +
      '<div class="row mt-2">' +
      '<label class="col-1 property-value-label">属性值：</label>' +
      '<div class="controls col-11 lv2s">' +
      '<div class="property-value-list" style="margin-right: 9px;">' +
      '<input type="text" name="lv2" class="form-control property-value-input" placeholder="属性值" required="required" style="margin-right: 4px;">' +
      '<a class="remove_lv2" href="javascript:;"><i class="mdi mdi-close-circle"></i></a>' +
      '</div>' +
      '<a class="add_lv2" href="javascript:;">添加属性值</a>' +
      '</div>' +
      '</div>' +
      '</div>';

  var lv2HTML = '<div class="property-value-list">' +
      '<input type="text" name="lv2" class="form-control property-value-input" placeholder="属性值" required="required" style="margin-right: 4px;">' +
      '<a class="remove_lv2" href="javascript:;"><i class="mdi mdi-close-circle"></i></a>' +
      '</div>';

  $(document).ready(function() {
    generate_sku();

    $('#add_lv1').on('click', function() {
      var last = $('.control-group.lv1:last');
      if (!last || last.length == 0) {
          $(this).parents('.control-group').eq(0).after(lv1HTML);
      } else {
          last.after(lv1HTML);
      }
    });

    $(document).on('click', '.remove_lv1', function() {
        $(this).parents('.lv1').remove();
        generate_sku();
    });

    $(document).on('click', '.add_lv2', function() {
      $(this).parents('.lv1').find('.lv2s').find('.property-value-list:last').after(lv2HTML);
    });

    $(document).on('click', '.remove_lv2', function() {
      const divElement = $(this).parents('.lv2s');
      $(this).parent().remove();
      auto_delete_lv1(divElement)
      generate_sku();
    });

    $(document).on('change', 'input[name="lv1"], input[name="lv2"]', function() {
      generate_sku();
    });
  });

  function auto_delete_lv1(divElement) {
    if (divElement.children().length > 0) {
      console.log('该 div 元素下有子元素');
    } else {
      divElement.parents('.lv1').remove();
    }
  }

  function generate_sku() {
    var lv1Arr = $('input[name="lv1"]');
    if (!lv1Arr || lv1Arr.length == 0) {
        $('#lv_table_con').hide();
        $('#lv_table').html('');
        return;
    }
    for (var i = 0; i < lv1Arr.length; i++) {
        var lv2Arr = $(lv1Arr[i]).parents('.lv1').find('input[name="lv2"]');
        if (!lv2Arr || lv2Arr.length == 0) {
            // alert('请先删除无参数的规格项！');
            return;
        }
    }

    var tableHTML = '';
    tableHTML += '<table class="table table-bordered">';
    tableHTML += '    <thead>';
    tableHTML += '        <tr>';
    for (var i = 0; i < lv1Arr.length; i++) {
        tableHTML += '<th style="width: 100px;">' + $(lv1Arr[i]).val() + '</th>';
    }
    tableHTML += '            <th><span class="validate-error">*</span>原价(元)</th>';
    tableHTML += '            <th><span class="validate-error">*</span>现价(元)</th>';
    tableHTML += '            <th><span class="validate-error">*</span>库存</th>';
    tableHTML += '            <th>商家编码</th>';
    tableHTML += '            <th>配图</th>';
    tableHTML += '        </tr>';
    tableHTML += '    </thead>';
    tableHTML += '    <tbody>';

    var numsArr = new Array();
    var idxArr = new Array();
    //numsArr记录每个规格又多少个参数，并用idxarr记录规格数组下标 方便后边标记name
    for (var i = 0; i < lv1Arr.length; i++) {
        numsArr.push($(lv1Arr[i]).parents('.lv1').find('input[name="lv2"]').length);
        idxArr[i] = 0;
    }

    var len = 1;
    var rowsArr = new Array();
    for (var i = 0; i < numsArr.length; i++) {
        //len  记录参数总行数
        len = len * numsArr[i];

        var tmpnum = 1;
        for (var j = numsArr.length - 1; j > i; j--) {
            tmpnum = tmpnum * numsArr[j];
        }
        //当前规格每个参数所占行数
        rowsArr.push(tmpnum);
    }

    var jsonArray = JSON.parse('<%= raw @product.skus_arr.to_json %>');
    var existsArr = new Array();

    for (var i = 0; i < len; i++) {
        tableHTML += '        <tr data-row="' + (i+1) + '">';

        var name = '';
        for (var j = 0; j < lv1Arr.length; j++) {
            var n = parseInt(i / rowsArr[j]);
            if (j == 0) {
            } else if (j == lv1Arr.length - 1) {
                n = idxArr[j];
                if (idxArr[j] + 1 >= numsArr[j]) {
                    idxArr[j] = 0;
                } else {
                    idxArr[j]++;
                }
            } else {
                var m = parseInt(i / rowsArr[j]);
                n = m % numsArr[j];
            }

            var lv1_text = $(lv1Arr[j]).parents('.lv1').find('input[name="lv1"]').val();
            var text = $(lv1Arr[j]).parents('.lv1').find('input[name="lv2"]').eq(n).val();
            if (j != lv1Arr.length - 1) {
                name += lv1_text + '_' + text + ',';
            } else {
                name += lv1_text + '_' + text;
            }

            if (i % rowsArr[j] == 0) {
                tableHTML += '<td width="50" rowspan="' + rowsArr[j] + '" data-rc="' + (i+1) + '_' + (j+1) + '">' + text + '</td>';
            }
        }

        var sku = jsonArray.find(function(obj) {
          var arr1 = obj.sku_name.split(",").sort();
          var arr2 = name.split(",").sort();
          var isEqual = arr1.join(",") === arr2.join(",");

          if (isEqual){
            existsArr.push(obj.sku_id);
          }

          return isEqual
        });

        var sku_id = sku ? sku.sku_id : '';
        var sku_price = sku ? sku.sku_price : '';
        var sku_markedprice = sku ? sku.sku_markedprice : '';
        var sku_quantity = sku ? sku.sku_quantity : '';
        var sku_number = sku ? sku.sku_number : '';
        var sku_pictureurl = sku ? sku.sku_pictureurl || '<%= image_url "upload_image.jpg" %>' : '<%= image_url "upload_image.jpg" %>';

        tableHTML += `<input type="hidden" class="form-control" name="product[skus_attributes][${i}][id]" value="${sku_id}" />`;
        tableHTML += `<td><input type="number" step="0.01" class="form-control" name="product[skus_attributes][${i}][sku_markedprice]" value="${sku_markedprice}" /></td>`;
        tableHTML += `<td><input type="number" step="0.01" class="form-control" name="product[skus_attributes][${i}][sku_price]" value="${sku_price}" /></td>`;
        tableHTML += `<td><input type="number" class="form-control" name="product[skus_attributes][${i}][sku_quantity]" value="${sku_quantity}" required="required" /></td>`;
        tableHTML += `<td><input type="text" class="form-control" name="product[skus_attributes][${i}][sku_number]" value="${sku_number}" /></td>`;
        tableHTML += `
          <td style="padding: 4px;text-align: center;">
            <input type="file" class="form-control product-image-input" style="display: none;" name="product[skus_attributes][${i}][sku_pictureurl]" value="${sku_pictureurl}" />
            <img class="product-image-pic" style="width: auto;height: 60px;" src="${sku_pictureurl}">
          </td>
        `;
        tableHTML += `<input type="hidden" class="form-control" name="product[skus_attributes][${i}][sku_properties_arrs]" value="${name}" />`;
        tableHTML += '</tr>';
    }

    var allSkuIds = jsonArray.map(function(obj) {
      return obj.sku_id;
    });

    var needDeleteSkuIds = allSkuIds.filter(function(value) {
      return !existsArr.includes(value);
    });

    for (var i = 0; i < needDeleteSkuIds.length; i++) {
      var sku_id = needDeleteSkuIds[i];
      a = i + 10000
      tableHTML += `<input type="hidden" class="form-control" name="product[skus_attributes][${a}][id]" value="${sku_id}" />`;
      tableHTML += `<input type="hidden" class="form-control" name="product[skus_attributes][${a}][_destroy]" value="true" />`;
    }

    console.log(needDeleteSkuIds);
    tableHTML += '</tbody>';
    tableHTML += '</table>';

    $('#lv_table_con').show();
    $('#lv_table').html(tableHTML);
  }
</script>