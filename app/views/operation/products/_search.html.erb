<%= search_form_for [:operation, @q], url: operation_products_path, method: :get,
    html: {class: "row gx-2 align-items-center"} do |f| %>
  <div class="col-auto mb-2">
    <div class="input-group flex-nowrap">
      <span class="input-group-text">商品标题</span>
      <%= f.text_field :name_cont, type: "search", class: "form-control" %>
    </div>
  </div>

  <div class="col-auto mb-2">
    <div class="input-group flex-nowrap">
      <span class="input-group-text">分类</span>
      <%= f.select :tag_id_eq, options_for_select([['全部', nil]] + @tags, params[:q]&.dig(:tag_id_eq)), {},
          type: "search", class: "form-select" %>
    </div>
  </div>

  <div class="col-auto mb-2">
    <div class="input-group flex-nowrap">
      <span class="input-group-text">是否显示</span>
      <%= f.select :on_shelf_eq, options_for_select([['全部', nil], ["是", true], ["否", false]], params[:q]&.dig(:on_shelf_eq)), {},
          type: "search", class: "form-select" %>
    </div>
  </div>

  <div class="col-auto mb-2">
    <div class="input-group flex-nowrap">
      <span class="input-group-text">是否在售</span>
      <%= f.select :is_sale_eq, options_for_select([['全部', nil], ["是", true], ["否", false]], params[:q]&.dig(:is_sale_eq)), {},
          type: "search", class: "form-select" %>
    </div>
  </div>

  <div class="col-auto mb-2">
    <div class="me-sm-2">
    <%= f.submit '搜索', class: "btn btn-outline-primary" %>
    <%= link_to "清除条件", operation_products_path, style: "margin-left: 10px;" %>
  </div>
<% end %>