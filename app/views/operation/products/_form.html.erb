<%= stylesheet_link_tag "operation/products" %>
<div id="error_explanation"></div>
<div data-controller="product-form">
  <div class="card container shadow-lg mb-3">
    <div class="card-body">
      <h4>基础信息</h4>
      <div class="row">
        <div class="form-group col-xl-4">
          <label for="tag_id"><span class="validate-error">*</span>商品分类</label>
          <%= f.select :tag_id, options_for_select(Tag.all.collect { |o| [o.name, o.id] }, @product.try(:tag_id)), {}, { class: "form-select", required: true } %>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-11">
          <div class="form-group">
            <label for="number"><span class="validate-error">*</span>商品主图</label>
          </div>
          <div class="form-group">
            <div id="product_images" style="display: flex;flex-flow: wrap;">
              <div style="margin-right: 10px;">
                <%= f.file_field :list_img_url, class: 'product-image-input', style: 'display: none;', placeholder: '活动图' %>
                <% img_url = f.object.list_img_url.present? ? f.object.list_img_url_url : "upload_image.jpg" %>
                <%= image_tag img_url, class: "product-image-pic", style: "margin-bottom: 10px;" %>
                <div class="top-toolbar">
                  <span class="badge text-light image-delete" style="width: 112px;" title="删除">
                    商品白底图
                  </span>
                </div>
              </div>

              <%= f.fields_for :product_images do |product_image| %>
                <%= render "product_image_fields", f: product_image %>
              <% end %>
              <div class="links">
                <%= link_to_add_association '添加主图', f, :product_images %>
              </div>
            </div>
          </div>
        </div>
        <div class="label-tip">
          商品主图大小不能超过3MB，尺寸大小建议为800x800。商品白底图不会在商品详情页展示。
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-4">
          <label for="name" id="product_name_label"><span class="validate-error">*</span>商品标题</label>
          <%= f.text_field :name, class: 'form-control', placeholder: '填写商品标题', required: true %>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-12">
          <label for="is_pre_sale">预售商品</label>
          <%= f.check_box("is_pre_sale", { "data-switch": "success" }, 1, 0) %>
          <label for="product_is_pre_sale" data-on-label="是" data-off-label="否" class="mb-0" style="position: absolute;"></label>
        </div>
        <div class="form-group col-xl-4 pre_sale_stage_id" style="display: <%= f.object.is_pre_sale? ? "block" : "none" %>;">
          <label for="pre_sale_stage">预售阶段</label>
          <%= f.select :pre_sale_stage, [["付定金", :pay_deposit], ["付尾款", :pay_final]], {}, class: 'form-select' %>
        </div>
        <div class="form-group col-xl-4 deposit_id" style="display: <%= f.object.is_pre_sale? ? "block" : "none" %>;">
          <label for="deposit">定金</label>
          <%= f.number_field :deposit, class: 'form-control', placeholder: '定金金额' %>
        </div>
      </div>
    </div>
  </div>
        <!-- <div class="form-group col-xl-3 mb-3">
          <label for="sale_count">公开销量：</label>
          <%= f.check_box("on_sale", { "data-switch": "success" }, 1, 0) %>
          <label for="product_on_sale" data-on-label="是" data-off-label="否" class="mb-0" style="position: absolute;"></label>
        </div> -->

        <!-- <div class="form-group col-xl-3 mb-3">
          <label for="flag_ask">讨论*：</label>
          <%= f.check_box("is_open_comment", { "data-switch": "success" }, 1, 0) %>
          <label for="product_is_open_comment" data-on-label="是" data-off-label="否" class="mb-0" style="position: absolute;"></label>
        </div> -->

        <!-- <div class="form-group col-xl-3 mb-3">
          <label for="product_category">销售量：</label>
          <%= f.text_field :sale_count, class: 'form-control', placeholder: '按钮文案' %>
        </div> -->


  <%= render 'properties_edit' %>

  <div class="card container shadow-lg mb-3">
    <div class="card-body">
      <h4 class="">导购素材</h4>
      <div class="row mt-2">
        <div class="form-group col-xl-2 mb-2">
          <!--售前分享图 -->
          <label for="before_image"><span class="validate-error">*</span>售前分享图</label><br/>
          <%= f.file_field :before_image, class: 'product-image-input', style: 'display:none;', placeholder: '活动图' %>
          <% img_url = f.object.before_image.present? ? f.object.before_image_url : "upload_image_34.jpg" %>
          <%= image_tag img_url, class: 'product-image-pic share-img' %>
        </div>

        <div class="form-group col-xl-2 mb-2">
          <!--售后分享图 -->
          <label for="aftermarket"><span class="validate-error">*</span>售后分享图</label><br/>
          <%= f.file_field :aftermarket, class: 'product-image-input', style: 'display:none;', placeholder: '活动图' %>
          <% img_url = f.object.aftermarket.present? ? f.object.aftermarket_url : "upload_image_34.jpg" %>
          <%= image_tag img_url, class: 'product-image-pic share-img' %>
        </div>
      </div>
    </div>
  </div>
  <div class="card container shadow-lg mb-3">
    <div class="card-body">
      <h4>商品详情</h4>
      <label for="detail"><span class="validate-error">*</span>描述</label>
      <%= f.text_area :detail, id: "ckeditor" %>
    </div>
  </div>

  <div class="card container shadow-lg mb-3">
    <div class="card-body">
      <h4 class="">更多设置</h4>
      <div class="row">
        <div class="form-group col-xl-10">
          <div class="form-group col-xl-12">
            <label for="flag_ask">讨论入口</label>
            <%= f.check_box("is_comment", { "data-switch": "success" }, 1, 0) %>
            <label for="product_is_comment" data-on-label="开启" data-off-label="关闭" class="mb-0" style="position: absolute;"></label>
          </div>
          <div class="form-group col-xl-12">
            <label for="sale_count">是否展示</label>
            <%= f.check_box("on_shelf", { "data-switch": "success" }, 1, 0) %>
            <label for="product_on_shelf" data-on-label="是" data-off-label="否" class="mb-0" style="position: absolute;"></label>
          </div>
          <div class="form-group col-xl-12">
            <label for="sale_count">是否在售</label>
            <%= f.check_box("is_sale", { "data-switch": "success" }, 1, 0) %>
            <label for="product_is_sale" data-on-label="是" data-off-label="否" class="mb-0" style="position: absolute;"></label>
          </div>
          <div class="form-group col-xl-4">
            <label for="sale_count">定时开售</label>
            <%= f.check_box("is_sale_at_button", { "data-switch": "success" }, 1, 0) %>
            <label for="product_is_sale_at_button" data-on-label="是" data-off-label="否" class="mb-0" style="position: absolute;"></label>
            <%= f.datetime_local_field :is_sale_at, class: "form-control product_is_sale_at", style:"display: #{f.object.is_sale_at_button ? 'block' : 'none'}" %>
          </div>
          <div class="form-group col-xl-4">
            <label for="button_txt">立即购买按钮文案</label>
            <%= f.text_field :button_txt, class: 'form-control', placeholder: '按钮文案' %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card container shadow-lg mb-3">
    <div class="product-footer container">
      <div>
        <% unless params[:action].in?(['new', 'create']) %>
          <%#= link_to '删除', operation_product_path(f.object), method: :delete, data: { confirm: "请确认是否删除" }, class: 'btn btn-light', style: 'width: 120px;margin-right:20px;' %>
        <% end %>
        <button class="btn btn-primary" style="width: 120px;" id="button_id">保存</button>
      </div>
    </div>
  </div>
</div>