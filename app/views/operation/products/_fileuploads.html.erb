<style>
    .main {
        width: 100%;
        margin: auto;
        padding-bottom: 20px;
    }

    * {
        padding: 0;
        margin: 0;
    }

    .btn {
        padding: 9px 18px;
        background: #40AFFE;
        color: #FFFFFF;
        border-radius: 5px;
    }

    .upimg {
        position: relative;
        width: 100px;
        height: 100px;
        border-radius: 5px;
        border: dashed #999999;
        background: url(https://img.iqunix.com/img/addimg.svg) no-repeat;
        background-position: 33px;
    }

    .upimg input {
        position: absolute;
        width: 100px;
        height: 100px;
        opacity: 0;
    }

    #showui {
        /*display: flex;*/
        justify-content: flex-start;
    }

    #showui li {
        width: 150px;
        height: 150px;
        position: relative;
        overflow: hidden;
        display: inline-block;
        margin-right: 5px;
    }

    #showui li img.showimg {
        position: absolute;
        text-align: center;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 6;
        width: 100%;
    }

    .showdiv {
        position: absolute;
        z-index: 9;
        bottom: 0;
        width: calc(100% - 20px);
        padding: 10px;
        display: flex;
        width: 100%;
        justify-content: space-around;
        background: rgba(0, 0, 0, .6);
    }

    .showdiv img {
        width: 20px;
        height: 20px;
        cursor: pointer;
    }

    #showui dl, ol, ul {
        padding: 0px;
    }

    #showui li:first-child img.left {
        opacity: .6;
        cursor: no-drop;
    }

    #showui li:last-child img.right {
        opacity: .6;
        cursor: no-drop;
    }

    .oneright {
        opacity: .6;
        cursor: no-drop !important;
    }
</style>
<!-- <div class="main "> -->
<div class="upimg mb-1">
  <input type="file" name="product[product_images][]" id="upgteimg" value="" multiple/>
</div>
<div id="showimg" class="mb-1">
  <ul id="showui">
    <% photos = @product.product_images %>
    <% (photos || []).each do |photo| %>
      <li>
        <div class="showdiv">
          <img class="left" src="https://img.iqunix.com/img/Arrow_left.svg">
          <img class="center" src="https://img.iqunix.com/img/delete.svg">
          <img class="right" src="https://img.iqunix.com/img/Arrow_right.svg">
        </div>
        <img id="img0b4Yymhwc" class="showimg" src="<%= photo.picture %>">
      </li>
    <% end %>
  </ul>

  <div id="showinput">
  </div>
</div>
