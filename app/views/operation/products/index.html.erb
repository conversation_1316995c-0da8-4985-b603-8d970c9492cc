<style>
    .pagination li {
      display: block !important;
      padding: 0px !important;
      border: 0px !important;
    }

    .pagination .page-item {
      height: 40px !important;
    }
    .sort_link {
      color: #202033;
    }
    .sort_link:hover {
      color: #202033;
    }
    .edit {
      margin-left: 8px;
      cursor: pointer;
    }

    .loading {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 9999;
    }
</style>
<div class="container-fluid">
  <!-- <div class="loading">
    <div class="spinner-border" role="status"></div>
  </div> -->

  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">商品管理</li>
          </ol>
        </div>
        <h4 class="page-title">商品管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-md-12">
              <%= render 'search' %>
            </div>
          </div>
          <div class="row mb-2">
            <div class="col-sm-4">
              <a href="/operation/products/new" class="btn btn-primary mb-2">发布商品</a>
            </div>
            <div class="col-sm-8">
            </div><!-- end col-->
          </div>
          <div class="products">
            <%= render 'index' %>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>
  <!-- end row -->
</div>

<script type="text/javascript">
    $(".goods_show_status").click(function () {
        var id = $(this).attr("id");
        var id_number = id.split("show_status")
        change_show_status(id_number[1]);
    });

    $(".goods_status").click(function () {
        var id = $(this).attr("id");
        var id_number = id.split("status")
        change_product_status(id_number[1]);
    });

    function change_product_status(id) {
        $.ajax({
            url: "/operation/products/" + id + "/product_status",
            method: 'post',
            success: function (msg) {
                if (msg["status"] == 500) {
                  message("操作失败");
                }
                if (msg["status"] == 200) {
                  message("操作成功");
                }

            }
        })
    };


    function change_show_status(id) {
        $.ajax({
            url: "/operation/products/" + id + "/show_status",
            method: 'post',
            success: function (msg) {
                if (msg["status"] == 500) {
                  message("操作失败");
                }
                if (msg["status"] == 200) {
                  message("操作成功");
                }

            }
        })
    }
</script>