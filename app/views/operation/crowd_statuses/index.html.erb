<style>
    .status-text {
        margin-left: 42px;
        display: block;
        margin-top: 8px;
    }

    .actions {
        padding-top: 20px !important;
        font-size: 18px;
    }
</style>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">设置</a></li>
            <li class="breadcrumb-item active">众筹状态管理</li>
          </ol>
        </div>
        <h4 class="page-title">众筹状态管理</h4>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-body">
      <%= link_to "新增状态", new_operation_crowd_status_path, class: "btn btn-primary mb-2", "data-bs-toggle": "modal", "data-bs-target": "#create-modal" %>
      <table class="table">
        <thead class="table-light">
        <tr>
          <th>状态</th>
          <th>图标</th>
          <th>操作</th>
        </tr>
        </thead>
        <tbody id="sortable-table">
        <% @crowd_statuses.each do |crowd_status| %>
          <tr class="crowd-status-item" crowd-status="<%= crowd_status.id %>">
            <td><span class="dragula-handle"></span><span class="status-text"><%= crowd_status.name %></span></td>
            <td><%= image_tag crowd_status.icon_url, size: "40x40" %></td>
            <td class="actions">
              <%= link_to "#", class: "action-icon", "data-bs-toggle": "modal", "data-bs-target": "#edit-modal-#{crowd_status.id}" do %>
                <i class="mdi mdi-square-edit-outline" title="编辑"></i>
              <% end %>
              <%= link_to operation_crowd_status_path(crowd_status), method: :delete, data: { confirm: "确定删除吗？" }, class: "action-icon" do %>
                <i class="uil-trash-alt" title="删除"></i>
              <% end %>
            </td>
          </tr>
        <% end %>
        </tbody>
      </table>
      <div style="display: flex;">
        <div>
          <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
        </div>
        <div style="flex: 1;text-align: right;">
          <% if @pagy.count <= 10 %>
            共 <b><%= @pagy.count %></b> 项
          <% else %>
            <%== pagy_info(@pagy) %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<%= render "form", crowd_status: CrowdStatus.new, title: "新增状态", id: "create-modal" %>

<% @crowd_statuses.each do |crowd_status| %>
  <%= render "form", crowd_status: crowd_status, title: "编辑状态", id: "edit-modal-#{crowd_status.id}" %>
<% end %>

<script>
    container = "sortable-table";
    dragula([document.getElementById(container)],
        {
            moves: function (el, container, handle) {
                return handle.classList.contains('dragula-handle');
            }
        }
    ).on('dragend', function () {
        $.ajax({
            url: "/operation/crowd_statuses/update_que",
            type: "patch",
            data: {
                sort: get_new_sort()
            }
        });
        message("排序已更新");
    });

    function get_new_sort() {
        let sort = [];
        let i = 1;
        $('.crowd-status-item').each(function () {
            sort.push({
                id: $(this).attr("crowd-status"),
                que: i
            });
            i++;
        });
        console.log(sort);
        return sort;
    }
</script>
