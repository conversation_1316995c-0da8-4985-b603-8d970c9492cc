<div id="<%= id %>" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="<%= id %>Label" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="<%= id %>Label"><%= title %></h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
      </div>
      <%= form_with(model: [:operation, crowd_status]) do |form| %>
        <div class="modal-body">
          <div>
            <label for="name"><span class="validate-error">*</span>名称</label>
            <%= form.text_field :name, class: "form-control", required: true %>
          </div>

          <div class="mt-2">
            <label for="icon" style="display:block"><span class="validate-error">*</span>角标</label>
            <%= form.file_field :icon, class: 'file-input', style: 'display:none;' %>
            <% img_url = form.object.icon.present? ? form.object.icon_url : "upload_image.jpg" %>
            <%= image_tag img_url, class: 'file-input-replace', onclick: "$(this).prev().click();", size: "60x60" %>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">保存</button>
        </div>
      <% end %>
    </div>
  </div>
</div>
