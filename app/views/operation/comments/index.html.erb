<style>
  .uil-ellipsis-h, .personal-info {
    cursor: pointer;
  }
</style>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">商品管理</a></li>
            <li class="breadcrumb-item active">商品讨论管理</li>
          </ol>
        </div>
        <h4 class="page-title"><%= @product.name %></h4>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="card-body">
      <div class="container" style="max-width: 600px">
        <div class="d-flex">
          <h4 class="my-1">讨论列表</h4>

          <div class="" style="margin-left: 0px;">
            <%= form_with url: operation_product_comments_path(@product), id: "comments-form", method: :get do |f| %>
              <div class="d-flex">
                <div class="form-check form-check-inline" style="width: 140px;margin-top: 6px;margin-left: 110px">
                  <%= check_box_tag :show_oneself_visible, params[:show_oneself_visible], params[:show_oneself_visible], class: "form-check-input" %>
                  <label class="form-check-label" for="show_oneself_visible">显示仅发布者可见</label>
                </div>
                <div class="form-check form-check-inline" style="width: 120px;margin-top: 6px">
                  <%= check_box_tag :show_ban, params[:show_ban], params[:show_ban], class: "form-check-input" %>
                  <label class="form-check-label" for="show_ban">显示已屏蔽</label>
                </div>
                <%= select_tag :sort, options_for_select([["按热度", "hot"], ["按时间", "time"], ["有图", "photos"]], params[:sort] || "hot"), class: "form-select form-select-sm comments-sort", style: "width: 100px;" %>
              </div>
              <%= f.submit "搜索", id: "comments-submit", style: "display: none;" %>
            <% end %>
          </div>
        </div>
        <hr>

        <% @comments.each do |comment| %>
          <% next if comment.is_owner && params[:show_oneself_visible].blank? %>
          <% next if !comment.status && params[:show_ban].blank? %>
          <div class="d-flex mt-3">
            <%= link_to operation_users_path(seach_id: comment.user.id), target: "_blank" do %>
              <%= image_tag comment.try(:user).avatar || 'default_user.jpg', class: "me-2 rounded-circle personal-info", size: "32x32" %>
            <% end %>
            <div class="w-100">
              <h5 class="mt-0">
                <%= link_to operation_users_path(seach_id: comment.user.id), target: "_blank", class: "link-secondary" do %>
                  <%= comment.user.user_name %>
                <% end %>
                <small class="text-muted float-end d-flex">
                  <div class="dropdown" style="margin-right: 5px;">
                    <i class="uil-ellipsis-h " id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></i>
                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                      <%= link_to "正常显示", operation_product_comment_path(@product, comment, status: "normal"), method: :patch, class: "dropdown-item #{ 'active' if !comment.is_owner && comment.status}" %>
                      <%= link_to "仅发布者可见", operation_product_comment_path(@product, comment, status: "visible_oneself"), method: :patch, class: "dropdown-item #{ 'active' if comment.is_owner && comment.status}" %>
                      <%= link_to "屏蔽", operation_product_comment_path(@product, comment, status: "ban"), method: :patch, class: "dropdown-item #{ 'active' if !comment.status}" %>
                    </div>
                  </div>
                  <div>
                    <i class="uil-thumbs-up"></i> <%= comment.try(:userlikes_count) %>
                  </div>
                </small>
              </h5>
              <%= comment.try(:content) %>
              <% if comment.asset_imgs_count > 0 %>
                <br>
                <% comment.asset_imgs.each do |asset_img| %>
                  <%= image_tag asset_img.image_url, class: "img-preview", style: "width: 50px;height: 50px;" %>
                <% end %>
              <% end %>
              <br>
              <small class="text-muted" title='<%= comment.created_at.strftime("%Y-%m-%d %H:%M:%S") %>'><%= time_ago_in_words comment.created_at %></small>
              <% if comment.comments.present? %>
                <br/>
                <div class="card mb-md-0 mb-3 shadow-none bg-light-lighten">
                  <div class="card-body">
                    <% comment.comments.each_with_index do |child_comment, index| %>
                      <% next if child_comment.is_owner && params[:show_oneself_visible].blank? %>
                      <% next if !child_comment.status && params[:show_ban].blank? %>
                      <% if index != 0 %>
                        <br>
                      <% end %>
                      <div class="d-flex">
                        <%= link_to operation_users_path(seach_id: child_comment.user.id), target: "_blank" do %>
                          <%= image_tag child_comment.try(:user).avatar_file_url || 'default_user.jpg', class: "me-2 rounded-circle personal-info", size: "32x32" %>
                        <% end %>
                        <div class="w-100">
                          <h5 class="mt-0">
                            <%= link_to operation_users_path(seach_id: child_comment.user.id), target: "_blank", class: "link-secondary" do %>
                              <%= child_comment.user.user_name %>
                            <% end %>
                            <small class="text-muted float-end d-flex">
                              <div class="dropdown" style="margin-right: 5px;">
                                <i class="uil-ellipsis-h " id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></i>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                  <%= link_to "正常显示", operation_product_comment_path(@product, child_comment, status: "normal"), method: :patch, class: "dropdown-item #{ 'active' if !child_comment.is_owner && child_comment.status}" %>
                                  <%= link_to "仅发布者可见", operation_product_comment_path(@product, child_comment, status: "visible_oneself"), method: :patch, class: "dropdown-item #{ 'active' if child_comment.is_owner && child_comment.status}" %>
                                  <%= link_to "屏蔽", operation_product_comment_path(@product, child_comment, status: "ban"), method: :patch, class: "dropdown-item #{ 'active' if !child_comment.status}" %>
                                </div>
                              </div>
                              <div>
                                <i class="uil-thumbs-up"></i> <%= child_comment.try(:userlikes_count) %>
                              </div>
                            </small>
                          </h5>
                          <div class="mb-1">
                            <%= child_comment.try(:content) %>
                            <br>
                            <small class="text-muted" title='<%= child_comment.created_at.strftime("%Y-%m-%d %H:%M:%S") %>'><%= time_ago_in_words child_comment.created_at %></small>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    $('.comments-sort').change(function() {
      $('#comments-submit').click();
    });
    $("#show_ban").change(function() {
      $('#comments-submit').click();
    });
    $("#show_oneself_visible").change(function() {
      $('#comments-submit').click();
    });
  });
</script>