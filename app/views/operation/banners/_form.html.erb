<style>
  .ck-editor__editable_inline {
    height: 600px;
  }
  .ck-editor {
    width: 400px !important;
  }
</style>
<div class="card">
  <div class="card-body">
    <div class="row" data-controller="banner-form">
      <div id="error_explanation"></div>
      <div>
        <label for="title"><span class="validate-error">*</span>广告标题</label>
        <%= f.text_field :title, class: "form-control", style: "max-width: 400px", required: true %>
      </div>

      <div class="mt-2">
        <label for="image" style="display:block"><span class="validate-error">*</span>广告图</label>
        <%= f.file_field :image, class: 'file-input', style: 'display:none;' %>
        <% img_url = f.object.image.present? ? f.object.image_url : "upload_image_34.jpg" %>
        <%= image_tag img_url, class: 'file-input-replace', onclick: "$(this).prev().click();", size: "180x240" %>
      </div>

      <div class="form-group mt-2">
        <label for="banner_type"><span class="validate-error">*</span>广告类型</label>
        <%= f.select :banner_type, [["关联众筹", :is_crowd], ["关联商品", :is_product], ["关联地址", :is_url], ["图文内容", :is_content]], {}, class: 'form-select', style: "max-width: 400px", required: true %>
      </div>

      <div class="form-group mt-2 banner_picture_url" style="display: <%= f.object.is_url? ? "block" : "none" %>;">
        <label for="picture_url"><span class="validate-error">*</span>跳转地址</label>
        <%= f.text_field :picture_url, class: 'form-control', placeholder: '跳转地址', style: "max-width: 400px" %>
      </div>

      <div class="form-group mt-2 banner_product_id" style="display: <%= f.object.is_product? ? "block" : "none" %>;">
        <label for="product_id"><span class="validate-error">*</span>商品id</label>
        <%= f.select :product_id, options_for_select(Product.all.collect{|o| [o.name,o.id]}, f.object.product_id), {}, class: "form-select", style: "max-width: 400px" %>
      </div>

      <div class="form-group mt-2 banner_crowd_id" style="display: <%= f.object.is_crowd? ? "block" : "none" %>;">
        <label for="crowd_id"><span class="validate-error">*</span>众筹活动id</label>
        <%= f.text_field :crowd_id, class: 'form-control', placeholder: '众筹活动id', style: "max-width: 400px" %>
      </div>

      <div class="form-group mt-2 banner_content" style="display: <%= f.object.is_content? ? "block" : "none" %>;">
        <label for="crowd_id"><span class="validate-error">*</span>图文内容</label>
        <%= f.text_area :content, class: 'form-control', id: "ckeditor", placeholder: '图文内容', style: "max-width: 400px" %>
      </div>

      <div class="form-group mt-2">
        <label for="flag_ask">是否展示</label>
        <div>
          <%= f.check_box("is_closing", { "data-switch": "success", checked: !f.object.is_closing? }, 0, 1) %>
          <label for="banner_is_closing" data-on-label="是" data-off-label="否" class="mb-0 d-block"></label>
        </div>
      </div>
    </div>
    <div class="mt-2">
      <button type="submit" class="btn btn-primary">保存</button>
    </div>
  </div>
</div>