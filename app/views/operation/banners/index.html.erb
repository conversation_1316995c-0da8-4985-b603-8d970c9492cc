<style>

    .sort_link {
        color: #202033;
    }
    .sort_link:hover {
        color: #202033;
    }
    .edit {
        margin-left: 8px;
        cursor: pointer;
    }
</style>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">首页管理</li>
          </ol>
        </div>
        <h4 class="page-title">广告列表</h4>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-4">
              <a href="/operation/banners/new" class="btn btn-primary mb-2">新增广告</a>
            </div>
            <div class="col-sm-8">
            </div><!-- end col-->
          </div>
          <div class="table-responsive" data-controller="banner">
            <table class="table table-centered w-100 dt-responsive nowrap" id="products-datatable">
              <thead class="table-light">
              <tr>
                <th>广告图片</th>
                <th>广告标题</th>
                <th>位置</th>
                <th>是否展示</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
              </thead>
              <tbody>
              <% @banners.each do |banner| %>
                <tr>
                  <td>
                    <img src="<%= banner.image %>" alt="contact-img" title="contact-img" class="rounded me-1" height="48"/>
                  </td>
                  <td>
                    <%= banner.try(:title) %>
                  </td>
                  <td>
                    <%= banner.que %><span class="edit" data-bs-toggle="modal" data-bs-target="#edit-modal<%= banner.id %>"><i class="ri-edit-2-line"></i></span>
                  </td>
                  <td>
                    <div style="margin-top: 12px;">
                      <% if policy(banner).update? %>
                        <input type="checkbox" class="banner_show_status"  <%= !banner.try(:is_closing) ? "checked=''" : "" %>  id="<%= 'show_status' + banner.id.to_s %>" data-switch="success">
                        <label for="<%= 'show_status' + banner.id.to_s %>" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;"></label>
                      <% else %>
                        <input type="checkbox" class="banner_show_status" <%= !banner.try(:is_closing) ? "checked=''" : "" %>  id="<%= 'show_status' + banner.id.to_s %>" data-switch="success" disabled>
                        <label for="<%= 'show_status' + banner.id.to_s %>" title="无权限" data-on-label="Yes" data-off-label="No" style="margin-bottom: 0px;"></label>
                      <% end %>
                    </div>
                  </td>
                  <td>
                    <%= banner.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
                  </td>
                  <td class="table-action">
                    <a href="<%= edit_operation_banner_path(banner.id) %>" class="action-icon">
                      <i class="mdi mdi-square-edit-outline"></i></a>
                  </td>
                </tr>
              <% end %>
              </tbody>
            </table>
          </div>

          <% @banners.each do |banner| %>
            <div id="edit-modal<%= banner.id %>" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="standard-modalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h4 class="modal-title" id="standard-modalLabel"><%= banner.title %></h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                  </div>
                  <%= form_with(model: [:operation, banner], url: update_que_operation_banner_path(banner)) do |f| %>
                    <div class="modal-body">
                      <label for="que">位置</label>
                      <%= f.number_field :que, class: 'form-control col-xl-3', placeholder: "输入商品权重", required: true %>
                      <input type="hidden" name="origin_url" value="<%= request.url %>">
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                      <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                  <% end %>
                </div><!-- /.modal-content -->
              </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->
          <% end %>

          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @pagy.count <= 10 %>
                共 <b><%= @pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@pagy) %>
              <% end %>
            </div>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>
</div>
