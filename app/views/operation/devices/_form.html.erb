<%= form_for([:operation, device]) do |f| %>
  <% if device.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(device.errors.count, "error") %> prohibited this device from being saved:</h4>
      <ul>
        <% device.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="row">
    <div class="col-md-6">
      <div class="field mb-3">
        <span style="color:red">*</span> <%= f.label :name, "设备名称" %><br>
        <%= f.text_field :name, class: 'form-control', required: true, placeholder: "请输入设备名称，如：EZ80" %>
      </div>
      
      <div class="field mb-3">
        <span style="color:red">*</span> <%= f.label :pid, "PID" %><br>
        <%= f.text_field :pid, class: 'form-control', required: true, placeholder: "请输入设备PID，如：9010" %>
        <small class="form-text text-muted">设备的产品标识符</small>
      </div>
      
      <div class="field mb-3">
        <%= f.label :dfu_pid, "DFU PID" %><br>
        <%= f.text_field :dfu_pid, class: 'form-control', placeholder: "请输入DFU模式下的PID（可选）" %>
        <small class="form-text text-muted">设备在DFU模式下的产品标识符</small>
      </div>
      
      <div class="actions mt-3">
        <%= f.submit device.persisted? ? "更新设备" : "创建设备", class: "btn btn-primary", style: "margin-right: 6px;" %>
        <%= link_to '取消', operation_devices_path, class: "btn btn-secondary" %>
      </div>
    </div>
  </div>
<% end %>
