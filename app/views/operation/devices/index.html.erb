<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item active">键盘型号管理</li>
          </ol>
        </div>
        <h4 class="page-title">键盘型号管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-2">
              <%= link_to "新增设备", new_operation_device_path, class: "btn btn-primary" %>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table table-centered table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>ID</th>
                  <th>设备名称</th>
                  <th>PID</th>
                  <th>DFU PID</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @devices.each do |device| %>
                  <tr>
                    <td><%= device.id %></td>
                    <td><%= device.name %></td>
                    <td><%= device.pid %></td>
                    <td><%= device.dfu_pid %></td>
                    <td><%= device.created_at.strftime("%Y-%m-%d %H:%M") %></td>
                    <td>
                      <%= link_to "编辑", edit_operation_device_path(device), class: "btn btn-sm btn-outline-primary" %>
                      <%= link_to "删除", operation_device_path(device), method: :delete,
                          class: "btn btn-sm btn-outline-danger",
                          data: { confirm: '确定要删除这个设备吗？' },
                          style: "margin-left: 5px;" %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          <% if @pagy %>
            <div class="mt-3">
              <%== pagy_bootstrap_nav(@pagy) %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>