<style>
  select{
    outline: none;
  }
</style>
<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">订单管理</li>
          </ol>
        </div>
        <h4 class="page-title">订单管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <br class="col-12">
            <%= search_form_for [:operation, @q], html: {class: "row gx-2 align-items-center"} do |f| %>
              <div class="col-auto mb-2">
                <div class="input-group flex-nowrap">
                  <%= select_tag :search_type, options_for_select(["全文搜索", "订单号", "商品名", "买家", "收货信息", "Ping++流水号"], params[:search_type]), class: "input-group-text" %>
                  <%= f.text_field :text_conment_or_user_username_or_order_details_product_name_or_payments_trade_no_cont, type: "search", placeholder: "订单号/商品名/买家/收货信息", class: "form-control search-text" %>
                  <%= f.text_field :number_cont, type: "search", placeholder: "订单号", class: "form-control search-text" %>
                  <%= f.text_field :order_details_product_name_cont, type: "search", placeholder: "商品名", class: "form-control search-text" %>
                  <%= f.text_field :user_username_cont, type: "search", placeholder: "买家", class: "form-control search-text" %>
                  <%= f.text_field :receive_name_or_receive_phone_or_receive_address_cont, type: "search", placeholder: "收货信息", class: "form-control search-text" %>
                  <%= f.text_field :payments_trade_no_cont, type: "search", placeholder: "Ping++流水号", class: "form-control search-text" %>
                </div>
              </div>
              <div class="col-auto mb-2">
                <div class="input-group flex-nowrap">
                  <span class="input-group-text">创建时间</span>
                  <%= f.text_field :created_at_gteq, class: "form-control datetime", type: 'search', placeholder: "起始时间", autocomplete: 'off', value: params[:q] ? params[:q][:created_at_gteq].to_s : nil %>
                  <span style="margin: 8px 5px 0px 5px;">-</span>
                  <%= f.text_field :created_at_lteq, class: "form-control datetime", type: 'search', placeholder: "结束时间", autocomplete: 'off', value: params[:q] ? params[:q][:created_at_lteq].to_s : nil %>
                </div>
              </div>
              <div class="col-auto mb-2">
                <div class="input-group flex-nowrap">
                  <span class="input-group-text">支付时间</span>
                  <%= f.text_field :pay_time_gteq, class: "form-control datetime", type: 'search', placeholder: "起始时间", autocomplete: 'off', value: params[:q] ? params[:q][:pay_time_gteq].to_s : nil %>
                  <span style="margin: 8px 5px 0px 5px;">-</span>
                  <%= f.text_field :pay_time_lteq, class: "form-control datetime", type: 'search', placeholder: "结束时间", autocomplete: 'off', value: params[:q] ? params[:q][:pay_time_lteq].to_s : nil %>
                </div>
              </div>
              <%= f.hidden_field :status_eq, value: params[:q] ? params[:q][:status_eq] : nil %>
              <div class="col-auto mb-2">
                <div class="me-sm-2">
                <%= f.submit '搜索订单', class: "btn btn-primary btn-rounded" %>
                <button type="button" class="btn btn-secondary" style="margin-left: 10px" id="batch-export">批量导出</button>
                <%= link_to "清除条件", operation_orders_path, style: "margin-left: 10px;" %>
              </div>
            <% end %>

            <%= form_for @q, url: operation_orders_path(format: :xlsx), method: :get do |f| %>
              <div class="form-inputs">
                <%= f.hidden_field :text_conment_or_user_username_or_order_details_product_name_or_payments_trade_no_cont, value: params[:q] ? params[:q][:text_conment_or_user_username_or_order_details_product_name_or_payments_trade_no_cont] : nil %>
                <%= f.hidden_field :number_cont, value: params[:q] ? params[:q][:number_cont].to_s : nil %>
                <%= f.hidden_field :order_details_product_name_cont, value: params[:q] ? params[:q][:order_details_product_name_cont].to_s : nil %>
                <%= f.hidden_field :user_username_cont, value: params[:q] ? params[:q][:user_username_cont].to_s : nil %>
                <%= f.hidden_field :receive_name_or_receive_phone_or_receive_address_cont, value: params[:q] ? params[:q][:receive_name_or_receive_phone_or_receive_address_cont].to_s : nil %>
                <%= f.hidden_field :payments_trade_no_cont, value: params[:q] ? params[:q][:payments_trade_no_cont].to_s : nil %>
                <%= f.hidden_field :status_eq, value: params[:q] ? params[:q][:status_eq] : nil %>
                <%= f.hidden_field :created_at_gteq, class: "form-control datetime", placeholder: "起始时间", autocomplete: 'off', value: params[:q] ? params[:q][:created_at_gteq].to_s : nil %>
                <%= f.hidden_field :created_at_lteq, class: "form-control datetime", placeholder: "结束时间", autocomplete: 'off', value: params[:q] ? params[:q][:created_at_lteq].to_s : nil %>
                <%= f.hidden_field :pay_time_gteq, class: "form-control datetime", placeholder: "起始时间", autocomplete: 'off', value: params[:q] ? params[:q][:pay_time_gteq].to_s : nil %>
                <%= f.hidden_field :pay_time_lteq, class: "form-control datetime", placeholder: "结束时间", autocomplete: 'off', value: params[:q] ? params[:q][:pay_time_lteq].to_s : nil %>
              </div>

              <div class="form-actions">
                <%= f.submit "批量导出", class: 'btn btn-light batch-export-origin', style: "display: none" %>
              </div>
            <% end %>
          </div>
          <div class="col-12 mt-3">
            <ul class="nav nav-tabs nav-bordered mb-3">
              <% ([[nil, "全部"]] + Order::OPERATION_STATUS.to_a).each do |value, status_cn| %>
                <li class="nav-item">
                  <% if status_cn == "等待发货" %>
                    <a href="<%= filter_url %>&q[status_eq]=<%= value %>" aria-expanded="false" class="nav-link <%= params[:q]&.dig(:status_eq).to_s == value.to_s ? 'active' : '' %>">
                      <span class="d-none d-md-block"><%= status_cn %>
                        <% awaiting_delivery_count = @orders_without_status.where(status: 1).count %>
                        <% if awaiting_delivery_count > 0 %>
                          <span class="badge bg-success"><%= awaiting_delivery_count %></span>
                        <% end %>
                      </span>
                    </a>
                  <% elsif status_cn == "发货前退款" %>
                    <a href="<%= filter_url %>&q[status_eq]=<%= value %>" aria-expanded="false" class="nav-link <%= params[:q]&.dig(:status_eq).to_s == value.to_s ? 'active' : '' %>">
                      <span class="d-none d-md-block"><%= status_cn %>
                        <% before_delivery_refund_count = @orders_without_status.where(status: 11).count %>
                        <% if before_delivery_refund_count > 0 %>
                          <span class="badge bg-success"><%= before_delivery_refund_count %></span>
                        <% end %>
                      </span>
                    </a>
                  <% elsif status_cn == "发货后退款" %>
                    <a href="<%= filter_url %>&q[status_eq]=<%= value %>" aria-expanded="false" class="nav-link <%= params[:q]&.dig(:status_eq).to_s == value.to_s ? 'active' : '' %>">
                      <span class="d-none d-md-block"><%= status_cn %>
                        <% after_delivery_refund_count = @orders_without_status.where(status: 21).count %>
                        <% if after_delivery_refund_count > 0 %>
                          <span class="badge bg-success"><%= after_delivery_refund_count %></span>
                        <% end %>
                      </span>
                    </a>
                  <% elsif status_cn == "已付定金" %>
                    <a href="<%= filter_url %>&q[status_eq]=<%= value %>" aria-expanded="false" class="nav-link <%= params[:q]&.dig(:status_eq).to_s == value.to_s ? 'active' : '' %>">
                      <span class="d-none d-md-block"><%= status_cn %>
                        <% pay_deposit_count = @orders_without_status.where(status: 2000).count %>
                        <% if pay_deposit_count > 0 %>
                          <span class="badge bg-success"><%= pay_deposit_count %></span>
                        <% end %>
                      </span>
                    </a>
                  <% else %>
                    <a href="<%= filter_url %>&q[status_eq]=<%= value %>" aria-expanded="false" class="nav-link <%= params[:q]&.dig(:status_eq).to_s == value.to_s ? 'active' : '' %>">
                      <span class="d-none d-md-block"><%= status_cn %></span>
                    </a>
                  <% end %>
                </li>
              <% end %>
            </ul>
          </div>
          <div class="col-12">
            <table class="table table-centered mb-0">
              <thead class="table-light">
                <tr>
                  <th>订单号</th>
                  <th>商品</th>
                  <th>商品sku</th>
                  <th>售价</th>
                  <th>数量</th>
                  <th>买家</th>
                  <th>创建时间</th>
                  <th>交易状态</th>
                  <th>实收款</th>
                  <th>备注</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody data-controller="order-index">
                <% (@orders || []).each do |order| %>
                  <tr>
                    <td>
                      <%= order.number %>
                    </td>
                    <td>
                      <%= order.try(:order_details).first.try(:product_name) %>
                    </td>
                    <td>
                      <%= JSON.parse(order.order_details.map(&:property_name).first).join('-') rescue '无' %>
                    <td>
                      <%= order.try(:order_details).first.try(:skuprice) %><small class="text-muted">元</small>
                    </td>
                    <td>
                      <%= order.try(:count) %>
                    </td>
                    <td>
                      <%= link_to order.user.user_name, operation_users_path(seach_id: order.user_id), target: "_blank" %>
                    </td>
                    <td>
                      <%= order.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
                    </td>
                    <td>
                      <%= order.status_cn %>
                      <% if order.status.in?([11, 21]) && order.refund_id.present? %>
                        <span style="color:red">(退款失败)</span> <i class="mdi mdi-help-circle-outline" data-bs-toggle="tooltip" data-bs-html="true" data-bs-title="<p><%= order.refund_id %></p>"></i>
                      <% end %>
                    </td>
                    <td>
                      <%= order.try(:amount) %><small class="text-muted">元</small>
                    </td>
                    <td>
                      <%= order.try(:user_node) %>
                    </td>
                    <td>
                      <a href="<%= operation_order_path(order.id) %>" target="_blank" class="action-icon">
                        <p class="m-0 d-inline-block align-middle font-12">查看</p>
                      </a>

                      <a href="###" class="action-icon node_js" id="node_js_<%= order.id %>">
                        <p class="m-0 d-inline-block align-middle font-12">备注</p>
                      </a>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          <br>
          <div style="display: flex;">
            <div>
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
            <div style="flex: 1;text-align: right;">
              <% if @pagy.count <= 10 %>
                共 <b><%= @pagy.count %></b> 项
              <% else %>
                <%== pagy_info(@pagy) %>
              <% end %>
            </div>
          </div>
        </div> <!-- end card-body-->
      </div> <!-- end card-->
    </div> <!-- end col -->
  </div>
  <!-- end row -->
</div>
<script type="text/javascript">
    $('.node_js').click(function () {
        var id_string = this.id;
        var arry_ids = id_string.split('_');
        var id_s = arry_ids[2];
        var str = prompt("请输入后台备注");
        if (str) {
            // 修改后端备注
            $.ajax({
                url: "/orders/" + id_s + "/create_update",
                type: 'post',
                data: {admin_node: str},
                cache: false,
                success: function (msg) {
                    if (msg) {//根据bai返回值进行跳转
                      message("订单备注成功");
                    }
                }
            });

        }

    })

    $("#batch-export").click(function () {
      message("正在导出，请稍后");
      $(".batch-export-origin").click();
    })

    lay('.datetime').each(function(){
      laydate.render({
        elem: this,
        type: 'datetime',
        trigger: 'click',
        theme: '#1677ff'
      });
    });

    search_type();
    $("#search_type").change(function(){
      $(".search-text").val('');
      search_type()
    })

    function search_type() {
      $(".search-text").hide();
      if ($("#search_type").val() == "全文搜索") {
        $("[name='q[text_conment_or_user_username_or_order_details_product_name_or_payments_trade_no_cont]']").show();
      } else if ($("#search_type").val() == "订单号") {
        $("[name='q[number_cont]']").show();
      } else if ($("#search_type").val() == "商品名") {
        $("[name='q[order_details_product_name_cont]']").show();
      } else if ($("#search_type").val() == "买家") {
        $("[name='q[user_username_cont]']").show();
      } else if ($("#search_type").val() == "收货信息") {
        $("[name='q[receive_name_or_receive_phone_or_receive_address_cont]']").show();
      } else if ($("#search_type").val() == "Ping++流水号") {
        $("[name='q[payments_trade_no_cont]']").show();
      }
    }
</script>
