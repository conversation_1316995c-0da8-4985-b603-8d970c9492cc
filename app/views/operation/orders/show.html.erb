<div class="row">
  <div class="col-12">
    <div class="page-title-box">
      <div class="page-title-right">
        <ol class="breadcrumb m-0">
          <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
          <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
          <li class="breadcrumb-item active">订单管理</li>
        </ol>
      </div>
      <h4 class="page-title">订单详情</h4>

    </div>
  </div>
</div>
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <ul class="nav nav-tabs nav-bordered mb-3">
          <li class="nav-item">
            <a href="#bootstrap-toasts-preview" data-bs-toggle="tab"
               aria-expanded="false" class="nav-link active">
              订单信息
            </a>
          </li>
          <li class="nav-item">
            <a href="#bootstrap-toasts-code" data-bs-toggle="tab" aria-expanded="true"
               class="nav-link">
              支付单信息
            </a>
          </li>
          <li class="nav-item">
            <a href="#after_sale_orders" data-bs-toggle="tab" aria-expanded="true"
               class="nav-link">
              售后服务
            </a>
          </li>
        </ul> <!-- end nav-->
        <div class="tab-content">
          <div class="tab-pane show active" id="bootstrap-toasts-preview">
            <div class="row justify-content-center">
              <div class="col-lg-10 col-md-12 col-sm-11">
                <div class="horizontal-steps mt-4 mb-4 pb-5">
                  <div class="horizontal-steps-content">
                    <% if @order.pre_sale? %>
                      <div class="step-item <%= @order.status == 0 ? "current" : "" %>">
                        <span class="up">用户下单</span>
                        <span><%= @order.created_at.strftime("%F %T") %></span>
                      </div>
                      <div class="step-item <%= @order.status == 2000 ? "current" : "" %>">
                        <span class="up">已付定金</span>
                        <span><%= @order.payments.paid.first.pay_at.strftime("%F %T") if @order.payments.paid.count >= 1 %></span>
                      </div>
                      <div class="step-item <%= @order.status == 1 ? "current" : "" %>">
                        <span class="up">已付尾款</span>
                        <span><%= @order.payments.paid.last.pay_at.strftime("%F %T") if @order.payments.paid.count == 2 %></span>
                      </div>
                      <div class="step-item <%= @order.status == 2 ? "current" : "" %>">
                        <span class="up">发货时间</span>
                        <span><%= @order.delivery_time.strftime("%F %T") unless @order.delivery_time.blank? %></span>
                      </div>
                      <% if @order.status != 6 %>
                        <div class="step-item <%= @order.status == 200 ? "current" : "" %>">
                          <span class="up">交易成功</span>
                        </div>
                      <% else %>
                        <div class="step-item <%= @order.status == 6 ? "current" : "" %>">
                          <span class="up">交易结束</span>
                        </div>
                      <% end %>
                    <% else %>
                      <div class="step-item <%= @order.status == 0 ? "current" : "" %>">
                        <span class="up">下单时间</span>
                        <span><%= @order.created_at.strftime("%F %T") %></span>
                      </div>
                      <div class="step-item <%= @order.status == 1 ? "current" : "" %>">
                        <span class="up">付款时间</span>
                        <span><%= @order.pay_time.strftime("%F %T") unless @order.pay_time.blank? %></span>
                      </div>
                      <div class="step-item <%= @order.status == 2 ? "current" : "" %>">
                        <span class="up">发货时间</span>
                        <span><%= @order.delivery_time.strftime("%F %T") unless @order.delivery_time.blank? %></span>
                      </div>
                      <% if @order.status != 6 %>
                        <div class="step-item <%= @order.status == 200 ? "current" : "" %>">
                          <span class="up">交易成功</span>
                        </div>
                      <% else %>
                        <div class="step-item <%= @order.status == 6 ? "current" : "" %>">
                          <span class="up">交易结束</span>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                  <% rate = ((100 / 3) * @order.order_status_rate) %>
                  <div class="process-line" style="width: <%= rate > 100 ? 100 : rate%>%;"></div>
                </div>
              </div>
            </div>
            <div class="row ">
              <div class="col-lg-6 col-md-12">
                <div class="card">
                  <div class="card-body">
                    <h4 class="header-title mb-3">订单信息</h4>

                    <div class="table-responsive">
                      <table class="table mb-0">
                        <tr>
                          <td>订单编号 :</td>
                          <td><%= @order.try(:number).to_s %></td>
                        </tr>
                        <tr>
                          <th style="width: 30%">订单状态</th>
                          <th style="width: 40%"><%= Order::STATUS[@order.try(:status).to_s] %></th>
                          <% if @order.is_after_sale? || @order.status == 2000 %>
                            <% if policy(@order).refunds? %>
                              <th style="width: 30%">
                                <% if !@order.charge_number.blank? %>
                                  <a href="javascript:void(0);" id="back_money" order_id="<%= @order.id %>" number="<%= @order.charge_number %>">
                                    <% if @order.status != 2000 %>
                                      <span id="back_money_p">后台退款</span>
                                    <% else %>
                                      <span id="back_money_p">主动退款</span>
                                    <% end %>
                                  </a>

                                  <% if @order.status != 2000 %>
                                    <%= link_to "拒绝退款", cancel_refund_operation_order_path(@order), method: :post, data: { confirm: "确定拒绝退款吗？" }, style: "margin-left: 10px;" %>
                                  <% end %>
                                <% end %>
                              </th>
                            <% end %>
                          <% end %>
                        </tr>
                        <tr>
                          <td>用户ID :</td>
                          <td><%= @order.user.try(:id) %></td>
                        </tr>
                        <tr>
                          <td>用户昵称 :</td>
                          <td><%= @order.user.try(:user_name) %></td>
                        </tr>
                        <tr>
                          <td>支付类型 :</td>
                          <td><%= Order::PAYTYPE[@order.pay_type] %></td>
                        </tr>
                        <tr>
                          <td>支付金额 :</td>
                          <td>￥<%= @order.try(:amount) %></td>
                        </tr>
                        <tr>
                          <td>支付时间 :</td>
                          <td><%= @order.pay_time.strftime("%F %T") unless @order.pay_time.blank? %></td>
                        </tr>
                        <tr>
                          <td>用户备注 :</td>
                          <td><%= @order.try(:user_node) %></td>
                        </tr>
                        <tr>
                          <td>后台备注 :</td>
                          <td><%= @order.try(:admin_node) %></td>
                        </tr>
                      </table>
                    </div>
                    <!-- end table-responsive -->
                  </div>

                </div>
                <div class="card">
                  <div class="card-body">
                    <h4 class="header-title mb-3">商品详情 订单号#<%= @order.id %></h4>

                    <div class="table-responsive">
                      <table class="table mb-0">
                        <thead class="thead-light">
                        <tr>
                          <th>商品</th>
                          <th>商品规格</th>
                          <th>数量</th>
                          <th>商品单价</th>
                          <th>规格编号</th>
                          <th>总价</th>
                        </tr>
                        </thead>
                        <tbody>
                        <% details = @order.order_details %>
                        <% details.each do |detail| %>
                          <tr>
                            <td>
                              <img src="<%= detail.try(:skupictureurl) %>" alt="contact-img" title="contact-img" class="rounded me-1" height="48"/>
                              <p class="m-0 d-inline-block align-middle font-12">
                                <a href="#" class="text-body"><%= detail.try(:product_name) %></a>
                                <br/>
                                <span class="text-warning">商品ID:<%= detail.product.try(:id) || "已删除" %></span>
                              </p>
                            </td>
                            <td><%= JSON.parse(detail.try(:property_name)).join("-") %></td>
                            <td><%= detail.try(:sku_count) %></td>
                            <td>￥<%= detail.try(:skuprice) %></td>
                            <td><%= detail.try(:sku_number) %></td>
                            <td>￥<%= detail.try(:sku_count) * detail.try(:skuprice) %></td>
                          </tr>
                        <% end %>
                        </tbody>
                      </table>
                    </div>
                    <!-- end table-responsive -->
                  </div>
                </div>
              </div>
              <div class="col-lg-6 col-md-12">
                <div class="card">
                  <div class="card-body">
                    <h4 class="header-title mb-3">收件人信息</h4>
                    <h5>收件人：<%= @order.try(:receive_name) || @order.user.nick_name %></h5>
                    <h5>手机号：<%= @order.receive_phone %></h5>
                    <h5>收货地址：<%= @order.receive_address %></h5>
                  </div>
                </div>
                <div class="card">
                  <div class="card-body">
                    <h4 class="header-title mb-3">物流信息</h4>

                    <div class="text-center">
                      <i class="mdi mdi-truck-fast h2 text-muted"></i>
                      <h5><b><%= @order.logistics_com %></b></h5>
                      <p class="mb-1"><b>运单号码 :</b> <%= @order.logistics_number %></p>
                      <p class="mb-0"><b>物流跟踪 :</b></p>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>

          <div class="tab-pane" id="bootstrap-toasts-code">
            <div class="row">
              <div class="col-lg-12">
                <div class="card">
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table mb-0">
                        <thead class="thead-light">
                        <tr>
                          <th>Ping++流水号</th>
                          <th>支付金额</th>
                          <th>支付方式</th>
                          <th>支付时间</th>
                          <th>退款时间</th>
                          <th>状态</th>
                        </tr>
                        </thead>
                        <tbody>
                          <% @order.payments.each do |payment| %>
                            <tr>
                              <td><%= payment.trade_no %></td>
                              <td><%= payment.amount %></td>
                              <td><%= payment.pay_method_i18n %></td>
                              <td><%= payment.pay_at %></td>
                              <td><%= payment.refund_at %></td>
                              <td><%= payment.status_i18n %></td>
                            </tr>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                    <!-- end table-responsive -->
                  </div>
                </div>
              </div>
            </div>
          </div> <!-- end preview code-->
          <div class="tab-pane" id="after_sale_orders">
            <div class="row">
              <div class="col-lg-12">
                <div class="card">
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table mb-0">
                        <thead class="thead-light">
                        <tr>
                          <th>商品</th>
                          <th>售后类型</th>
                          <th>原因</th>
                          <th>凭证图片</th>
                        </tr>
                        </thead>
                        <tbody>
                        <% if after_sale = @order.after_sale %>
                          <tr>
                            <td>
                              <img src="<%= @order.order_details.first.try(:skupictureurl) %>" alt="contact-img" title="contact-img" class="rounded me-1" height="48"/>
                              <p class="m-0 d-inline-block align-middle font-12">
                                <a href="###" class="text-body"><%= @order.order_details.first.try(:product_name) %></a>
                                <br/>
                                <span class="text-warning">商品ID:<%= @order.order_details.first.try(:product).try(:id) || "无" %></span>
                              </p>
                            </td>
                            <td><%= after_sale.reason %></td>
                            <td><%= after_sale.detail %></td>
                            <td>
                              <% after_sale.asset_imgs.each do |asset_img| %>
                                <%= image_tag asset_img.image_url, size: "50x50", class: "img-preview", style: "margin-right: 3px" %>
                              <% end %>
                            </td>
                          </tr>
                        <% end %>
                        </tbody>
                      </table>
                    </div>
                    <!-- end table-responsive -->
                  </div>
                </div>
              </div>
            </div>
          </div> <!-- end preview code-->
        </div> <!-- end tab-content-->
      </div> <!-- end card-body-->
    </div> <!-- end card-->
  </div> <!-- end col-->
</div>
<style>
    .horizontal-steps .horizontal-steps-content .step-item span.up {
        top: calc(100% - 2.5em);
    }
</style>

<script type="text/javascript">
    $('#back_money').click(function () {
        var number = $('#back_money').attr("number");
        var order_id = $('#back_money').attr("order_id")
        backendRefundButton(number, order_id);
    });

    function backendRefundButton(number, order_id) {
        if (number == "" && order_id == '') {
            alert("此订单没有支付信息，需要联系后端管理员！");
            return false;
        }
        if (window.confirm("确定要退款吗？点击后不可撤回！")) {
            payRefund(number, order_id);
        }

    }

    function payRefund(number, order_id) {
        $.ajax({
            url: "/operation/orders/" + order_id + "/refunds",
            method: 'post',
            success: function (msg) {
                if (msg["status"] == 500) {
                    alert(msg["data"]);

                }
                if (msg["status"] == 200) {
                    alert("退款进行中，请稍后查看！");
                    window.location.reload();
                }

            }
        })
    }
</script>


