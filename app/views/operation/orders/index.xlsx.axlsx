wb = xlsx_package.workbook
wb.styles do |style|
  heading = style.add_style alignment: {horizontal: :center}, b: true, sz: 14, bg_color: "0066CC", fg_color: "FF"
  date_cell = style.add_style alignment: {horizontal: :center}, format_code: "yyyy-mm-dd HH:MM:ss", sz: 12
  data = style.add_style alignment: {horizontal: :center}, sz: 12
  wb.add_worksheet(name: "订单列表") do |sheet|
    sheet.add_row %w(订单号 商品 商品sku 商品sku唯一标识 售价(元) 货款状态 支付单号 数量 买家 交易状态
      支付方式 实收款(元) 注册号码 收件人 收件号码 收件地址 创建时间 付款时间 发货时间 退款时间 用户备注 后台备注), style: heading
    @orders_all.each do |order|
      Rails.logger.error "订单号：#{order.id}"
      order_detail = order.order_details.first
      if order.payments.where(status: [:paid, :refund]).count > 1
        order.payments.each_with_index do |payment, index|
          money_status = payment.amount.to_i == 100 ? "定金" : "尾款"
          sheet.add_row ["#{order.number}\t", order_detail.product_name, order_detail.property_name_format,
            order_detail.sku_id, order_detail.skuprice, money_status, "#{payment.trade_no}\t", order.count, order.user.user_name, order.status_cn,
            payment.pay_method_i18n, payment.amount, order.user.phone, order.receive_name,
            order.receive_phone, order.receive_address, order.created_at, payment.pay_at, order.delivery_time,
            payment.refund_at, order.user_node, order.admin_node],
            style: [data, data, data, data, data, data, data, data, data, data, data, data, data, data, data, data, date_cell, date_cell, date_cell, date_cell, data, data]
        end
      else
        payment = order.payments.first
        money_status = payment.amount.to_i == 100 ? "定金" : "全款"
        sheet.add_row ["#{order.number}\t", order_detail.product_name, order_detail.property_name_format,
          order_detail.sku_id, order_detail.skuprice, money_status, "#{payment.trade_no}\t", order.count, order.user.user_name, order.status_cn,
          payment.pay_method_i18n, payment.amount, order.user.phone, order.receive_name,
          order.receive_phone, order.receive_address, order.created_at, payment.pay_at, order.delivery_time,
          payment.refund_at, order.user_node, order.admin_node],
          style: [data, data, data, data, data, data, data, data, data, data, data, data, data, data, data, data, date_cell, date_cell, date_cell, date_cell, data, data]
      end
    end
  end
end
