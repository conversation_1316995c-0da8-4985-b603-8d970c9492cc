<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">系统管理</a></li>
            <li class="breadcrumb-item active">日志管理</li>
          </ol>
        </div>
        <h4 class="page-title">日志管理</h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <ul class="nav nav-tabs nav-bordered mb-3">
            <li class="nav-item">
              <a href="#login-logs" data-bs-toggle="tab" aria-expanded="false" class="nav-link active">
                <i class="mdi mdi-home-variant d-md-none d-block"></i>
                <span class="d-none d-md-block">登录日志</span>
              </a>
            </li>
          </ul>

          <div class="tab-content">
            <div class="tab-pane show active" id="login-logs">
              <div class="table-responsive" data-controller="log">
                <table class="table table-centered table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>用户名</th>
                      <th>登录地址</th>
                      <th>登录时间</th>
                      <% if policy(Log).forbidden_address? %>
                        <th>操作</th>
                      <% end %>
                    </tr>
                  </thead>
                  <tbody>
                    <% @login_logs.each do |login_log| %>
                      <tr>
                        <td><%= login_log.admin_user.account %></td>
                        <td>
                          <div><%= login_log.ip %></div>
                          <div><%= login_log.address %></div>
                        </td>
                        <td><%= login_log.login_at.strftime("%F %T") %></td>
                        <% if policy(Log).forbidden_address? %>
                          <td>
                            <input type="checkbox" class="forbidden_login"  <%= login_log.try(:is_forbidden) ? "" : "checked=''" %>  id="<%= 'forbidden_login' + login_log.id.to_s %>" data-switch="success">
                            <label for="<%= 'forbidden_login' + login_log.id.to_s %>" data-off-label="禁用" data-on-label="启用" style="margin-bottom: 0px;"></label>
                          </td>
                        <% end %>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
