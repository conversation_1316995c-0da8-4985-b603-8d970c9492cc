<div id="<%= id %>" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="<%= id %>Label" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="<%= id %>Label"><%= title %></h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
      </div>
      <%= form_with model: [:operation, admin_user], html: {autocomplete: "off"}, data: { turbo: false } do |form| %>
        <div class="modal-body">
          <div class="row">
            <label for="info" class="col-2 col-form-label"><span class="validate-error">*</span>用户名</label>
            <div class="col-10">
              <%= form.text_field :account, class: 'form-control', placeholder: '用户名', required: true %>
            </div>
          </div>
          <div class="row mt-2">
            <label for="info" class="col-2 col-form-label">真实姓名</label>
            <div class="col-10">
              <%= form.text_field :name, class: 'form-control', placeholder: '真实姓名' %>
            </div>
          </div>
          <div class="row mt-2">
            <% if admin_user.persisted? %>
              <label for="info" class="col-2 col-form-label">密码</label>
              <div class="col-10">
                <%= form.password_field :password, class: 'form-control', placeholder: "如不修改请置空", autocomplete: "new-password" %>
              </div>
            <% else %>
              <label for="info" class="col-2 col-form-label"><span class="validate-error">*</span>密码</label>
              <div class="col-10">
                <%= form.password_field :password, class: 'form-control', placeholder: "密码", required: true, autocomplete: "new-password" %>
              </div>
            <% end %>
          </div>
          <div class="row mt-2" data-controller="role-select" id="role-select">
            <label for="info" class="col-2 col-form-label">角色</label>
            <div class="col-10">
              <% selected_values = form.object&.manage_roles ? form.object&.manage_role_ids : [] %>
              <%= form.select :manage_role_ids, options_for_select(ManageRole.pluck(:name, :id), selected_values), {}, id: "mult_select#{form.object&.id}", class: 'select2 form-control select2-multiple', "data-toggle": 'select2', multiple: "multiple" %>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">保存</button>
        </div>
      <% end %>
    </div>
  </div>
</div>
