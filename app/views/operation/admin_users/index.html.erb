<style>
  .select2-container {
    z-index: 9999;
  }

  .select2-selection--multiple {
    height: 38px;
  }

  .select2-search__field {
    margin-top: 7px !important;
    margin-left: 15px !important;
  }

  .select2-selection__choice {
    background-color: rgba(32, 128, 240, 0.12) !important;
    color: #1677ff !important;
    border: 0px solid #1677ff !important;
    margin-top: 7px !important;
  }

  .select2-selection__choice__remove {
    border-right: 0px solid #aaa !important;
  }

  .select2-selection__choice__remove:hover {
    background-color: rgba(32, 128, 240, 0.12) !important;
    color: #1677ff !important;
    border-right: 0px solid #aaa !important;
  }

</style>

<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">系统管理</a></li>
            <li class="breadcrumb-item active">员工管理</li>
          </ol>
        </div>
        <h4 class="page-title">员工管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-2">
              <%= link_to "添加员工", "#", class: "btn btn-primary", "data-bs-toggle": "modal", "data-bs-target": "#create-modal" %>
            </div>
          </div>
          <div class="table-responsive" data-controller="manage-role" id="manage_roles">
            <table class="table table-centered table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>用户名</th>
                  <th>真实姓名</th>
                  <th>角色</th>
                  <th>最后登录地址</th>
                  <th>最后登录时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @admin_users.each do |admin_user| %>
                  <tr>
                    <td><a href="#" class="text-body fw-bold"><%= admin_user.account %></a> </td>
                    <td><%= admin_user.name %></td>
                    <td>
                      <% admin_user.manage_roles.each do |manage_role| %>
                        <n-tag :bordered="false" type="info" size="small" style="margin-right: 5px;">
                          <%= manage_role.name %>
                        </n-tag>
                      <% end %>
                    </td>
                    <% login_log = admin_user.login_logs.find_by(is_lastest: true) %>
                    <td><%= login_log&.address %></td>
                    <td><%= login_log&.login_at&.strftime("%F %T") %></td>
                    <td class="actions">
                      <%= link_to "#", class: "action-icon", "data-bs-toggle": "modal", "data-bs-target": "#edit-modal-#{admin_user.id}" do %>
                        <i class="mdi mdi-square-edit-outline" title="编辑"></i>
                      <% end %>
                      <%= link_to operation_admin_user_path(admin_user.id), method: :delete, class: "action-icon", data: {confirm: "确定删除此员工?"} do %>
                        <i class="uil-trash-alt" title="删除"></i>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
            <div style="display: flex;">
              <div>
                <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
              </div>
              <div style="flex: 1;text-align: right;">
                <% if @pagy.count <= 10 %>
                  共 <b><%= @pagy.count %></b> 项
                <% else %>
                  <%== pagy_info(@pagy) %>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<%= render "form", admin_user: AdminUser.new, title: "添加员工", id: "create-modal" %>
<% @admin_users.each do |admin_user| %>
  <div id="parent-modal-<%= admin_user.id %>">
    <%= render "form", admin_user: admin_user, title: "编辑员工", id: "edit-modal-#{admin_user.id}" %>
  </div>
<% end %>
