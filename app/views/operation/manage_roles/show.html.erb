<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">设置</a></li>
            <li class="breadcrumb-item active"><a href="/operation/manage_roles">角色权限管理</a></li>
          </ol>
        </div>
        <h4 class="page-title">角色权限管理</h4>
      </div>
    </div>
  </div>
  <div class="card" data-controller="manage-role-action">
    <div class="card-body" id="manage_role_actions">
      <input value="<%= @operation_manage_role.manage_actions.ids&.map { |x| x.to_s } %>" id="selected_actions" data-manage-role-id="<%= params[:id] %>" data-json="<%= @tmp_hash.to_json %>" style="display: none"/>
      <n-config-provider :theme-overrides="themeOverrides">
        <div class="d-flex">
          <div style="margin-top: 6px;margin-right: 10px;">
            当前角色
            <n-tag :bordered="false" type="info" size="small">
              <%= @operation_manage_role.name %>
            </n-tag>
          </div>
        </div>
        <table class="table table-hover table-bordered table-centered mt-2">
          <thead class="table-light">
            <tr>
              <th>模块</th>
              <th>全选</th>
              <th>权限点</th>
            </tr>
          </thead>
          <tbody>
            <% ManageController.order(:que).each do |manage_controller| %>
              <tr>
                <td>
                  <%= manage_controller.name %>
                </td>
                <td>
                  <n-checkbox :checked="checked_all.<%= manage_controller.word %>" :indeterminate="indeterminate.<%= manage_controller.word %>" checked-value="<%= manage_controller.word %>-true" unchecked-value="<%= manage_controller.word %>-false" label="全选" @update:checked="handleCheckedChange"></n-checkbox>
                </td>
                <td>
                  <n-checkbox-group v-model:value="cities" @update:value="handleUpdateValue">
                    <n-grid :y-gap="8" :cols="5">
                      <% manage_controller.manage_actions.order(:que).each do |manage_action| %>
                        <n-gi>
                          <n-checkbox :checked="checked" value="<%= manage_action.id %>" label="<%= manage_action.name %>" ></n-checkbox>
                        </n-gi>
                      <% end %>
                    </n-grid>
                  </n-checkbox-group>
                </td>
              </tr>
            <% end %>
            </tbody>
          </table>
      </n-config-provider>
    </div>
  </div>
</div>