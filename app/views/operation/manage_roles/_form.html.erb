<div id="<%= id %>" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="<%= id %>Label" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="<%= id %>Label"><%= title %></h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
      </div>
      <%= form_with model: [:operation, manage_role], html: {autocomplete: "off"}, data: { turbo: false } do |form| %>
        <div class="modal-body">
          <div class="row">
            <label for="info" class="col-2 col-form-label"><span class="validate-error">*</span>名称</label>
            <div class="col-10">
              <%= form.text_field :name, class: 'form-control', placeholder: '名称', required: true %>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">保存</button>
        </div>
      <% end %>
    </div>
  </div>
</div>
