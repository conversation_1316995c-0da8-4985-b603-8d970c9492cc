<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">设置</a></li>
            <li class="breadcrumb-item active">角色权限管理</li>
          </ol>
        </div>
        <h4 class="page-title">角色权限管理</h4>
      </div>
    </div>
  </div>
  <div class="card" data-controller="manage-role">
    <div class="card-body" id="manage_roles">
      <%= link_to "添加角色", "#", class: "btn btn-primary", "data-bs-toggle": "modal", "data-bs-target": "#create-modal" %>
      <table class="table table-hover table-centered mt-2">
        <thead class="table-light">
          <tr>
            <th>角色</th>
            <th>添加时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <% @operation_manage_roles.each do |operation_manage_role| %>
            <tr>
              <td>
                <n-tag :bordered="false" type="info" size="small">
                  <%= operation_manage_role.name %>
                </n-tag>
              </td>
              <td>
                <%= operation_manage_role.created_at.strftime("%F %T") %>
              </td>
              <td>
                <%= link_to [:operation, operation_manage_role], class: "action-icon", style: "position: relative;top: 3px;" do %>
                  <i class="ri-user-settings-line" title="配置权限"></i>
                <% end %>
                <% unless operation_manage_role.name == "管理员" %>
                  <%= link_to [:operation, operation_manage_role], class: "action-icon", method: :delete, data: {confirm: "确定删除吗?"} do %>
                    <i class="uil-trash-alt" title="删除"></i>
                  <% end %>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
      <div style="display: flex;">
        <div>
          <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
        </div>
        <div style="flex: 1;text-align: right;">
          <% if @pagy.count <= 10 %>
            共 <b><%= @pagy.count %></b> 项
          <% else %>
            <%== pagy_info(@pagy) %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
<%= render "form", manage_role: ManageRole.new, title: "添加角色", id: "create-modal" %>