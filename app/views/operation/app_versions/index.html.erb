<style>
  .pagination li{
      display: inline-block;
      margin: 0;
      padding: 0 4px;
      border: 1px solid #d1dbe5;
      border-right: 0;
      background: #fff;
      font-size: 13px;
      min-width: 28px;
      height: 28px;
      line-height: 28px;
      cursor: pointer;
      box-sizing: border-box;
      text-align: center;
  }
</style>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">管理</a></li>
            <li class="breadcrumb-item active">版本管理</li>
          </ol>
        </div>
        <h4 class="page-title">版本列表</h4>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-centered w-100 dt-responsive nowrap" id="products-datatable">
              <thead class="table-light">
                <tr>
                  <th>平台名称</th>
                  <th>版本号</th>
                  <th>弹窗标题</th>
                  <th>弹窗描述</th>
                  <th>更新时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @versions.each do |version| %>
                  <tr>
                    <td>
                      <%= version.ver_type %>
                    </td>
                    <td>
                      <%= version.try(:version) %>
                    </td>
                    <td>
                      <%= version.try(:title) %>
                    </td>
                    <td>
                      <%= version.node %>
                    </td>
                    <td>
                      <%= version.updated_at.strftime("%Y-%m-%d %H:%M:%S") %>
                    </td>
                    <td class="table-action">
                      <a href="<%= edit_operation_app_version_path(version.id) %>" class="action-icon"> <i class="mdi mdi-square-edit-outline"></i></a>
                    </td>
                  </tr>
                <%end%>
              </tbody>
            </table>
            <div style="display: flex;">
              <div>
                <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
              </div>
              <div style="flex: 1;text-align: right;">
                <% if @pagy.count <= 10 %>
                  共 <b><%= @pagy.count %></b> 项
                <% else %>
                  <%== pagy_info(@pagy) %>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        <!-- end card-body-->
      </div>
      <!-- end card-->
    </div>
    <!-- end col -->
  </div>
</div>
