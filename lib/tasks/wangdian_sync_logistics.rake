desc "旺店通同步物流"
task wangdian_sync_logistics: :environment do
  result = WangdianCommon.sync_logistics
  if result["status"] == 0
    p "旺店通同步物流成功"
    success_sync_ids = []
    result["data"].each do |item|
      order = Order.find_by(number: item["tid"])
      # if order.present? && order.status == 1
      if order.present?
        begin
          order.update(logistics_com: item["logistics_name"], logistics_desp: item["logistics_code"], logistics_number: item["logistics_no"], status: 2, delivery_time: Time.current)
          success_sync_ids << item["sync_id"]
        rescue => e
          Rails.logger.error("内部旺店通同步物流失败: #{e.message}")
          Rails.logger.error(item)
        end
      else
        Rails.logger.error("旺店通同步物流失败: 订单状态问题：#{order.status}")
      end
    end
    p "旺店通同步物流状态回传成功"
    p WangdianCommon.sync_logistics_status(success_sync_ids)
  else
    Rails.logger.error("外部旺店通同步物流失败: #{result}")
  end
end
