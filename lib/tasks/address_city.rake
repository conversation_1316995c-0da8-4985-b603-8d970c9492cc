namespace :iqunix do
  desc "初始化region数据"
  task address_city: :environment do
    # 先查当天的订单
    file = File.read("./public/area.json")
    regions = JSON.parse(file)
    Region.all.destroy_all
    (regions || []).each do |obj|
      name = obj["name"]
      parent = Region.create(name: name, level: 0)
      (obj["city"] || []).each do |city|
        name = city["name"]
        city_ = Region.create(name: name, level: 1, parent_id: parent.id)
        (city["area"] || []).each do |area|
          Region.create!(name: area, level: 2, parent_id: city_.id)
        end
      end
    end
  end
end
