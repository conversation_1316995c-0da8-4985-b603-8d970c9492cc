desc "更新用户头像地址"
task update_user_avatar: :environment do
  User.all.each do |user|
    if user.avatar.present?
      final_avatar_url = user.avatar.gsub("http://************:3000", Settings.oss.host)
      user.update_columns(avatar: final_avatar_url)
    end
  end

  User.all.each do |user|
    if user.avatar.present?
      final_avatar_url = user.avatar.gsub("https://iqunix.oss-cn-shenzhen.aliyuncs.com/defult_user.jpeg", "https://iqunix.oss-cn-shenzhen.aliyuncs.com/uploads/user/default_user.jpg")
      user.update_columns(avatar: final_avatar_url)
    end
  end
end
