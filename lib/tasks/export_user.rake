namespace :export do
  desc "Export all users"
  task user: :environment do
    CSV.open("#{Rails.root}/public/1+1users.csv", "wb") do |csv|
      # 订阅某件商品的用户
      # user_ids = Subscription.where(is_subscription: true, product_id: 50).pluck(:user_id).uniq
      # User.where(id: user_ids).where.not(phone: [nil, '']).each do |user|

      # 全部用户
      User.where.not(phone: [nil, '']).each do |user|
        csv << [user.phone]
      end
    end
  end
end