namespace :stats do
  desc "统计每天最大同时在线人数"
  task max_online_per_day: :environment do
    logs = ConnectionLog.where("timestamp >= ? AND timestamp <= ?", Date.yesterday.beginning_of_day, Date.yesterday.end_of_day).order(:timestamp)
    online_set = Set.new
    max_online = 0

    logs.each do |log|
      if log.status == "connected"
        online_set.add(log.device_id)
      elsif log.status == "disconnected"
        online_set.delete(log.device_id)
      end
      max_online = [max_online, online_set.size].max
    end

    puts "【#{Date.yesterday}】最大在线人数：#{max_online}"

    # 可选：存入数据库
    # DailyOnlineStat.create!(date: Date.yesterday, max_online: max_online)
  end
end