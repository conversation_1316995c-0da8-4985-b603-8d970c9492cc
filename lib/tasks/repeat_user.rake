desc '处理重复用户'
task repeat_user: :environment do
  ActiveRecord::Base.transaction do
    User.where(phone: "").update_all(phone: nil)
    User.where(openid: "").update_all(openid: nil)
    destroy_count = 0
    phone_destroy_count = 0
    origin_user_count = User.count
    puts "开始执行总用户数#{origin_user_count}个"
    repeat_oid_count = User.unscope(:order).where.not(openid: [nil, '']).select(:id,:openid)
        .group(:openid).having('count(openid) > 1').pluck(:openid).count
    User.unscope(:order).where.not(openid: [nil, '']).select(:id,:openid)
        .group(:openid).having('count(openid) > 1').pluck(:openid).each do |openid|
      user = User.where(phone: [nil, '']).where(openid: openid).first
      if user && user.orders.count.zero?
        puts "被删除的用户ID #{user.id}"
        user.destroy
        destroy_count += 1
      else
        puts "用户#{user.id}有订单，不删除"
      end
    end

    User.unscope(:order).where.not(phone: [nil, '']).select(:id,:phone).group(:phone).having('count(phone) > 1')
        .pluck(:phone).each do |phone|
      user = User.unscope(:order).order(updated_at: :desc).where(phone: phone).last
      if user && user.orders.count.zero?
        puts "手机号被删除的用户ID #{user.id}"
        user.destroy
        phone_destroy_count += 1
      else
        puts "手机号被删除的用户#{user.id}有订单，不删除"
      end
    end
    puts "开始执行时用户数 #{origin_user_count} 个"
    puts "重复id数 #{repeat_oid_count} 个"
    puts "执行完毕剩余用户数 #{User.count} 个"
    puts "删除 #{destroy_count} 个重复用户"
  end
end
