desc "更新用户名称"
task update_username: :environment do
  User.where(id: [3, 4, 5, 8, 9]).destroy_all
  # 事务
  ActiveRecord::Base.transaction do
    # 首先处理已注销用户
    User.where(status: true).each do |user|
      user.username = "#{user.user_nikename}.delete"
      user.save
    end

    ActiveRecord::Base.transaction do
      User.all.where(username: nil).each do |user|
        if user.newup_name.present?
          user.username = user.newup_name
        elsif user.wx_name.present?
          user.username = user.wx_name
        elsif user.user_nikename.present?
          user.username = user.user_nikename
        end

        user.save
      end
    end
  end
end
