namespace :db do
  desc "Backup databases to a sql file"
  task backup: :environment do
    require "aliyun/oss"
    Dir.mkdir("#{Rails.root}/db/backups") unless File.exist?("#{Rails.root}/db/backups")

    config = Rails.configuration.database_configuration
    host = config[Rails.env]["host"]
    database = config[Rails.env]["database"]
    username = config[Rails.env]["username"]

    backup_file_name = "#{database}_bak_#{Time.now.strftime("%Y%m%d_%H%M%S")}.bak"

    system "mysqldump -u #{username} -h #{host} #{database} > ./db/backups/#{backup_file_name}"
    system "tar -czvf ./db/backups/#{backup_file_name}.tar.gz ./db/backups/#{backup_file_name}"
    system "rm -f ./db/backups/#{backup_file_name}"

    p "#{backup_file_name} 备份成功"

    # 数据库备份上传aliyun_oss
    client = Aliyun::OSS::Client.new(
      endpoint: Settings.oss.endpoint,
      access_key_id: Settings.oss.access_key_id,
      access_key_secret: Settings.oss.access_key_secret
    )

    bucket = client.get_bucket("iqunixdb")
    local_file = File.open("#{Rails.root}/db/backups/#{backup_file_name}.tar.gz")
    bucket.put_object("iqunixdb/db/#{backup_file_name}.tar.gz", file: local_file, content_type: "application/gzip")
    p "#{backup_file_name}上传成功"
    system "rm -f ./db/backups/#{backup_file_name}.tar.gz"
  end
end
