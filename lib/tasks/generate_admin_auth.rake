desc '生成后台权限'
task generate_admin_auth: :environment do
  abilities = YAML.load_file("#{Rails.root}/config/admin_auth.yml")
  abilities.each_with_index do |ability, index|
    manage_controller = ManageController.find_or_create_by(word: ability.keys.first)
    manage_controller.update(name: ability.dig(ability.keys.first), que: index + 1)
    ability['children'].each_with_index do |child, action_index|
      manage_controller.manage_actions.find_or_create_by(word: child.keys.first).update(name: child.dig(child.keys.first), que: action_index + 1)
    end
  end
end
