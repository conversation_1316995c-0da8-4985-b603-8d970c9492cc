/**
 * Generate an id
 * @returns {string} id
 */
const t=function(){let t=0;return function(){return(++t).toString()}}();function createToast(n,e="blank",o={icon:{type:"",content:""},duration:"",closeable:false,theme:{type:"light",style:{background:"",color:"",stroke:""}}}){const s=t();const a=createToastItem(s,e,o);const i=createToastIcon(e,o);const r=createToastContent(n);a.appendChild(i);a.appendChild(r);o.closeable&&a.appendChild(createToastCloseButton(a));document.querySelector("wc-toast").appendChild(a);return{id:s,type:e,message:n,...o}}function createToastItem(t,n,e){const{duration:o,theme:s}=e;const a=document.createElement("wc-toast-item");const i=window?.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)");const r=i?.matches?"dark":"light";a.setAttribute("type",n);a.setAttribute("duration",o||"");a.setAttribute("data-toast-item-id",t);a.setAttribute("theme",s?.type?s.type:r);if("custom"===s?.type&&s?.style){const{background:t,stroke:n,color:e}=s.style;a.style.setProperty("--wc-toast-background",t);a.style.setProperty("--wc-toast-stroke",n);a.style.setProperty("--wc-toast-color",e)}return a}function createToastIcon(t,n){const{icon:e}=n;const o=document.createElement("wc-toast-icon");o.setAttribute("type",e?.type?e.type:t);o.setAttribute("icon",e?.content&&"custom"===e?.type?e.content:"");"svg"===e?.type&&(o.innerHTML=e?.content?e.content:"");return o}function createToastContent(t){const n=document.createElement("wc-toast-content");n.setAttribute("message",t);return n}function createToastCloseButton(t){const n=document.createElement("wc-toast-close-button");n.addEventListener("click",(()=>{t.classList.add("dismiss-with-close-button")}));return n}
/**
 * Create toast from type
 * @param {'blank' | 'success' | 'loading' | 'error' | 'custom'} type
 */function createHandler(t){
/**
   * @param {string} message
   * @param {ToastOptions} [options]
   * @returns {string}
   */
return function(n,e){const o=createToast(n,t,e);return o.id}}
/**
 * @typedef {Object} ToastOptions
 * @property {object} [icon]
 * @property {string} [icon.type]
 * @property {string} [icon.content]
 * @property {number} [duration=3500]
 * @property {object} [theme]
 * @property {'light' | 'dark' | 'custom'} [theme.type="light"]
 * @property {object} [theme.style]
 * @property {string} [theme.style.background]
 * @property {string} [theme.style.color]
 * @property {string} [theme.style.stroke]
 * @property {boolean} [closeable=false]
 */
/**
 * Create blank toast
 * @param {string} message
 * @param {ToastOptions} [options]
 * @returns {string}
 */function toast(t,n){return createHandler("blank")(t,n)}toast.loading=createHandler("loading");toast.success=createHandler("success");toast.error=createHandler("error");
/**
 * Dismiss toast by id
 * @param {string} id
 * @returns {void}
 * @example
 * const id = toast.loading('Loading...')
 * ...
 * toast.dismiss(id);
 */toast.dismiss=function(t){const n=document.querySelectorAll("wc-toast-item");for(const e of n){const n=e.getAttribute("data-toast-item-id");t===n&&e.classList.add("dismiss")}};
/**
 * Automatically add loading toast, success or error toast in promise
 * @param {Promise} promise
 * @param {object} message
 * @param {string} message.loading
 * @param {string} message.success
 * @param {string} message.error
 * @param {ToastOptions} [options]
 * @returns {Promise}
 */toast.promise=async function(t,n={loading:"",success:"",error:""},e){const o=toast.loading(n.loading,{...e});try{const s=await t;toast.dismiss(o);toast.success(n.success,{...e});return s}catch(t){toast.dismiss(o);toast.error(n.error,{...e});return t}};class WCToast extends HTMLElement{constructor(){super();this.attachShadow({mode:"open"});this.template=document.createElement("template");this.template.innerHTML=WCToast.template();this.shadowRoot.append(this.template.content.cloneNode(true))}connectedCallback(){this.setAttribute("role","status");this.setAttribute("aria-live","polite");this.position=this.getAttribute("position")||"top-center";this.arrangeToastPosition(this.position)}static get observedAttributes(){return["position"]}attributeChangedCallback(t,n,e){if("position"===t){this.position=e;this.arrangeToastPosition(this.position)}}arrangeToastPosition(t){const n=t.includes("top");const e={top:n&&0,bottom:!n&&0};const o=t.includes("center")?"center":t.includes("right")?"flex-end":"flex-start";const s=n?1:-1;const a=n?"column-reverse":"column";const i=window.getComputedStyle(document.querySelector("html"));const r=i.getPropertyValue("scrollbar-gutter");this.style.setProperty("--wc-toast-factor",s);this.style.setProperty("--wc-toast-position",o);this.style.setProperty("--wc-toast-direction",a);const c=this.shadowRoot.querySelector(".wc-toast-container");c.style.top=e.top;c.style.bottom=e.bottom;c.style.right=r.includes("stable")&&"4px";c.style.justifyContent=o}static template(){return'\n    <style>\n      :host {\n        --wc-toast-factor: 1;\n        --wc-toast-position: center;\n        --wc-toast-direction: column-reverse;\n\n        position: fixed;\n        z-index: 9999;\n        top: 16px;\n        left: 16px;\n        right: 16px;\n        bottom: 16px;\n        pointer-events: none;\n      }\n\n      .wc-toast-container {\n        z-index: 9999;\n        left: 0;\n        right: 0;\n        display: flex;\n        position: absolute;\n      }\n\n      .wc-toast-wrapper {\n        display: flex;\n        flex-direction: var(--wc-toast-direction);\n        justify-content: flex-end;\n        gap: 16px;\n        will-change: transform;\n        transition: all 230ms cubic-bezier(0.21, 1.02, 0.73, 1);\n        pointer-events: none;\n      }\n    </style>\n    <div class="wc-toast-container">\n      <div class="wc-toast-wrapper" aria-live="polite">\n        <slot> </slot>\n      </div>\n    </div>\n    '}}customElements.define("wc-toast",WCToast);class WCToastItem extends HTMLElement{constructor(){super();this.createdAt=new Date;this.EXIT_ANIMATION_DURATION=350;this.attachShadow({mode:"open"});this.template=document.createElement("template");this.template.innerHTML=WCToastItem.template();this.shadowRoot.append(this.template.content.cloneNode(true))}connectedCallback(){this.type=this.getAttribute("type")||"blank";this.theme=this.getAttribute("theme")||"light";this.duration=this.getAttribute("duration")||this.getDurationByType(this.type);if("dark"===this.theme){this.style.setProperty("--wc-toast-background","#2a2a32");this.style.setProperty("--wc-toast-stroke","#f9f9fa");this.style.setProperty("--wc-toast-color","#f9f9fa")}setTimeout((()=>{this.shadowRoot.querySelector(".wc-toast-bar").classList.add("dismiss");setTimeout((()=>{this.remove()}),this.EXIT_ANIMATION_DURATION)}),this.duration)}static get observedAttributes(){return["class"]}attributeChangedCallback(t,n,e){if("class"===t)switch(e){case"dismiss-with-close-button":this.shadowRoot.querySelector(".wc-toast-bar").classList.add("dismiss");setTimeout((()=>{this.remove()}),this.EXIT_ANIMATION_DURATION);break;case"dismiss":default:this.remove();break}}getDurationByType(t){switch(t){case"success":return 2e3;case"loading":return 6e6;case"error":case"blank":case"custom":default:return 3500}}static template(){return"\n    <style>\n      /*\n       * Author: Timo Lins\n       * License: MIT\n       * Source: https://github.com/timolins/react-hot-toast/blob/main/src/components/toast-bar.tsx\n       */\n\n      :host {\n        --wc-toast-background: #fff;\n        --wc-toast-max-width: 350px;\n        --wc-toast-stroke: #2a2a32;\n        --wc-toast-color: #000;\n        --wc-toast-font-family: 'Roboto', 'Amiri', sans-serif;\n        --wc-toast-font-size: 16px;\n        --wc-toast-border-radius: 8px;\n        --wc-toast-content-margin: 4px 10px;\n\n        display: flex;\n        justify-content: var(--wc-toast-position);\n        transition: all 230ms cubic-bezier(0.21, 1.02, 0.73, 1);\n      }\n\n      :host > * {\n        pointer-events: auto;\n      }\n\n      @media (prefers-color-scheme: dark) {\n        :host {\n          --wc-toast-background: #2a2a32;\n          --wc-toast-stroke: #f9f9fa;\n          --wc-toast-color: #f9f9fa;\n        }\n\n        :host([theme=light]) {\n          --wc-toast-background: #fff;\n          --wc-toast-stroke: #2a2a32;\n          --wc-toast-color: #000;\n        }\n      }\n\n      @keyframes enter-animation {\n        0% {\n          transform: translate3d(0, calc(var(--wc-toast-factor) * -200%), 0) scale(0.6);\n          opacity: 0.5;\n        }\n        100% {\n          transform: translate3d(0, 0, 0) scale(1);\n          opacity: 1;\n        }\n      }\n\n      @keyframes exit-animation {\n        0% {\n          transform: translate3d(0, 0, -1px) scale(1);\n          opacity: 1;\n        }\n        100% {\n          transform: translate3d(0, calc(var(--wc-toast-factor) * -150%), -1px) scale(0.6);\n          opacity: 0;\n        }\n      }\n\n      @keyframes fade-in {\n        0% {\n          opacity: 0;\n        }\n        100% {\n          opacity: 1;\n        }\n      }\n\n      @keyframes fade-out {\n        0% {\n          opacity: 1;\n        }\n        100% {\n          opacity: 0;\n        }\n      }\n\n      .wc-toast-bar {\n        display: flex;\n        align-items: center;\n        background: var(--wc-toast-background, #fff);\n        line-height: 1.3;\n        will-change: transform;\n        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n        animation: enter-animation 0.3s cubic-bezier(0.21, 1.02, 0.73, 1) forwards;\n        max-width: var(--wc-toast-max-width);\n        pointer-events: auto;\n        padding: 8px 10px;\n        border-radius: var(--wc-toast-border-radius);\n      }\n\n      .wc-toast-bar.dismiss {\n        animation: exit-animation 0.3s forwards cubic-bezier(0.06, 0.71, 0.55, 1);\n      }\n\n      @media (prefers-reduced-motion: reduce) {\n        .wc-toast-bar {\n          animation-name: fade-in;\n        }\n\n        .wc-toast-bar.dismiss {\n          animation-name: fade-out;\n        }\n      }\n    </style>\n    <div class=\"wc-toast-bar\">\n      <slot></slot>\n    </div>\n    "}}customElements.define("wc-toast-item",WCToastItem);class WCToastIcon extends HTMLElement{constructor(){super();this.attachShadow({mode:"open"});this.template=document.createElement("template");this.template.innerHTML=WCToastIcon.template();this.shadowRoot.append(this.template.content.cloneNode(true))}connectedCallback(){this.icon=this.getAttribute("icon");this.type=this.getAttribute("type")||"blank";this.setAttribute("aria-hidden","true");if("svg"!==this.type){this.icon=null!=this.icon?this.createIcon(this.type,this.icon):this.createIcon(this.type);this.shadowRoot.appendChild(this.icon)}}createIcon(t="blank",n=""){switch(t){case"success":const t=document.createElement("div");t.classList.add("checkmark-icon");return t;case"error":const e=document.createElement("div");e.classList.add("error-icon");e.innerHTML='<svg focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>';return e;case"loading":const o=document.createElement("div");o.classList.add("loading-icon");return o;case"custom":const s=document.createElement("div");s.classList.add("custom-icon");s.innerHTML=n;return s;case"blank":default:const a=document.createElement("div");return a}}static template(){return"\n    <style>\n      /*\n      * Author: Timo Lins\n      * License: MIT\n      * Source: \n      * - https://github.com/timolins/react-hot-toast/blob/main/src/components/checkmark.tsx\n      * - https://github.com/timolins/react-hot-toast/blob/main/src/components/error.tsx\n      * - https://github.com/timolins/react-hot-toast/blob/main/src/components/loader.tsx\n      */\n\n      :host {\n        display: flex;\n        align-self: flex-start;\n        margin-block: 4px !important;\n      }\n\n      @keyframes circle-animation {\n        from {\n          transform: scale(0) rotate(45deg);\n          opacity: 0;\n        }\n        to {\n          transform: scale(1) rotate(45deg);\n          opacity: 1;\n        }\n      }\n\n      .checkmark-icon {\n        width: 20px;\n        opacity: 0;\n        height: 20px;\n        border-radius: 10px;\n        background: #61d345;\n        position: relative;\n        transform: rotate(45deg);\n        animation: circle-animation 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;\n        animation-delay: 100ms;\n      }\n\n      @keyframes checkmark-animation {\n        0% {\n          height: 0;\n          width: 0;\n          opacity: 0;\n        }\n        40% {\n          height: 0;\n          width: 6px;\n          opacity: 1;\n        }\n        100% {\n          opacity: 1;\n          height: 10px;\n        }\n      }\n\n      .checkmark-icon::after {\n        content: '';\n        box-sizing: border-box;\n        animation: checkmark-animation 0.2s ease-out forwards;\n        opacity: 0;\n        animation-delay: 200ms;\n        position: absolute;\n        border-right: 2px solid;\n        border-bottom: 2px solid;\n        border-color: #fff;\n        bottom: 6px;\n        left: 6px;\n        height: 10px;\n        width: 6px;\n      }\n\n      @keyframes slide-in {\n        from {\n          transform: scale(0);\n          opacity: 0;\n        }\n        to {\n          transform: scale(1);\n          opacity: 1;\n        }\n      }\n\n      .error-icon {\n        width: 20px;\n        height: 20px;\n        border-radius: 10px;\n        background: #ff4b4b;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        animation: slide-in 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;\n      }\n\n      .error-icon svg{\n        width: 16px;\n        padding-left: 1px;\n        height: 20px;\n        stroke: #fff;\n        animation: slide-in .2s ease-out;\n        animation-delay: 100ms;\n      }\n\n      @keyframes rotate {\n        from {\n          transform: rotate(0deg);\n        }\n        to {\n          transform: rotate(360deg);\n        }\n      }\n\n      .loading-icon {\n        height: 20px;\n        width: 20px;\n        position: relative;\n        border-radius: 10px;\n        background-color: white;\n      }\n\n      .loading-icon::after {\n        content: '';\n        position: absolute;\n        bottom: 4px;\n        left: 4px;\n        width: 12px;\n        height: 12px;\n        box-sizing: border-box;\n        border: 2px solid;\n        border-radius: 100%;\n        border-color: #e0e0e0;\n        border-right-color: #616161;\n        animation: rotate 1s linear infinite;\n      }\n\n      @media (prefers-color-scheme: dark) {\n        ::slotted(svg) {\n          stroke: var(--wc-toast-stroke, #fff);\n        }\n      }\n    </style>\n    <slot name=\"svg\"></slot>\n    "}}customElements.define("wc-toast-icon",WCToastIcon);class WCToastContent extends HTMLElement{constructor(){super();this.attachShadow({mode:"open"});this.template=document.createElement("template");this.template.innerHTML=WCToastContent.template();this.shadowRoot.append(this.template.content.cloneNode(true))}connectedCallback(){this.message=this.getAttribute("message");this.shadowRoot.querySelector('slot[name="content"]').innerHTML=this.message}static template(){return'\n    <style>\n      :host {\n        display: flex;\n        justify-content: center;\n        flex: 1 1 auto;\n        margin: var(--wc-toast-content-margin) !important;\n        color: var(--wc-toast-color, #000);\n        font-family: var(--wc-toast-font-family);\n        font-size: var(--wc-toast-font-size);\n      }\n    </style>\n    <slot name="content"></slot>\n    '}}customElements.define("wc-toast-content",WCToastContent);class WCToastCloseButton extends HTMLElement{constructor(){super();this.attachShadow({mode:"open"});this.template=document.createElement("template");this.template.innerHTML=WCToastCloseButton.template();this.shadowRoot.append(this.template.content.cloneNode(true))}static template(){return'\n    <style>\n      :host {\n        width: 20px;\n        opacity: 1;\n        height: 20px;\n        border-radius: 2px;\n        border: 1px solid #dadce0;\n        background: var(--wc-toast-background);\n        position: relative;\n        cursor: pointer;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-left: 5px;\n      }\n\n      svg {\n        stroke: var(--wc-toast-stroke, #2a2a32);\n      }\n    </style>\n    <svg\n      focusable="false"\n      xmlns="http://www.w3.org/2000/svg"\n      viewBox="0 0 24 24"\n      stroke="currentColor"\n    >\n      <path\n        stroke-linecap="round"\n        stroke-linejoin="round"\n        stroke-width="2"\n        d="M6 18L18 6M6 6l12 12"\n      />\n    </svg>\n    '}}customElements.define("wc-toast-close-button",WCToastCloseButton);export{WCToast,WCToastCloseButton,WCToastContent,WCToastIcon,WCToastItem,toast};

