# README

[菠萝派自建商城接口文档](https://api.jackyun.com/wkdoc/share/doc/doc.html?id=86A230D1E4D6912230E0EEB12249D276&insideParams=bW9kdWxlSWQ9ZG9jX2NlbnRlciZwYXJhbXM9ZnJvbVNlYXJjaFR5cGUlM0RzZWFyY2glMjZkb2NJ%20ZCUzRDk4NjcxNzc0NzI2MzMwOTA2MyUyNnNwYWNlSWQlM0QxMzk5ODYzNTM3MzU2MzQzOTM2)

[接口逻辑图](https://api.jackyun.com/wkdoc/share/doc/doc.html?id=AF8DE5A7F890F53B2941A7920F23089E&insideParams=bW9kdWxlSWQ9ZG9jX2NlbnRlciZwYXJhbXM9ZnJvbVNlYXJjaFR5cGUlM0RzZWFyY2glMjZkb2NJ%20ZCUzRDk4NjcxNzc0NzI2MzMwOTA2NiUyNnNwYWNlSWQlM0Q4MzgzNDM5NzY5OTkwMjgwOTY=)


# 数据结构

rails g model manage_controllers name:string word:string
rails g model manage_actions name:string word:string manage_controller_id:integer:index
rails g model manage_roles name:string
rails g model manage_role_actions manage_role_id:integer:index manage_action_id:integer:index
rails g model manage_role_users manage_role_id:integer:index admin_user_id:integer:index
rails g model push_messages title:string include_sms_message:boolean include_jpush:boolean template_id:integer product_id:integer template_content:text msg_id:string pushed_at:datetime crowd:json expected_push_count:integer actual_push_count:integer timing:integer status:integer:index

rails g model career_presets background:string title_zh:string description_zh:text title_en:string description_en:text title_tw:string description_tw:text title_ja:string description_ja:text title_ko:string description_ko:text

rails g model keycaps name:string position:string

rails g model keycap_configs keycap_id:integer:index career_preset_id:integer:index enable_rt_mode:boolean trigger_point:float press_trigger_point:float release_trigger_point:float

rails g model keyboard_configs share_code:string keymap:json light:json performance:json pro_keys:json remark:string