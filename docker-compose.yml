version: "3.9"
services:
  web:
    container_name: iqunix
    hostname: iqunix.com
    build: .
    stdin_open: true
    tty: true
    command: bash -c "rm -rf tmp/pids/server.pid && bundle install && rails db:create && rails db:migrate && bundle exec rails s -b 0.0.0.0 -p 3000"
    volumes:
      - .:/app
    ports:
      - 3000:3000
    depends_on:
      - mysql
  mysql:
    container_name: mysql
    image: mysql:8.0.26
    restart: always
    environment:
      MYSQL_ALLOW_EMPTY_PASSWORD: yes
