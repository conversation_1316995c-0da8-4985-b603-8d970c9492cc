# == Schema Information
#
# Table name: receives
#
#  id         :bigint           not null, primary key
#  address    :string(255)
#  is_default :boolean          default(FALSE)
#  name       :string(255)
#  phone      :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  region_id  :integer
#  user_id    :integer
#
FactoryBot.define do
  factory :receive do
    name { "MyString" }
    phone { "MyString" }
    address { "MyString" }
    is_default { false }
    user { nil }
    region_id { Region.create(name: "北京市").id }
  end
end
