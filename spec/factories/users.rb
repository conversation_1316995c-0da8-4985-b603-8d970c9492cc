# == Schema Information
#
# Table name: users
#
#  id                   :bigint           not null, primary key
#  amounts_count        :float(24)        default(0.0)
#  assembles_count      :integer          default(0)
#  authentication_token :string(255)
#  avatar               :string(255)
#  avatar_file          :string(255)
#  comments_count       :integer          default(0)
#  login_time           :datetime
#  mini_program_openid  :string(255)
#  newup_name           :string(255)
#  openid               :string(255)
#  option_count         :integer          default(0)
#  orders_count         :integer          default(0)
#  phone                :string(255)
#  source_from          :integer
#  status               :boolean          default(FALSE)
#  subscriptions_count  :integer          default(0)
#  unionid              :string(255)
#  user_login_time      :integer          default(0)
#  user_nikename        :string(255)
#  user_system          :string(255)      default(""), not null
#  user_version         :string(255)      default(""), not null
#  username             :string(255)
#  wx_name              :string(255)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  register_id          :string(255)
#
# Indexes
#
#  index_users_on_openid    (openid) UNIQUE
#  index_users_on_phone     (phone) UNIQUE
#  index_users_on_username  (username) UNIQUE
#
FactoryBot.define do
  factory :user do
    phone { "133333#{rand(10000..99999)}" }
    user_nikename { "xxxxxx" }
    authentication_token { SecureRandom.urlsafe_base64(64) }
  end
end
