# == Schema Information
#
# Table name: manage_actions
#
#  id                   :bigint           not null, primary key
#  name                 :string(255)
#  que                  :integer
#  word                 :string(255)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  manage_controller_id :integer
#
# Indexes
#
#  index_manage_actions_on_manage_controller_id  (manage_controller_id)
#
FactoryBot.define do
  factory :manage_action do
    name { "MyString" }
    word { "MyString" }
    manage_controller_id { 1 }
  end
end
