# == Schema Information
#
# Table name: notifications
#
#  id           :bigint           not null, primary key
#  content      :text(65535)
#  is_read      :boolean          default(FALSE)
#  notify_type  :integer
#  target_type  :string(255)
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  send_user_id :integer
#  target_id    :integer
#  user_id      :integer
#
# Indexes
#
#  index_notifications_on_send_user_id               (send_user_id)
#  index_notifications_on_target_id_and_target_type  (target_id,target_type)
#
FactoryBot.define do
  factory :notification do
    notify_type { 1 }
    content { "xxxxxx" }
    is_read { false }
    target { create(:opinion) }
    send_user { create(:user) }
    user { create(:user) }
  end
end
