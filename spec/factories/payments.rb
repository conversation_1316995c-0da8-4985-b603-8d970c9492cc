# == Schema Information
#
# Table name: payments
#
#  id         :bigint           not null, primary key
#  amount     :float(24)
#  pay_at     :datetime
#  pay_method :integer
#  refund_at  :datetime
#  status     :integer
#  trade_no   :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  order_id   :integer
#
# Indexes
#
#  index_payments_on_order_id  (order_id)
#
FactoryBot.define do
  factory :payment do
    trade_no { "MyString" }
    amount { 1.5 }
    order_id { 1 }
    pay_method { 1 }
    pay_at { "2023-08-09 16:12:42" }
    refund_at { "2023-08-09 16:12:42" }
    status { 1 }
  end
end
