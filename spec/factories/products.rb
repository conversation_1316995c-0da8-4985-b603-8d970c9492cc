# == Schema Information
#
# Table name: products
#
#  id                  :bigint           not null, primary key
#  aftermarket         :string(255)
#  assembles_count     :integer
#  before_image        :string(255)
#  button_txt          :string(255)
#  deposit             :float(24)
#  detail              :text(65535)
#  is_comment          :boolean          default(FALSE)
#  is_open_comment     :boolean          default(FALSE)
#  is_pre_sale         :boolean
#  is_sale             :boolean          default(FALSE)
#  is_sale_at          :datetime
#  is_sale_at_button   :boolean
#  list_img_url        :string(255)
#  name                :string(255)      default(""), not null
#  on_sale             :boolean          default(FALSE)
#  on_shelf            :boolean          default(FALSE)
#  pre_sale_stage      :integer
#  qrcode              :string(255)
#  que                 :integer          default(0)
#  sale_count          :integer          default(0)
#  share_img_url       :string(255)
#  subscriptions_count :integer          default(0)
#  title               :string(255)
#  total_inventory     :integer          default(0)
#  url_link            :string(255)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  tag_id              :integer
#
FactoryBot.define do
  factory :product do
    name { "MyString" }
    list_img_url { "MyString" }
    before_image { "MyString" }
    aftermarket { "MyString" }
    detail { "MyString" }
    que { 1 }
    is_pre_sale { false }
    is_sale { true }
    pre_sale_stage { 1 }
    deposit { 100 }
    tag_id { tag.id }
  end
end
