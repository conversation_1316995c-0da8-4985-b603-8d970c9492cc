# == Schema Information
#
# Table name: opinions
#
#  id               :bigint           not null, primary key
#  asset_imgs_count :integer          default(0)
#  code             :string(255)
#  detail           :text(65535)
#  is_mark          :boolean
#  mobile           :string(255)
#  option_type      :integer
#  status           :integer          default("pending")
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  parent_id        :integer
#  user_id          :integer
#
# Indexes
#
#  index_opinions_on_parent_id  (parent_id)
#
FactoryBot.define do
  factory :opinion do
    option_type { 1 }
    detail { "xxxxxx" }
    status { 0 }
    user { nil }
    parent { nil }
  end
end
