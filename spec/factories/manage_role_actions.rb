# == Schema Information
#
# Table name: manage_role_actions
#
#  id               :bigint           not null, primary key
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  manage_action_id :integer
#  manage_role_id   :integer
#
# Indexes
#
#  index_manage_role_actions_on_manage_action_id  (manage_action_id)
#  index_manage_role_actions_on_manage_role_id    (manage_role_id)
#
FactoryBot.define do
  factory :manage_role_action do
    manage_role_id { 1 }
    manage_action_id { 1 }
  end
end
