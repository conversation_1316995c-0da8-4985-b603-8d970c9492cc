# == Schema Information
#
# Table name: skus
#
#  id              :bigint           not null, primary key
#  properties_ids  :string(255)
#  que             :integer          default(0)
#  sku_markedprice :float(24)
#  sku_name        :string(255)
#  sku_number      :string(255)
#  sku_outerid     :string(255)
#  sku_pictureurl  :string(255)
#  sku_price       :float(24)
#  sku_property    :string(255)
#  sku_quantity    :integer          default(0)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  product_id      :integer
#
FactoryBot.define do
  factory :sku do
    sku_outerid { "MyString" }
    sku_name { "MyString" }
    sku_property { "MyString" }
    sku_pictureurl { "MyString" }
    sku_markedprice { 1000 }
    sku_price { 1000 }
    sku_quantity { 10 }
    product_id { product.id }
    que { 1 }
  end
end
