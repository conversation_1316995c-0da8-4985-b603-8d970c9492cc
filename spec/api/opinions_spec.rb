# frozen_string_literal: true

require 'rails_helper'

describe API::Opinions do
  context 'opinions' do
    let_it_be(:user) { create(:user) }
    let_it_be(:another_user) { create(:user) }
    let_it_be(:opinion1) { create(:opinion, user: user, option_type: 1) }
    let_it_be(:opinion2) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion3) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion4) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion5) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion6) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion7) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion8) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion9) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion10) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion11) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion12) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:opinion13) { create(:opinion, user: user, option_type: 2) }
    let_it_be(:child_opinion2) { create(:opinion, user: another_user, option_type: 2, parent: opinion2) }

    before do
      Grape::Endpoint.before_each do |endpoint|
        allow(endpoint).to receive(:current_user).and_return(user)
      end
    end

    after do
      Grape::Endpoint.before_each nil
    end

    context 'GET /api/opinions pending' do
      it 'returns 200 ok' do
        get '/api/opinions', params: {
          page: 1,
          per: 10,
          status: 'pending'
        }
        subject = JSON.parse(response.body)

        expect(response.status).to eq(200)
        expect(subject.size).to eq(10)
        expect(subject.last.keys.size).to eq(4)
        expect(subject.last['id']).to eq(opinion4.id)
        expect(subject.last['option_type']).to eq(opinion4.option_type_i18n)
        expect(subject.last['detail']).to eq(opinion4.detail)
        expect(subject.last['created_on']).to eq(opinion4.created_at.strftime('%F'))
        expect(headers['X-Total']).to eq('13')
        expect(headers['X-Per-Page']).to eq('10')
        expect(headers['X-Page']).to eq('1')
      end
    end

    context 'GET /api/opinions completed' do
      before do
        opinion1.update(status: Opinion.statuses['completed'])
        opinion2.update(status: Opinion.statuses['completed'])
      end

      after do
        opinion1.update(status: Opinion.statuses['pending'])
        opinion2.update(status: Opinion.statuses['pending'])
      end

      it 'returns 200 ok' do
        get '/api/opinions', params: {
          page: 1,
          per: 10,
          status: 'completed'
        }
        subject = JSON.parse(response.body)

        expect(response.status).to eq(200)
        expect(subject.size).to eq(2)
        expect(subject.last.keys.size).to eq(4)
        expect(subject.last['id']).to eq(opinion1.id)
        expect(subject.last['option_type']).to eq(opinion1.option_type_i18n)
        expect(subject.last['detail']).to eq(opinion1.detail)
        expect(subject.last['created_on']).to eq(opinion1.created_at.strftime('%F'))
        expect(headers['X-Total']).to eq('2')
        expect(headers['X-Per-Page']).to eq('10')
        expect(headers['X-Page']).to eq('1')
      end
    end

    context 'GET /api/opinions/{id}' do
      it 'returns 200 ok' do
        get "/api/opinions/#{opinion2.id}"
        subject = JSON.parse(response.body)

        expect(response.status).to eq(200)
        expect(subject.keys.size).to eq(7)
        expect(subject['id']).to eq(opinion2.id)
        expect(subject['option_type']).to eq(opinion2.option_type_i18n)
        expect(subject['detail']).to eq(opinion2.detail)
        expect(subject['status']).to eq(opinion2.status_i18n)
        expect(subject['code']).to eq(opinion2.code)
        expect(subject['created_at']).to eq(opinion1.created_at.strftime('%F %T'))
        expect(subject['child_opinions'].first['created_at']).to eq(opinion1.created_at.strftime('%F %T'))
        expect(subject['child_opinions'].first['detail']).to eq(opinion1.detail)
        expect(subject['child_opinions'].first['asset_imgs']).to eq([])
        expect(subject['child_opinions'].first['user']['id']).to eq(another_user.id)
        expect(subject['child_opinions'].first['user']['avatar']).to eq(another_user.avatar)
        expect(subject['child_opinions'].first['user']['user_name']).to eq(another_user.user_name)
        expect(subject['child_opinions'].first['user']['is_official_user']).to eq(another_user.is_official_user?)
      end
    end

    context 'POST /api/opinions create' do
      it 'returns 201 created' do
        post '/api/opinions', params: {
          opinion_code: Opinion.option_types[:logistics],
          description: 'test',
          mobile: '18877777777'
        }

        expect(response.status).to eq(201)
      end
    end

    context 'POST /api/opinions reply' do
      it 'returns 201 created' do
        post '/api/opinions', params: {
          opinion_code: Opinion.option_types[:instruction],
          description: 'test',
          parent_opinion_id: opinion2.id
        }

        expect(response.status).to eq(201)
        expect(opinion2.child_opinions.count).to eq(2)
      end
    end

    context 'POST /api/opinions cancel' do
      it 'returns 201 created' do
        post '/api/opinions', params: {
          opinion_code: Opinion.option_types[:cancel_instruction],
          description: 'test',
          parent_opinion_id: opinion2.id
        }

        expect(response.status).to eq(201)
      end
    end

    context 'POST /api/opinions cancel and parent_opinion_id is nil' do
      it 'returns 400 bad request' do
        post '/api/opinions', params: {
          opinion_code: Opinion.option_types[:cancel_instruction],
          description: 'test'
        }

        expect(response.status).to eq(400)
      end
    end
  end
end
