require "rails_helper"

describe API::Opinions do
  context "notifications" do
    let_it_be(:user) { create(:user) }
    let_it_be(:send_user) { create(:user) }
    let_it_be(:notification) { create(:notification, user: user, send_user: send_user) }
    let_it_be(:notification2) { create(:notification, user: user) }
    let_it_be(:notification3) { create(:notification, user: user) }

    before do
      Grape::Endpoint.before_each do |endpoint|
        allow(endpoint).to receive(:current_user).and_return(user)
      end
    end

    after do
      Grape::Endpoint.before_each nil
    end

    context "GET /api/notifications/aboutme" do
      it "returns 200" do
        get "/api/notifications/aboutme", params: {
          page: 1,
          per: 10
        }
        subject = JSON.parse(response.body)

        expect(response.status).to eq(200)
        expect(subject.size).to eq(3)
        expect(subject.last.keys.size).to eq(9)
        expect(subject.last["id"]).to eq(notification.id)
        expect(subject.last["is_read"]).to eq(notification.is_read)
        expect(subject.last["notify_type"]).to eq("回复了你的评论")
        expect(subject.last["target_type"]).to eq(notification.target_type)
        expect(subject.last["target_id"]).to eq(notification.target_id)
        expect(subject.last["content"]).to eq(notification.content)
        expect(subject.last["created_at"]).to eq(notification.created_at.strftime("%F %T"))
        expect(subject.last["send_user"]["id"]).to eq(send_user.id)
        expect(subject.last["send_user"]["avatar"]).to eq(send_user.avatar)
        expect(subject.last["send_user"]["user_name"]).to eq(send_user.user_name)
        expect(subject.last["send_user"]["is_official_user"]).to eq(send_user.is_official_user?)
        expect(headers["X-Total"]).to eq("3")
        expect(headers["X-Per-Page"]).to eq("10")
        expect(headers["X-Page"]).to eq("1")
      end
    end

    context "DELETE /api/notifications/:id" do
      it "returns 204" do
        expect(user.notifications.count).to eq(3)
        delete "/api/notifications/#{notification.id}"
        expect(response.status).to eq(204)
        expect(response.body).to eq("")
        expect(user.notifications.count).to eq(2)
      end
    end
  end
end
