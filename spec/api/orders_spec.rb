# frozen_string_literal: true

require 'rails_helper'
require 'carrierwave/test/matchers'

describe API::Orders do
  context '常规商品下单流程' do
    let_it_be(:user) { create(:user) }
    let_it_be(:receive) { create(:receive, user: user) }
    let_it_be(:tag) { create(:tag) }
    let_it_be(:uploader) { FileUploader.new  }
    let_it_be(:uploadered) { File.open(Rails.root.to_s + "/public/apple-touch-icon.png") { |f| uploader.store!(f) }  }
    let_it_be(:property) { create(:property) }
    let_it_be(:product) { create(:product, tag_id: tag.id, list_img_url: uploader,
      before_image: uploader, aftermarket: uploader) }
    let_it_be(:sku) { create(:sku, product_id: product.id) }
    let_it_be(:sku_property) { create(:sku_property, sku_id: sku.id, property_id: property.id) }

    after do
      FileUploader.enable_processing = false
      uploader.remove!
    end

    it 'should create order and pay successful' do
      product.reload
      post '/api/react/orders', params: { token: user.authentication_token, good_id: product.id,
        sku_id: product.skus.first.id, number: 1, pay_type: 'alipay', addr_id: receive.id }
      expect(response.status).to eq(201)
      expect(Order::STATUS[Order.first.status.to_s]).to eq('待支付')

      Order.first.pay!
      expect(Order::STATUS[Order.first.status.to_s]).to eq('已支付待发货')
    end
  end

  context '预售商品下单' do
    let_it_be(:user) { create(:user) }
    let_it_be(:receive) { create(:receive, user: user) }
    let_it_be(:tag) { create(:tag) }
    let_it_be(:uploader) { FileUploader.new  }
    let_it_be(:uploadered) { File.open(Rails.root.to_s + "/public/apple-touch-icon.png") { |f| uploader.store!(f) }  }
    let_it_be(:property) { create(:property) }
    let_it_be(:another_property) { create(:property) }
    let_it_be(:pre_sale_product) { create(:product, is_pre_sale: true, tag_id: tag.id, list_img_url: uploader,
      before_image: uploader, aftermarket: uploader) }
    let_it_be(:sku) { create(:sku, product_id: pre_sale_product.id) }
    let_it_be(:sku_property) { create(:sku_property, sku_id: sku.id, property_id: property.id) }
    let_it_be(:another_sku) { create(:sku, product_id: pre_sale_product.id) }
    let_it_be(:another_sku_property) { create(:sku_property, sku_id: another_sku.id, property_id: another_property.id) }

    after do
      FileUploader.enable_processing = false
      uploader.remove!
    end

    context '商品付尾款阶段, 如果没填sku价格则无法保存' do
      before do
        Product.last.update(pre_sale_stage: 'pay_deposit', deposit: 100)
        Sku.last.update(sku_price: nil, sku_markedprice: nil)
        pre_sale_product.reload
      end

      it '如果没填sku价格则无法保存' do
        # 先支付定金
        post '/api/react/orders', params: { token: user.authentication_token, good_id: pre_sale_product.id,
          sku_id: pre_sale_product.skus.first.id, number: 1, pay_type: 'alipay', addr_id: receive.id }

        expect(response.status).to eq(201)
        deposit_order = Order.first
        expect(Order::STATUS[deposit_order.status.to_s]).to eq('待支付')

        deposit_order.pay!
        expect(Order::STATUS[deposit_order.status.to_s]).to eq('等待支付尾款')
        expect(deposit_order.amount).to eq(pre_sale_product.deposit)

        # 修改商品为付尾款阶段，再支付尾款
        expect { pre_sale_product.update!(pre_sale_stage: 'pay_final') }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end

    context '未支付定金的用户不能付尾款' do
      before do
        Product.last.update(pre_sale_stage: 'pay_deposit', deposit: 100)
        pre_sale_product.reload
      end

      it '不能支付尾款' do
        # 修改商品为付尾款阶段，再支付尾款
        pre_sale_product.update(pre_sale_stage: 'pay_final')
        pre_sale_product.reload
        post '/api/react/orders', params: { token: user.authentication_token, good_id: pre_sale_product.id,
          sku_id: pre_sale_product.skus.first.id, number: 1, pay_type: 'alipay', addr_id: receive.id }

        expect(JSON.parse(response.body)["code"]).to eq(500)
        expect(JSON.parse(response.body)["msg"]).to eq("未检测到您的定金订单！")
      end
    end

    context '已支付定金的用户可正常付尾款' do
      before do
        Product.last.update(pre_sale_stage: 'pay_deposit', deposit: 100)
        pre_sale_product.reload
      end

      it '已支付定金的用户可正常付尾款' do
        expect(pre_sale_product.reload.sale_count).to eq(0)
        expect(sku.reload.sku_quantity).to eq(10)
        # 先支付定金
        post '/api/react/orders', params: { token: user.authentication_token, good_id: pre_sale_product.id,
          sku_id: pre_sale_product.skus.first.id, number: 1, pay_type: 'alipay', addr_id: receive.id }

        expect(response.status).to eq(201)
        deposit_order = Order.first
        expect(Order::STATUS[deposit_order.status.to_s]).to eq('待支付')

        deposit_order.pay!
        expect(Order::STATUS[deposit_order.status.to_s]).to eq('等待支付尾款')
        expect(deposit_order.amount).to eq(pre_sale_product.deposit)
        expect(pre_sale_product.reload.sale_count).to eq(0)

        # 修改商品为付尾款阶段，再支付尾款
        pre_sale_product.update(pre_sale_stage: 'pay_final')
        pre_sale_product.reload
        post '/api/react/orders', params: { token: user.authentication_token, good_id: pre_sale_product.id,
          sku_id: pre_sale_product.skus.first.id, number: 1, pay_type: 'alipay', addr_id: receive.id }

        expect(response.status).to eq(201)
        final_order = Order.first
        expect(Order::STATUS[final_order.status.to_s]).to eq('待支付')
        # 尾款订单支付金额是 sku 价格减去定金
        expect(final_order.amount).to eq(final_order.order_details.first.sku.sku_price - pre_sale_product.deposit)

        final_order.pay!
        final_order = Order.last
        expect(Order.count).to eq(1)
        expect(Order::STATUS[final_order.status.to_s]).to eq('已支付待发货')
        expect(pre_sale_product.reload.sale_count).to eq(1)
        expect(sku.reload.sku_quantity).to eq(9)
      end
    end

    context '已支付定金的用户可正常付尾款' do
      before do
        Product.last.update(pre_sale_stage: 'pay_deposit', deposit: 100)
        pre_sale_product.reload
      end

      it '已支付定金的用户可正常付尾款, 且能正常合并sku、收货地址等信息' do
        expect(pre_sale_product.reload.sale_count).to eq(0)
        expect(sku.reload.sku_quantity).to eq(10)
        # 先支付定金
        post '/api/react/orders', params: { token: user.authentication_token, good_id: pre_sale_product.id,
          sku_id: sku.id, number: 1, pay_type: 'alipay', addr_id: receive.id }

        expect(response.status).to eq(201)
        deposit_order = Order.first
        expect(Order::STATUS[deposit_order.status.to_s]).to eq('待支付')

        deposit_order.pay!
        expect(Order::STATUS[deposit_order.status.to_s]).to eq('等待支付尾款')
        expect(deposit_order.amount).to eq(pre_sale_product.deposit)
        expect(pre_sale_product.reload.sale_count).to eq(0)

        # 修改商品为付尾款阶段，再支付尾款
        pre_sale_product.update(pre_sale_stage: 'pay_final')
        pre_sale_product.reload
        sku.destroy
        post '/api/react/orders', params: { token: user.authentication_token, good_id: pre_sale_product.id,
          sku_id: another_sku.id, number: 1, pay_type: 'alipay', addr_id: receive.id }

        expect(response.status).to eq(201)
        final_order = Order.first
        expect(Order::STATUS[final_order.status.to_s]).to eq('待支付')
        # 尾款订单支付金额是 sku 价格减去定金
        expect(final_order.amount).to eq(final_order.order_details.first.sku.sku_price - pre_sale_product.deposit)

        final_order.pay!
        final_order = Order.last
        expect(Order.count).to eq(1)
        expect(Order::STATUS[final_order.status.to_s]).to eq('已支付待发货')
        expect(pre_sale_product.reload.sale_count).to eq(1)
        expect(another_sku.reload.sku_quantity).to eq(9)
      end
    end
  end
end
