<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta content="width=device-width,initial-scale=1.0" name="viewport">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no;"/>
    <meta name="keywords" content="桌面生活,数据线,机械键盘,iQunix Pad,Edit,Zand,Spider,Poto,Lyrae,HIMA,MINIPOWER,PANKI,CABLE,IQKB87,IQKB62,Lambo62">
    <meta name="description" content="IQUNIX 设计并制作一系列高端桌面周边产品，选购各式 顶级外设产品，以及搭配iPhone、iPad和Mac的高品质配件。">
    <title>iQunix Design - 官方网站</title>
  	<!-- Standard -->
    <link rel="shortcut icon" href="assets/img/ficon.png">
    <link href="dist/assets/css/main.min.css" rel="stylesheet">
  
  
  <style>
    * {
      padding: 0;
      margin: 0;
    }

    body {
      overflow-y: auto;
    }
    .background-box {
      width: 100%;
      height: auto;
      background-image: linear-gradient(90deg, #dfdfdf 2px, transparent 0), linear-gradient(#dfdfdf 2px, transparent 0), linear-gradient(90deg, #dfdfdf 1px, transparent 0), linear-gradient(#dfdfdf 1px, transparent 0);
      background-size: 100px 100px, 100px 100px, 40px 40px, 40px 40px;
      background-color: #f3f3f3;
    }
    .isvwexin {
      width: 100%;
      height: auto;
      background: url(./images/index.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 20;
    }
    .tips {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 14.2rem;
      height: auto;
      z-index: 10;
      display: none;
    }

    .tips img {
      width: 100%;
      height: 100%;
    }

    .download-img  {
      width: 100%;
      height: auto;
      position: relative;
      text-align: center;
    }
    .logo-bg{
      margin: 0 auto;
      margin-top: 3%;
      width: 30%;
      height: auto;
    }
    .main-bg{
      /*margin-top: 4.5rem;*/
      width: 100%;
      height: auto;
/*      height: 57rem;
      
*/    }
    .footer-bg{
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: auto;
    }
    .btn-group {
      position: fixed;
      bottom: 20%;
      width: 100%;
      height: auto;
      z-index: 100;
    }

    .btn-group img {
      width: 60%;
      height: auto;
      display: none;
      margin:  0 auto;
    }
    .btn-img-android,
    .btn-img-ios{
      display: none;
    }
  </style>
</head>

<body>
  <div class="background-box">
    <!-- <div class="isvwexin">
      <div class="tips">
        <img src="./images/index.png" alt="">
      </div> 
    </div> -->
    <div class="download-img">
      <!-- <img src="./images/logo.png" alt="" class="logo-bg img-responsive"> -->
      <div>
        <img src="./images/index.png" alt="" class="main-bg img-responsive">
      </div>
      <!-- <img src="./images/footer.png" alt="" class="footer-bg img-responsive"> -->
    </div>
    
  </div>
  
</body>

</html>
