# == Schema Information
#
# Table name: profiles
#
#  id         :bigint           not null, primary key
#  data       :json
#  share_code :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  device_id  :integer
#
# Indexes
#
#  index_profiles_on_device_id  (device_id)
#
require "test_helper"

class ProfileTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
