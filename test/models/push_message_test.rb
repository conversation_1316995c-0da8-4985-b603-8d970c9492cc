# == Schema Information
#
# Table name: push_messages
#
#  id                  :bigint           not null, primary key
#  actual_push_count   :integer
#  crowd               :json
#  expected_push_count :integer
#  include_jpush       :boolean
#  include_sms_message :boolean
#  pushed_at           :datetime
#  status              :integer
#  template_content    :text(65535)
#  timing              :integer
#  title               :string(255)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  msg_id              :string(255)
#  product_id          :integer
#  template_id         :integer
#
# Indexes
#
#  index_push_messages_on_status  (status)
#
require "test_helper"

class PushMessageTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
