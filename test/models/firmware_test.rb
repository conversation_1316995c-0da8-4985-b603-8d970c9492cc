# == Schema Information
#
# Table name: firmwares
#
#  id             :bigint           not null, primary key
#  deploy_env     :json
#  description_en :text(65535)
#  description_ja :text(65535)
#  description_ko :text(65535)
#  description_tw :text(65535)
#  description_zh :text(65535)
#  file           :string(255)
#  force_update   :boolean
#  title_en       :string(255)
#  title_ja       :string(255)
#  title_ko       :string(255)
#  title_tw       :string(255)
#  title_zh       :string(255)
#  version        :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  device_id      :integer
#
require "test_helper"

class FirmwareTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
