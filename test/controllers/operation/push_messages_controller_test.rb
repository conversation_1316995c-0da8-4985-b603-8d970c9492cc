require 'test_helper'

class Operation::PushMessagesControllerTest < ActionController::TestCase
  setup do
    @push_message = push_messages(:one)
  end

  test "should get index" do
    get :index
    assert_response :success
    assert_not_nil assigns(:push_messages)
  end

  test "should get new" do
    get :new
    assert_response :success
  end

  test "should create push_message" do
    assert_difference('PushMessage.count') do
      post :create, push_message: { actual_push_count: @push_message.actual_push_count, crowd: @push_message.crowd, expected_push_count: @push_message.expected_push_count, include_jpush: @push_message.include_jpush, include_sms_message: @push_message.include_sms_message, msg_id: @push_message.msg_id, product_id: @push_message.product_id, pushed_at: @push_message.pushed_at, status: @push_message.status, template_content: @push_message.template_content, template_id: @push_message.template_id, timing: @push_message.timing, title: @push_message.title }
    end

    assert_redirected_to operation_push_message_path(assigns(:push_message))
  end

  test "should show push_message" do
    get :show, id: @push_message
    assert_response :success
  end

  test "should get edit" do
    get :edit, id: @push_message
    assert_response :success
  end

  test "should update push_message" do
    patch :update, id: @push_message, push_message: { actual_push_count: @push_message.actual_push_count, crowd: @push_message.crowd, expected_push_count: @push_message.expected_push_count, include_jpush: @push_message.include_jpush, include_sms_message: @push_message.include_sms_message, msg_id: @push_message.msg_id, product_id: @push_message.product_id, pushed_at: @push_message.pushed_at, status: @push_message.status, template_content: @push_message.template_content, template_id: @push_message.template_id, timing: @push_message.timing, title: @push_message.title }
    assert_redirected_to operation_push_message_path(assigns(:push_message))
  end

  test "should destroy push_message" do
    assert_difference('PushMessage.count', -1) do
      delete :destroy, id: @push_message
    end

    assert_redirected_to operation_push_messages_path
  end
end
