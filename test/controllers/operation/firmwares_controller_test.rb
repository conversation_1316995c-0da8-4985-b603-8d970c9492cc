require 'test_helper'

class Operation::FirmwaresControllerTest < ActionController::TestCase
  setup do
    @firmware = firmwares(:one)
  end

  test "should get index" do
    get :index
    assert_response :success
    assert_not_nil assigns(:firmwares)
  end

  test "should get new" do
    get :new
    assert_response :success
  end

  test "should create firmware" do
    assert_difference('Firmware.count') do
      post :create, firmware: { description_en: @firmware.description_en, description_ja: @firmware.description_ja, description_ko: @firmware.description_ko, description_tw: @firmware.description_tw, description_zh: @firmware.description_zh, file: @firmware.file, force_update: @firmware.force_update, title_en: @firmware.title_en, title_ja: @firmware.title_ja, title_ko: @firmware.title_ko, title_tw: @firmware.title_tw, title_zh: @firmware.title_zh, version: @firmware.version }
    end

    assert_redirected_to operation_firmware_path(assigns(:firmware))
  end

  test "should show firmware" do
    get :show, id: @firmware
    assert_response :success
  end

  test "should get edit" do
    get :edit, id: @firmware
    assert_response :success
  end

  test "should update firmware" do
    patch :update, id: @firmware, firmware: { description_en: @firmware.description_en, description_ja: @firmware.description_ja, description_ko: @firmware.description_ko, description_tw: @firmware.description_tw, description_zh: @firmware.description_zh, file: @firmware.file, force_update: @firmware.force_update, title_en: @firmware.title_en, title_ja: @firmware.title_ja, title_ko: @firmware.title_ko, title_tw: @firmware.title_tw, title_zh: @firmware.title_zh, version: @firmware.version }
    assert_redirected_to operation_firmware_path(assigns(:firmware))
  end

  test "should destroy firmware" do
    assert_difference('Firmware.count', -1) do
      delete :destroy, id: @firmware
    end

    assert_redirected_to operation_firmwares_path
  end
end
