require 'test_helper'

class Operation::CareerPresetsControllerTest < ActionController::TestCase
  setup do
    @career_preset = career_presets(:one)
  end

  test "should get index" do
    get :index
    assert_response :success
    assert_not_nil assigns(:career_presets)
  end

  test "should get new" do
    get :new
    assert_response :success
  end

  test "should create career_preset" do
    assert_difference('CareerPreset.count') do
      post :create, career_preset: { background: @career_preset.background, description_en: @career_preset.description_en, description_ja: @career_preset.description_ja, description_ko: @career_preset.description_ko, description_tw: @career_preset.description_tw, description_zh: @career_preset.description_zh, title_en: @career_preset.title_en, title_ja: @career_preset.title_ja, title_ko: @career_preset.title_ko, title_tw: @career_preset.title_tw, title_zh: @career_preset.title_zh }
    end

    assert_redirected_to operation_career_preset_path(assigns(:career_preset))
  end

  test "should show career_preset" do
    get :show, id: @career_preset
    assert_response :success
  end

  test "should get edit" do
    get :edit, id: @career_preset
    assert_response :success
  end

  test "should update career_preset" do
    patch :update, id: @career_preset, career_preset: { background: @career_preset.background, description_en: @career_preset.description_en, description_ja: @career_preset.description_ja, description_ko: @career_preset.description_ko, description_tw: @career_preset.description_tw, description_zh: @career_preset.description_zh, title_en: @career_preset.title_en, title_ja: @career_preset.title_ja, title_ko: @career_preset.title_ko, title_tw: @career_preset.title_tw, title_zh: @career_preset.title_zh }
    assert_redirected_to operation_career_preset_path(assigns(:career_preset))
  end

  test "should destroy career_preset" do
    assert_difference('CareerPreset.count', -1) do
      delete :destroy, id: @career_preset
    end

    assert_redirected_to operation_career_presets_path
  end
end
