# == Schema Information
#
# Table name: profiles
#
#  id         :bigint           not null, primary key
#  data       :json
#  share_code :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  device_id  :integer
#
# Indexes
#
#  index_profiles_on_device_id  (device_id)
#
FactoryBot.define do
  factory :profile do
    share_code { "MyString" }
    device_id { 1 }
    data { "" }
  end
end
