# == Schema Information
#
# Table name: push_messages
#
#  id                  :bigint           not null, primary key
#  actual_push_count   :integer
#  crowd               :json
#  expected_push_count :integer
#  include_jpush       :boolean
#  include_sms_message :boolean
#  pushed_at           :datetime
#  status              :integer
#  template_content    :text(65535)
#  timing              :integer
#  title               :string(255)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  msg_id              :string(255)
#  product_id          :integer
#  template_id         :integer
#
# Indexes
#
#  index_push_messages_on_status  (status)
#
FactoryBot.define do
  factory :push_message do
    title { "MyString" }
    include_sms_message { false }
    include_jpush { false }
    template_id { 1 }
    product_id { 1 }
    template_content { "MyText" }
    msg_id { "MyString" }
    pushed_at { "2023-11-20 17:01:54" }
    crowd { "" }
    expected_push_count { 1 }
    actual_push_count { 1 }
    timing { 1 }
    status { 1 }
  end
end
