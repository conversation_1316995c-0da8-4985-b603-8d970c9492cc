source 'https://gems.ruby-china.com'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem 'rails', '~> 7.0.4', '>= *******'

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem 'sprockets-rails'

# Use mysql as the database for Active Record
gem 'mysql2', '~> 0.5'

# Use the Puma web server [https://github.com/puma/puma]
gem 'puma', '~> 3.7'

# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem 'importmap-rails'

# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem 'turbo-rails'

# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem 'stimulus-rails'

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem 'jbuilder'

# Use Redis adapter to run Action Cable in production
# gem "redis", "~> 4.0"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

gem 'devise'
gem 'jquery-ui-rails'
gem 'jquery-rails'
gem 'sassc-rails'
gem 'laydate-rails'

# 翻页
gem 'kaminari'
gem 'pagy', '~> 6.0', '>= 6.0.3'

# 搜索
gem 'ransack'

# 排序
gem 'acts_as_list'

# 图片上传
gem 'carrierwave', '~> 1.0'
gem 'carrierwave-aliyun'
gem 'mini_magick'
gem 'cocoon'

# 配置文件
gem 'config'

# api
gem 'grape'
gem 'grape-swagger'
gem 'grape-entity'
gem 'grape-swagger-entity'
gem 'grape-swagger-rails'
gem 'grape_logging'
gem 'hashie-forbidden_attributes'
gem 'api-pagination'


# excel
gem 'roo', '~> 2.7', '>= 2.7.1'
gem 'spreadsheet', '~> 1.1', '>= 1.1.4'
gem 'caxlsx_rails'
# gem 'jpush', git: 'https://github.com/jpush/jpush-api-ruby-client.git'
gem 'jpush', '~> 4.0', '>= 4.0.11'
gem 'faraday', '~> 2.7', '>= 2.7.10'
gem 'httparty'
gem 'rexml', '~> 3.2', '>= 3.2.6'
gem 'rest-client', '~> 2.0', '>= 2.0.2'
gem 'pingpp'
# 用ruby的service
gem 'simple_command'

# 工具包
gem 'enum_help'

# 队列
gem 'sidekiq'
gem 'sidekiq-failures'

# Audited (previously acts_as_audited) is an ORM extension that logs all changes to your models. Audited can also record who made those changes, save comments and associate models related to the changes.
gem 'audited'

# Pundit provides a set of helpers which guide you in leveraging regular Ruby classes and object oriented design patterns to build a simple, robust and scalable authorization system.
gem 'pundit'

gem 'whenever'

gem 'aliyun-cloud_sms'

gem 'geocoder'
gem 'rack-cors'
# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', require: false

gem 'mqtt'
gem 'redis'
# Use Sass to process CSS
# gem "sassc-rails"

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem 'debug', platforms: %i[mri mingw x64_mingw]
  gem 'factory_bot_rails'
  gem 'standard', '~> 1.28'
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem 'annotate'
  gem 'sorbet', '~> 0.5.11139'

  gem 'capistrano3-puma'
  gem 'capistrano-rails'
  gem 'capistrano-sidekiq', '~> 2.3', '>= 2.3.1'
  gem 'grape_on_rails_routes'
  gem 'rails-admin-scaffold'
  gem 'web-console'

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem 'capybara'
  gem 'selenium-webdriver'
  gem 'webdrivers'
  gem 'rspec-rails'
  gem 'test-prof'
end
